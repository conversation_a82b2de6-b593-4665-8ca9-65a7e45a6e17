// src/components/RosterTable/RosterRow.js
import { memo } from 'react';
import { isNewEmployee } from '../../utils/rosterUtils';

/**
 * RosterRow component - Renders a single row in the roster table
 *
 * @param {Object} props - Component props
 * @param {Object} props.tech - The technician/employee data
 * @param {number} props.idx - The index of the row
 * @param {Object} props.selection - The rectangular selection coordinates
 * @param {boolean} props.dragging - Whether dragging is in progress
 * @param {string} props.mode - The current mode ('update', 'drag', or 'dialog')
 * @param {Function} props.handleCellClick - Function to handle cell click
 * @param {Function} props.handleCellMouseDown - Function to handle mouse down for selection
 * @param {Function} props.handleCellMouseEnter - Function to handle mouse enter for selection
 * @param {Function} props.handleDragStart - Function to handle drag start
 * @param {Function} props.handleDragOver - Function to handle drag over
 * @param {Function} props.handleDrop - Function to handle drop
 * @param {Function} props.handleShiftChange - Function to handle shift change
 * @param {Object} props.dropdownPosition - The position of the dropdown
 * @param {Function} props.setDropdownPosition - Function to set dropdown position
 * @param {Array} props.shiftTypes - The available shift types
 * @param {Object} props.shiftColors - The colors for each shift type
 * @param {Array} props.changedCells - The cells that have been changed
 * @param {number} props.daysInMonth - The number of days in the month
 * @param {number} props.minRow - Min row index for rectangular selection
 * @param {number} props.maxRow - Max row index for rectangular selection
 * @param {number} props.minCol - Min column for rectangular selection
 * @param {number} props.maxCol - Max column for rectangular selection
 * @param {Array} props.techIds - Array of all tech IDs
 * @param {Array} props.multiSelection - Array of individually selected cells
 * @param {Object} props.dropTarget - The current drop target
 * @param {Object} props.lastSelected - The last selected cell
 * @returns {JSX.Element} RosterRow component
 */
const RosterRow = memo(({
  tech,
  idx,
  selection,
  dragging,
  mode,
  handleCellClick,
  handleCellMouseDown,
  handleCellMouseEnter,
  handleDragStart,
  handleDragOver,
  handleDrop,
  handleShiftChange,
  dropdownPosition,
  setDropdownPosition,
  shiftTypes,
  shiftColors,
  changedCells,
  daysInMonth,
  minRow,
  maxRow,
  minCol,
  maxCol,
  techIds,
  multiSelection = [],
  dropTarget = { techId: null, day: null },
  lastSelected = { techId: null, day: null },
  // Employee reorder props
  handleEmployeeReorder = () => {}
}) => {
  const isNew = isNewEmployee(tech);
  const isRowDragged = false; // Will implement visual drag state later
  const isRowDropTarget = false; // Will implement visual drop target later

  return (
    <tr
      key={tech.employeeId}
      style={{
        backgroundColor: isRowDropTarget
          ? '#dbeafe'
          : isRowDragged
            ? '#fef3c7'
            : idx % 2 === 0 ? '#f9fafb' : 'white',
        opacity: isRowDragged ? 0.7 : 1,
        transition: 'background-color 0.2s, opacity 0.2s'
      }}
      draggable={mode === 'employeeReorder'}
      onDragStart={mode === 'employeeReorder' ? (e) => {
        // Simple drag start for employee reordering
        e.dataTransfer.setData('text/plain', JSON.stringify({
          type: 'employeeReorder',
          employeeId: tech.employeeId,
          employeeName: tech['Name'],
          currentRowIndex: tech.rowIndex
        }));
        console.log('Started dragging employee:', tech['Name']);
      } : undefined}
      onDragOver={mode === 'employeeReorder' ? (e) => {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
      } : undefined}
      onDrop={mode === 'employeeReorder' ? (e) => {
        e.preventDefault();
        try {
          const dragData = JSON.parse(e.dataTransfer.getData('text/plain'));
          if (dragData.type === 'employeeReorder') {
            console.log('Dropped employee reorder:', dragData, 'on:', tech['Name']);
            // Call the handler to show preview modal
            handleEmployeeReorder(dragData, tech);
          }
        } catch (err) {
          console.error('Error handling employee reorder drop:', err);
        }
      } : undefined}
      onDragEnd={mode === 'employeeReorder' ? () => {
        console.log('Drag ended');
      } : undefined}
    >
      <td
        draggable={false}
        style={{
          border: isRowDropTarget ? '2px solid #3b82f6' : '1px solid #d1d5db',
          padding: '0.5rem 1rem',
          width: 'auto',
          minWidth: '150px',
          whiteSpace: 'nowrap',
          height: '40px',
          textAlign: 'left',
          cursor: mode === 'employeeReorder' ? 'grab' : 'default'
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center' }}>
          {mode === 'employeeReorder' && (
            <span
              style={{
                marginRight: '0.5rem',
                color: '#6b7280',
                cursor: 'grab',
                fontSize: '14px',
                userSelect: 'none'
              }}
              title="Drag to reorder employee"
            >
              ↕️
            </span>
          )}
          {tech['Name']}
          {isNew && (
            <span
              title="New employee: No shifts scheduled yet"
              style={{ marginLeft: '0.5rem', color: '#f59e0b', cursor: 'help' }}
            >
              ⚠️
            </span>
          )}
        </div>
      </td>
      <td draggable={false} style={{ border: '1px solid #d1d5db', padding: '0.5rem 1rem', fontWeight: '500', width: '120px', minWidth: '120px', height: '40px' }}>{tech['Staff/SRC#']}</td>
      <td draggable={false} style={{ border: '1px solid #d1d5db', padding: '0.5rem 1rem', width: '80px', minWidth: '80px', height: '40px' }}>{tech['Position']}</td>
      {Array.from({ length: daysInMonth }, (_, i) => {
        const day = (i + 1).toString();
        const dayNum = parseInt(day);

        // Check if this cell is within the rectangular selection
        let isSelectedRectangular = false;
        if (selection.start.techId && selection.end.techId) {
          const rowIdx = techIds.indexOf(tech.employeeId);
          if (rowIdx >= minRow && rowIdx <= maxRow && dayNum >= minCol && dayNum <= maxCol) {
            isSelectedRectangular = true;
          }
        }

        // Check if this cell is in the multi-selection
        const isMultiSelected = multiSelection.some(cell =>
          cell.techId === tech.employeeId && cell.day === dayNum
        );

        // Combine all selection systems
        const isSelected = isSelectedRectangular || isMultiSelected;

        const shift = tech[day]; // No default value
        const isMissing = shift === undefined && !isNew;
        const isDropdownOpen = dropdownPosition.techId === tech.employeeId && dropdownPosition.day === day;
        const isChanged = changedCells.some(cell => cell.techId === tech.employeeId && cell.day === day);

        // Check if this cell is the current drop target
        const isDropTarget =dropTarget&& dropTarget.techId === tech.employeeId && dropTarget.day === dayNum;

        // Check if this is the last selected cell
        const isLastSelected = lastSelected.techId === tech.employeeId && parseInt(lastSelected.day) === dayNum;

        return (
          <td
            key={i}
            data-tech-id={tech.employeeId}
            data-day={day}
            draggable={mode === 'drag' && isSelected}
            onMouseDown={mode === 'drag' ? (e) => handleCellMouseDown(tech.employeeId, day, e) : undefined}
            onMouseEnter={mode === 'drag' ? () => handleCellMouseEnter(tech.employeeId, day) : undefined}
            onClick={mode === 'update' || mode === 'dialog' ? () => handleCellClick(tech.employeeId, day) : undefined}
            onDragStart={(e) => handleDragStart(e, tech.employeeId, dayNum, isSelected, isMultiSelected)}
            onDragOver={(e) => handleDragOver(e, tech.employeeId, dayNum)}
            onDrop={(e) => handleDrop(e, tech.employeeId, day)}
            style={{
              border: isDropTarget
                ? '2.5px solid #0074D9'
                : isLastSelected
                  ? '2.5px solid #2563eb'
                  : isMultiSelected
                    ? '2px solid #3b82f6'
                    : isSelectedRectangular
                      ? '2px solid #f87171'
                      : '1px solid #d1d5db',
              boxShadow: isDropTarget ? '0 0 6px 2px #0074D9' : undefined,
              transition: 'border 0.18s, background 0.18s, box-shadow 0.18s',
              padding: '0.25rem 0.5rem',
              textAlign: 'center',
              cursor: mode === 'drag' && isSelected ? 'grab' : 'pointer',
              background: isDropTarget
                ? '#e0f2fe'
                : isChanged && isSelected
                  ? 'linear-gradient(90deg, #ff9500 0%, #ff9500 50%, #bde0fe 50%, #bde0fe 100%)'
                  : isChanged
                    ? '#ff9500'
                    : isMultiSelected
                      ? '#bde0fe'
                      : isSelectedRectangular
                        ? '#ffcccb'
                        : isNew
                          ? '#f3f4f6'
                          : isMissing
                            ? '#fee2e2'
                            : (shiftColors[shift] || 'transparent'),
              opacity: dragging && !isSelected ? 0.7 : 1,
              width: '40px',
              minWidth: '40px',
              height: '40px',
              position: 'relative',
              zIndex: isDropTarget ? 10 : undefined,
            }}
            title={isSelected ? 'Selected for drag (Ctrl+click to add cells, Shift+click for rectangular selection)' : ''}
          >
            {isNew ? (
              <span title="No shifts scheduled" style={{ color: '#6b7280' }}>
                --
              </span>
            ) : isMissing ? (
              <span title="Shift data missing for this day" style={{ color: '#b91c1c' }}>
                ?
              </span>
            ) : (
              shift || ''
            )}
            {isDropdownOpen && (
              <div
                style={{
                  position: 'absolute',
                  top: '100%',
                  left: 0,
                  zIndex: '10',
                  backgroundColor: 'white',
                  border: '1px solid #d1d5db',
                  borderRadius: '0.25rem',
                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                  maxHeight: '150px',
                  overflowY: 'auto',
                }}
              >
                {shiftTypes.map((shiftOption) => (
                  <div
                    key={shiftOption}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleShiftChange(tech.employeeId, day, shiftOption);
                    }}
                    style={{
                      padding: '0.25rem 0.5rem',
                      cursor: 'pointer',
                      backgroundColor: shiftOption === shift ? '#e5e7eb' : 'white',
                      ':hover': { backgroundColor: '#f3f4f6' },
                    }}
                  >
                    {shiftOption}
                  </div>
                ))}
              </div>
            )}
          </td>
        );
      })}
      <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', height: '40px' }}>{tech['Leave days'] || 0}</td>
      <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', height: '40px' }}>{tech['Sick days'] || 0}</td>
      <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', height: '40px' }}>{tech['Work days'] || 0}</td>
      <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', height: '40px' }}>{tech['Work Time'] || '0:00'}</td>
      <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', height: '40px' }}>{tech['Overtime days'] || 0}</td>
      <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', height: '40px' }}>{tech['Overtime'] || '0:00'}</td>
      <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', height: '40px' }}>{tech['Aircraft'] || '0:00'}</td>
      <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', height: '40px' }}>{tech['Office'] || '0:00'}</td>
    </tr>
  );
});

export default RosterRow;
