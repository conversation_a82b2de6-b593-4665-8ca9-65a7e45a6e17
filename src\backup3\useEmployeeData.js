//src/useEmployeeData.js
import { useState, useEffect, useRef } from 'react';
import { getDatabase, ref, get, set, remove, update, onValue } from 'firebase/database';

const useEmployeeData = () => {
  const [employees, setEmployees] = useState([]);
  const [contactInfo, setContactInfo] = useState({});
  const [shifts, setShifts] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const rosterPathsCache = useRef(null);

  useEffect(() => {
    const db = getDatabase();

    const contactInfoRef = ref(db, 'contactInfo');
    const unsubscribeContactInfo = onValue(
      contactInfoRef,
      (snapshot) => {
        setContactInfo(snapshot.exists() ? snapshot.val() : {});
      },
      (err) => {
        setError(err.message);
        console.error('Error listening to contact info:', err);
      }
    );

    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        const [employeesSnapshot, shiftsSnapshot] = await Promise.all([
          get(ref(db, 'employees')),
          get(ref(db, 'shifts')),
        ]);

        let employeeData = employeesSnapshot.exists()
          ? Object.values(employeesSnapshot.val())
          : [];

        // Sort by rowIndex to maintain correct order
        employeeData = employeeData.sort((a, b) => a.rowIndex - b.rowIndex);
        setEmployees(employeeData);
        setShifts(shiftsSnapshot.exists() ? shiftsSnapshot.val() : {});
      } catch (err) {
        setError(err.message);
        console.error('Error fetching employee data:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();

    return () => {
      unsubscribeContactInfo();
    };
  }, []);

  const getAllRosterPaths = async () => {
    if (rosterPathsCache.current) return rosterPathsCache.current;

    const db = getDatabase();
    const snapshot = await get(ref(db, 'rostersDraft'));
    if (!snapshot.exists()) return [];

    const rostersDraft = snapshot.val();
    const allPaths = [];
    Object.entries(rostersDraft).forEach(([year, months]) => {
      Object.keys(months).forEach((month) => {
        allPaths.push(`${year}/${month}`);
      });
    });

    rosterPathsCache.current = allPaths;
    return allPaths;
  };

  const getFutureRosterPaths = async () => {
    const allPaths = await getAllRosterPaths();
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1;

    return allPaths.filter((path) => {
      const [year, month] = path.split('/');
      const rosterYear = parseInt(year);
      const rosterMonth = parseInt(month);

      return (
        rosterYear > currentYear ||
        (rosterYear === currentYear && rosterMonth >= currentMonth)
      );
    });
  };

  const adjustRowIndices = async (employeeId, newRowIndex) => {
    const db = getDatabase();
    const employeesSnapshot = await get(ref(db, 'employees'));
    if (!employeesSnapshot.exists()) return;

    const allEmployees = Object.values(employeesSnapshot.val());
    const currentEmployee = allEmployees.find(emp => emp.employeeId === employeeId);
    if (!currentEmployee) return;

    const oldRowIndex = currentEmployee.rowIndex;
    if (oldRowIndex === newRowIndex) return;

    // Create a sorted copy of employees by rowIndex
    const sortedEmployees = [...allEmployees].sort((a, b) => a.rowIndex - b.rowIndex);

    // Determine affected employees and their new row indices
    const affectedEmployees = [];
    const updates = {};

    if (newRowIndex < oldRowIndex) {
      // Moving up (e.g., from 15 to 10)
      for (let i = newRowIndex; i < oldRowIndex; i++) {
        const emp = sortedEmployees.find(e => e.rowIndex === i);
        if (emp && emp.employeeId !== employeeId) {
          affectedEmployees.push({ ...emp, newRowIndex: i + 1 });
        }
      }
    } else {
      // Moving down (e.g., from 10 to 15)
      for (let i = oldRowIndex + 1; i <= newRowIndex; i++) {
        const emp = sortedEmployees.find(e => e.rowIndex === i);
        if (emp && emp.employeeId !== employeeId) {
          affectedEmployees.push({ ...emp, newRowIndex: i - 1 });
        }
      }
    }

    // Prepare updates for all affected employees
    affectedEmployees.forEach(emp => {
      updates[`employees/${emp.employeeId}/rowIndex`] = emp.newRowIndex;
    });
    updates[`employees/${employeeId}/rowIndex`] = newRowIndex;

    // Apply updates to Firebase
    await update(ref(db), updates);

    // Update rosters for affected employees
    const futurePaths = await getFutureRosterPaths();
    const rosterUpdates = {};

    // Add the main employee being moved
    affectedEmployees.push({
      employeeId,
      newRowIndex
    });

    for (const emp of affectedEmployees) {
      for (const path of futurePaths) {
        const rosterRef = `rostersDraft/${path}/employeeShifts/${emp.employeeId}`;
        const snapshot = await get(ref(db, rosterRef));
        if (snapshot.exists()) {
          rosterUpdates[rosterRef] = {
            ...snapshot.val(),
            rowIndex: emp.newRowIndex
          };
        }
      }
    }

    if (Object.keys(rosterUpdates).length > 0) {
      await update(ref(db), rosterUpdates);
    }

    // Update local state
    setEmployees(prev => {
      const updated = prev.map(emp => {
        const affected = affectedEmployees.find(a => a.employeeId === emp.employeeId);
        return affected ? { ...emp, rowIndex: affected.newRowIndex } : emp;
      });
      return updated.sort((a, b) => a.rowIndex - b.rowIndex);
    });
  };

  const addEmployee = async (employeeData) => {
    try {
      const db = getDatabase();
      const newRowIndex = employees.length + 1;
      const newEmployee = { ...employeeData, rowIndex: newRowIndex };

      await set(ref(db, `employees/${employeeData.employeeId}`), newEmployee);

      setEmployees(prev => {
        const updated = [...prev, newEmployee].sort((a, b) => a.rowIndex - b.rowIndex);
        return updated;
      });
    } catch (err) {
      console.error('Error adding employee:', err);
      throw new Error('Failed to add employee: ' + err.message);
    }
  };

  const updateEmployee = async (employeeId, updatedData) => {
    try {
      const db = getDatabase();

      // 1.  Get current employee data.
      const employeeRef = ref(db, `employees/${employeeId}`);
      const employeeSnapshot = await get(employeeRef);

      if (!employeeSnapshot.exists()) {
        console.warn(`Employee with ID ${employeeId} not found.`);
        return;
      }
      const currentEmployee = employeeSnapshot.val();
      const oldRowIndex = currentEmployee.rowIndex;


      // 2. Handle rowIndex change.
      if (updatedData.rowIndex !== undefined && updatedData.rowIndex !== oldRowIndex) {
        // Check if the employee has entries in future rosters before adjusting row indices
        const hasRosterEntries = await checkFutureRosterEntries(employeeId);
        if (hasRosterEntries) {
          await adjustRowIndices(employeeId, updatedData.rowIndex);
        } else {
          console.warn(`Employee ${employeeId} has no future roster entries.  Skipping rowIndex update.`);
          // Remove rowIndex from updatedData to prevent it from being updated below.
          delete updatedData.rowIndex;
        }
      }

      // 3. Update the employee data.  Use the employeeRef we already have.
      await update(employeeRef, updatedData);


      // 4. Update the rosters.
      await updateEmployeeAndRoster(employeeId, updatedData);

      // 5. Update local state.
      setEmployees(prevEmployees => {
        return prevEmployees.map(employee => {
          if (employee.employeeId === employeeId) {
            return { ...employee, ...updatedData }; // Merge, don't replace
          }
          return employee;
        }).sort((a, b) => a.rowIndex - b.rowIndex);
      });

    } catch (error) {
      console.error('Error updating employee:', error);
      throw new Error('Failed to update employee: ' + error.message);
    }
  };

  /**
   * Updates employee data and relevant fields in future roster entries.
   *
   * @param {string} employeeId - The ID of the employee to update.
   * @param {object} updatedData - An object containing the fields and values to update.
   * @returns {Promise<void>} - A Promise that resolves when the update is complete, or rejects on error.
   */
  const updateEmployeeAndRoster = async (employeeId, updatedData) => {
    try {
      const db = getDatabase();
      const employeeRef = ref(db, `employees/${employeeId}`);
      const employeeSnapshot = await get(employeeRef);

      if (!employeeSnapshot.exists()) {
        console.warn(`Employee with ID ${employeeId} not found.`);
        return;
      }

      const currentEmployee = employeeSnapshot.val();

      // Get paths to future rosters
      const rosterPaths = await getFutureRosterPaths(); // Ensure this function is correctly implemented
      if (rosterPaths.length === 0) {
        console.warn("No future rosters found to update.");
        return;
      }

      const employeeUpdates = {};
      const rosterUpdates = {};
      let hasRosterUpdates = false; // Track if any roster updates were actually made

      // 1. Update the employee's data.  Only update fields present in the employee object.
      for (const key in updatedData) {
        if (currentEmployee.hasOwnProperty(key)) {
          employeeUpdates[key] = updatedData[key];
        }
      }
      if (Object.keys(employeeUpdates).length > 0) {
        await update(employeeRef, employeeUpdates);
        console.log(`Updated employee ${employeeId} with:`, employeeUpdates);
      }


      // 2. Update relevant fields in future rosters.
      for (const path of rosterPaths) {
        const rosterRef = ref(db, `rostersDraft/${path}/employeeShifts/${employeeId}`);
        const rosterSnapshot = await get(rosterRef);

        if (rosterSnapshot.exists()) {
          const rosterEntry = rosterSnapshot.val();
          const rosterUpdateFields = {};

          // Iterate through the updated data to find fields that exist in the *roster entry*
          for (const key in updatedData) {
            if (rosterEntry.hasOwnProperty(key)) {
              // Important:  Only update if the new data is different from the *roster* data.
              if (updatedData[key] !== rosterEntry[key]) {
                rosterUpdateFields[key] = updatedData[key];
              }
            }
          }
          if (Object.keys(rosterUpdateFields).length > 0) {
            rosterUpdates[`${path}/employeeShifts/${employeeId}`] = {
              ...rosterEntry,
              ...rosterUpdateFields,
            };
            hasRosterUpdates = true; // Set the flag
            console.log(`Updated roster at ${path} for employee ${employeeId} with:`, rosterUpdateFields);
          }
        } else {
          console.warn(`Employee ${employeeId} not found in roster at ${path}`); //keep going
        }
      }

      if (hasRosterUpdates) {
        await update(ref(db, 'rostersDraft'), rosterUpdates);
      } else {
        console.warn("No roster updates were needed.");
      }

      console.log("Employee and roster update process completed.");

    } catch (error) {
      console.error("Error updating employee and roster:", error);
      throw new Error("Failed to update employee and roster: " + error.message); // Re-throw for caller to handle
    }
  };

  const deleteEmployee = async (employeeId) => {
    try {
      const db = getDatabase();
      const employeeSnapshot = await get(ref(db, `employees/${employeeId}`));
      if (!employeeSnapshot.exists()) return;

      const deletedRowIndex = employeeSnapshot.val().rowIndex;

      // First delete the employee
      await remove(ref(db, `employees/${employeeId}`));
      await removeEmployeeFromRosters(employeeId);

      // Get all remaining employees
      const snapshot = await get(ref(db, 'employees'));
      if (!snapshot.exists()) {
        setEmployees([]);
        return;
      }

      const remainingEmployees = Object.values(snapshot.val())
        .filter(emp => emp.employeeId !== employeeId)
        .sort((a, b) => a.rowIndex - b.rowIndex);

      // Adjust row indices for employees below the deleted one
      const updates = {};
      const affectedEmployees = remainingEmployees
        .filter(emp => emp.rowIndex > deletedRowIndex)
        .map(emp => ({
          ...emp,
          newRowIndex: emp.rowIndex - 1
        }));

      affectedEmployees.forEach(emp => {
        updates[`employees/${emp.employeeId}/rowIndex`] = emp.newRowIndex;
      });

      if (Object.keys(updates).length > 0) {
        await update(ref(db), updates);
      }

      // Update rosters for affected employees
      const futurePaths = await getFutureRosterPaths();
      const rosterUpdates = {};

      for (const emp of affectedEmployees) {
        for (const path of futurePaths) {
          const rosterRef = `rostersDraft/${path}/employeeShifts/${emp.employeeId}`;
          const rosterSnapshot = await get(ref(db, rosterRef));
          if (rosterSnapshot.exists()) {
            rosterUpdates[rosterRef] = {
              ...rosterSnapshot.val(),
              rowIndex: emp.newRowIndex
            };
          }
        }
      }

      if (Object.keys(rosterUpdates).length > 0) {
        await update(ref(db), rosterUpdates);
      }

      // Update local state
      setEmployees(prev => {
        const updated = prev
          .filter(emp => emp.employeeId !== employeeId)
          .map(emp => {
            const affected = affectedEmployees.find(a => a.employeeId === emp.employeeId);
            return affected ? { ...emp, rowIndex: affected.newRowIndex } : emp;
          });
        return updated.sort((a, b) => a.rowIndex - b.rowIndex);
      });
    } catch (err) {
      console.error('Error deleting employee:', err);
      throw new Error('Failed to delete employee: ' + err.message);
    }
  };

  const removeEmployeeFromRosters = async (employeeId) => {
    const futurePaths = await getFutureRosterPaths();
    if (futurePaths.length === 0) return;

    const db = getDatabase();
    const updates = {};
    futurePaths.forEach(path => {
      updates[`rostersDraft/${path}/employeeShifts/${employeeId}`] = null;
    });
    await update(ref(db), updates);
  };

  /**
   * Checks if an employee has entries in any future rosters.
   *
   * @param {string} employeeId - The ID of the employee to check.
   * @returns {Promise<boolean>} - A promise that resolves to true if the employee has future roster entries, false otherwise.
   */
  const checkFutureRosterEntries = async (employeeId) => {
    const db = getDatabase();
    const futurePaths = await getFutureRosterPaths();
    if (futurePaths.length === 0) {
      return false; // No future rosters, so no entries.
    }

    for (const path of futurePaths) {
      const rosterRef = ref(db, `rostersDraft/${path}/employeeShifts/${employeeId}`);
      const snapshot = await get(rosterRef);
      if (snapshot.exists()) {
        return true; // Found an entry in a future roster.
      }
    }
    return false; // No entries found in any future roster.
  };

  // ... (rest of the contact info and shifts functions remain the same)
  const updateContactInfo = async (key, phone) => {
    try {
      const db = getDatabase();
      await set(ref(db, `contactInfo/${key}`), { phone });
      setContactInfo((prev) => ({ ...prev, [key]: { phone } }));
    } catch (err) {
      console.error('Error updating contact info:', err);
      throw new Error('Failed to update contact info: ' + err.message);
    }
  };

  const addContactInfo = async (key, phone) => {
    try {
      const db = getDatabase();
      await set(ref(db, `contactInfo/${key}`), { phone });
      setContactInfo((prev) => ({ ...prev, [key]: { phone } }));
    } catch (err) {
      console.error('Error adding contact info:', err);
      throw new Error('Failed to add contact info: ' + err.message);
    }
  };

  const deleteContactInfo = async (key) => {
    try {
      const db = getDatabase();
      await remove(ref(db, `contactInfo/${key}`));
      setContactInfo((prev) => {
        const updated = { ...prev };
        delete updated[key];
        return updated;
      });
    } catch (err) {
      console.error('Error deleting contact info:', err);
      throw new Error('Failed to delete contact info: ' + err.message);
    }
  };

  const addShift = async (shiftName, shiftData) => {
    try {
      const db = getDatabase();
      await set(ref(db, `shifts/${shiftName}`), shiftData);
      setShifts((prev) => ({ ...prev, [shiftName]: shiftData }));
    } catch (err) {
      console.error('Error adding shift:', err);
      throw new Error('Failed to add shift: ' + err.message);
    }
  };

  const updateShift = async (shiftName, shiftData) => {
    try {
      const db = getDatabase();
      await set(ref(db, `shifts/${shiftName}`), shiftData);
      setShifts((prev) => ({ ...prev, [shiftName]: shiftData }));
    } catch (err) {
      console.error('Error updating shift:', err);
      throw new Error('Failed to update shift: ' + err.message);
    }
  };

  const deleteShift = async (shiftName) => {
    try {
      const db = getDatabase();
      await remove(ref(db, `shifts/${shiftName}`));
      setShifts((prev) => {
        const updated = { ...prev };
        delete updated[shiftName];
        return updated;
      });
    } catch (err) {
      console.error('Error deleting shift:', err);
      throw new Error('Failed to delete shift: ' + err.message);
    }
  };

  return {
    employees,
    contactInfo,
    shifts,
    loading,
    error,
    addEmployee,
    updateEmployee,
    deleteEmployee,
    updateContactInfo,
    addContactInfo,
    deleteContactInfo,
    addShift,
    updateShift,
    deleteShift,
  };
};

export default useEmployeeData;
