// src/components/CacheStatus.jsx
import React, { useState, useEffect } from 'react';
import dataCache from '../utils/cacheUtils';

const CacheStatus = () => {
  const [cacheInfo, setCacheInfo] = useState({
    size: 0,
    keys: []
  });
  const [expanded, setExpanded] = useState(false);

  // Update cache info every second
  useEffect(() => {
    const updateCacheInfo = () => {
      setCacheInfo({
        size: dataCache.size(),
        keys: dataCache.keys()
      });
    };

    // Initial update
    updateCacheInfo();

    // Set up interval
    const interval = setInterval(updateCacheInfo, 1000);

    // Clean up
    return () => clearInterval(interval);
  }, []);

  const handleClearCache = () => {
    dataCache.clear();
    setCacheInfo({
      size: 0,
      keys: []
    });
  };

  const handleClearItem = (key) => {
    dataCache.delete(key);
    setCacheInfo({
      size: dataCache.size(),
      keys: dataCache.keys()
    });
  };

  return (
    <div className="fixed bottom-4 right-4 bg-white shadow-lg rounded-lg p-4 z-50 max-w-md">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Cache Status</h3>
        <button
          onClick={() => setExpanded(!expanded)}
          className="text-blue-500 hover:text-blue-700"
        >
          {expanded ? 'Collapse' : 'Expand'}
        </button>
      </div>

      <div className="mt-2">
        <p>Items in cache: <span className="font-semibold">{cacheInfo.size}</span></p>
      </div>

      {expanded && (
        <div className="mt-4">
          <div className="flex justify-between mb-2">
            <h4 className="font-medium">Cached Items</h4>
            <button
              onClick={handleClearCache}
              className="text-xs bg-red-500 hover:bg-red-600 text-white px-2 py-1 rounded"
            >
              Clear All
            </button>
          </div>

          {cacheInfo.keys.length > 0 ? (
            <ul className="max-h-40 overflow-y-auto">
              {cacheInfo.keys.map(key => (
                <li key={key} className="flex justify-between items-center py-1 border-b">
                  <span className="text-sm">{key}</span>
                  <button
                    onClick={() => handleClearItem(key)}
                    className="text-xs text-red-500 hover:text-red-700"
                  >
                    Clear
                  </button>
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-sm text-gray-500">No items in cache</p>
          )}
        </div>
      )}
    </div>
  );
};

export default CacheStatus;
