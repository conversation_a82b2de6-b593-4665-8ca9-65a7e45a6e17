# Move RosterTable hooks back
Move-Item -Path "src/backup2/hooks/RosterTable/useRosterMode.js" -Destination "src/hooks/RosterTable/" -Force -ErrorAction SilentlyContinue
Move-Item -Path "src/backup2/hooks/RosterTable/useRosterHistory.js" -Destination "src/hooks/RosterTable/" -Force -ErrorAction SilentlyContinue
Move-Item -Path "src/backup2/hooks/RosterTable/useSelection.js" -Destination "src/hooks/RosterTable/" -Force -ErrorAction SilentlyContinue
Move-Item -Path "src/backup2/hooks/RosterTable/useDragAndDrop.js" -Destination "src/hooks/RosterTable/" -Force -ErrorAction SilentlyContinue
Move-Item -Path "src/backup2/hooks/RosterTable/useRosterData.js" -Destination "src/hooks/RosterTable/" -Force -ErrorAction SilentlyContinue
Move-Item -Path "src/backup2/hooks/RosterTable/useRosterModal.js" -Destination "src/hooks/RosterTable/" -Force -ErrorAction SilentlyContinue
Move-Item -Path "src/backup2/hooks/RosterTable/useAutoScroll.js" -Destination "src/hooks/RosterTable/" -Force -ErrorAction SilentlyContinue

Write-Host "RosterTable hooks restored successfully!"
