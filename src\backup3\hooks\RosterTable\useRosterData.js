// src/hooks/RosterTable/useRosterData.js
import { useCallback } from 'react';
import { updateEmployeeWorkMetrics } from '../../utils/rosterUtils';

/**
 * Custom hook to manage roster data operations
 *
 * @param {Array} rosterData - The roster data
 * @param {Function} addToHistory - Function to add to history
 * @param {Function} trackCellChange - Function to track cell changes
 * @param {Object} shiftHours - Shift hours configuration
 * @param {number} daysInMonth - Number of days in the month
 * @param {boolean} isRosterDraft - Whether the roster is in draft mode
 * @returns {Object} Roster data operations
 */
const useRosterData = (
  rosterData,
  addToHistory,
  trackCellChange,
  shiftHours,
  daysInMonth,
  isRosterDraft,
  setDropdownPosition
) => {
  // Handle shift change from dropdown (Update Mode)
  const handleShiftChange = useCallback((techId, day, newShift) => {
    if (!isRosterDraft) return null;

    const tech = rosterData.find(t => t.employeeId === techId);
    const currentShift = tech[day] || '';

    if (newShift === currentShift) {
      // Close dropdown even if no change
      if (setDropdownPosition) {
        setDropdownPosition({ techId: null, day: null });
      }
      return null;
    }

    const newRosterData = rosterData.map((tech) => {
      if (tech.employeeId !== techId) return { ...tech };

      // Create a copy of the tech object
      let updatedTech = { ...tech };
      // Update the shift
      updatedTech[day] = newShift;
      // Update work metrics and return the result
      return updateEmployeeWorkMetrics(updatedTech, shiftHours, daysInMonth);
    });

    trackCellChange(techId, day);

    // Close dropdown after changing shift
    if (setDropdownPosition) {
      setDropdownPosition({ techId: null, day: null });
    }

    return addToHistory(newRosterData);
  }, [rosterData, isRosterDraft, addToHistory, trackCellChange, shiftHours, daysInMonth, setDropdownPosition]);

  // Handle modal form submission (Dialog Mode)
  const handleModalSubmit = useCallback((modalData) => {
    if (!isRosterDraft) return null;

    const { modalTechId, modalStartDay, modalEndDay, modalShifts } = modalData;
    const start = parseInt(modalStartDay);
    const end = parseInt(modalEndDay);

    if (end - start + 1 > 7) {
      return { error: 'The date range cannot exceed 7 days.' };
    }

    const newRosterData = rosterData.map(tech => {
      if (tech.employeeId !== modalTechId) return { ...tech };

      // Create a copy of the tech object
      let updatedTech = { ...tech };
      const originalShifts = { ...tech };

      for (let day = start; day <= end; day++) {
        const dayStr = day.toString();
        const newShift = modalShifts[dayStr];

        if (newShift) {
          updatedTech[dayStr] = newShift;
        } else {
          delete updatedTech[dayStr];
        }

        if (updatedTech[dayStr] !== originalShifts[dayStr]) {
          trackCellChange(modalTechId, dayStr);
        }
      }

      // Update work metrics and return the result
      return updateEmployeeWorkMetrics(updatedTech, shiftHours, daysInMonth);
    });

    return addToHistory(newRosterData);
  }, [rosterData, isRosterDraft, addToHistory, trackCellChange, shiftHours, daysInMonth]);

  return {
    handleShiftChange,
    handleModalSubmit
  };
};

export default useRosterData;
