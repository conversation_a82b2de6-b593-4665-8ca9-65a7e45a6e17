const { initializeApp } = require('firebase-admin/app');
const { getAuth } = require('firebase-admin/auth');
const { getDatabase } = require('firebase-admin/database');
const {  onValueUpdated} = require("firebase-functions/v2/database");
const logger = require("firebase-functions/logger");
const { onRequest } = require('firebase-functions/v2/https');
const { Telegraf } = require('telegraf');


initializeApp();
// Securely retrieve the Telegram Bot token from environment configuration
const telegramBotToken = '**********************************************';
if (!telegramBotToken) {
    logger.error("Telegram Bot Token is not configured in environment variables!");
    // Consider throwing an error to prevent function execution
    // throw new Error("Telegram Bot Token is not configured.");
}

// Initialize Telegraf bot (only if token is available)
const bot = telegramBotToken ? new Telegraf(telegramBotToken) : null;
const db = getDatabase();

// --- Helper Functions ---

/**
 * Stores a chat ID in the Firebase Realtime Database.
 * @param {number} chatId - The Telegram chat ID to store.
 * @returns {Promise<void>}
 */
const storeChatId = async (chatId) => {
    try {
        const subscriberRef = db.ref(`/telegram_subscribers/${chatId}`);
        await subscriberRef.set({ subscribed: true }); // You can store more user info if needed
        logger.info(`Chat ID ${chatId} stored successfully.`);
    } catch (error) {
        logger.error(`Error storing chat ID ${chatId}:`, error);
        throw error; // Re-throw to be caught by the caller
    }
};

/**
 * Retrieves all chat IDs from the Firebase Realtime Database.
 * @returns {Promise<string[]>} - An array of chat IDs.
 */
const getChatIds = async () => {
    try {
        const subscribersRef = db.ref('/telegram_subscribers');
        const snapshot = await subscribersRef.get();
        const chatIds = [];
        snapshot.forEach(childSnapshot => {
            const chatId = childSnapshot.key; // Use key, not val().  The key *is* the chat ID.
            chatIds.push(chatId);
        });
        logger.info(`Retrieved chat IDs: ${chatIds.join(', ')}`);
        return chatIds;
    } catch (error) {
        logger.error("Error retrieving chat IDs:", error);
        throw error;  // Re-throw to be caught by the caller
    }
};

/**
 * Sends a Telegram message to a single chat ID.  Handles errors.
 * @param {string} chatId - The Telegram chat ID.
 * @param {string} message - The message to send.
 * @returns {Promise<void>}
 */
const sendTelegramMessage = async (chatId, message) => {
    try {
        await bot.telegram.sendMessage(chatId, message);
        logger.info(`Message sent successfully to chat ID ${chatId}.`);
    } catch (error) {
        logger.error(`Error sending message to chat ID ${chatId}:`, error);
        //  IMPORTANT:  Decide how to handle this error.  
        //  Should you retry?  Remove the chat ID from the database?
        //  For now, we'll just log and continue.  Acknowledge that the message *failed* for this chat.
    }
};

// --- Cloud Functions ---

/**
 * Cloud Function to handle the Telegram Bot webhook.
 * This function receives updates from Telegram (messages, commands, etc.).
 */
exports.telegramWebhook = onRequest(async (req, res) => {
    if (!bot) {
        logger.warn("Telegram Bot is not initialized.  This function will not work.");
        res.status(500).send("Telegram Bot not configured."); // Send 500, not 200
        return;
    }

    try {
        const update = req.body;
        logger.info("Received Telegram update:", update);

        // Handle /start command (and other commands if you want)
        if (update.message && update.message.text === '/start') {
            const chatId = String(update.message.chat.id); //chat.id is a number, convert to string
            try {
                await storeChatId(chatId);
                await bot.telegram.sendMessage(chatId, 'Welcome! You will now receive roster updates.');
                res.status(200).send('OK'); // Always send 200 for successful webhook
                return;
            } catch (error) {
                res.status(500).send('Error storing chat ID'); // inform telegram of error
                return;
            }
        }
        //  IMPORTANT:  If you're only handling /start, and *not* calling bot.handleUpdate,
        //  you MUST send a 200 response here for *every* other kind of message,
        //  or Telegram will keep retrying.
        res.status(200).send('OK');

    } catch (error) {
        logger.error("Error handling Telegram webhook:", error);
        res.status(500).send('Internal Server Error'); // Use 500 for server errors.
    }
});



/**
 * Firebase Realtime Database trigger that sends a Telegram notification
 * when the data at the '/rosters' path changes.
 */
exports.rosterUpdateNotification = onValueUpdated(
    {
        ref: '/rosters/{pushId}',
        region: "us-central1",
    },
    async (event) => {
        if (!bot) {
            logger.warn("Telegram Bot not initialized. Skipping notification.");
            return;
        }

        logger.info("Roster data changed:", event);

        const afterData = event.data.after.val();
        const beforeData = event.data.before.val();

        if (JSON.stringify(afterData) === JSON.stringify(beforeData)) {
            logger.info("No significant data change, skipping notification.");
            return;
        }

        const message = "New roster update!";

        try {
            const chatIds = await getChatIds(); // Get the chat IDs from the database
            await Promise.all(chatIds.map(chatId => sendTelegramMessage(chatId, message))); //use the helper
            logger.info("Telegram notifications sent successfully to:", chatIds);
        } catch (error) {
            //  IMPORTANT:  Error from getChatIds or sendTelegramMessage is already logged.
            logger.error("Error in rosterUpdateNotification:", error);
            //  Consider if you need to do anything else (e.g., retry, alert an admin).
        }
    }
);


// You can also trigger on creation or deletion if needed:




/**
 * Firebase Function to add a user (HTTPS POST Trigger).
 */
exports.addUser = onRequest(
  {
    enforceAppCheck: false, // Set to true in production
    region: "us-central1",  // Adjust to your region
  },
  async (req, res) => {
    // Set CORS headers for all responses
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    res.set('Access-Control-Max-Age', '3600');

    logger.info("Received addUser request:", {
      method: req.method,
      url: req.url,
      headers: req.headers,
    });

    try {
      // Handle OPTIONS preflight request
      if (req.method === 'OPTIONS') {
        logger.info("Handling OPTIONS preflight request for addUser");
        res.status(204).send('');
        return;
      }

      // Check for POST request
      if (req.method !== 'POST') {
        logger.error("Invalid method for addUser:", req.method);
        res.status(405).send({ error: 'Method Not Allowed. Use POST.' });
        return;
      }

      // Verify Bearer token
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        logger.error("Missing or invalid Authorization header for addUser");
        res.status(403).send({ error: "Unauthorized: Missing or invalid token." });
        return;
      }

      const idToken = authHeader.split('Bearer ')[1];
      const decodedToken = await getAuth().verifyIdToken(idToken);
      logger.info("Token verified for user:", decodedToken.uid);

      // TODO: Enable admin check in production
      // const userRecord = await getAuth().getUser(decodedToken.uid);
      // if (!userRecord.customClaims?.admin) {
      //   logger.error("Non-admin user attempted to add user:", decodedToken.uid);
      //   res.status(403).send({ error: "Must be an admin to add users." });
      //   return;
      // }

      // Extract email and displayName
      const { email, displayName } = req.body;
      if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        logger.error("Invalid or missing email");
        res.status(400).send({ error: 'Valid email is required.' });
        return;
      }
      if (!displayName) {
        logger.error("Missing displayName");
        res.status(400).send({ error: 'Display name is required.' });
        return;
      }

      logger.info(`Attempting to create user with email: ${email}`);

      // Create user in Firebase Auth
      const userRecord = await getAuth().createUser({
        email,
        displayName,
        emailVerified: false,
        disabled: false,
      });
      logger.info(`Successfully created user with UID: ${userRecord.uid}`);

      // Set user data in Realtime Database
      const database = getDatabase();
      const userRef = database.ref(`users/${userRecord.uid}`);
      await userRef.set({
        email,
        role: 'user',
        uid: userRecord.uid,
        displayName,
      });
      logger.info(`Successfully set user data for UID: ${userRecord.uid} in Realtime Database`);

      res.status(200).send({
        message: `User with email ${email} created successfully.`,
        uid: userRecord.uid,
      });
    } catch (error) {
      logger.error("Error adding user:", {
        message: error.message,
        code: error.code,
        email: req.body.email,
      });
      if (error.code === 'auth/email-already-in-use') {
        res.status(400).send({ error: 'Email is already in use.' });
      } else if (error.code === 'auth/invalid-email') {
        res.status(400).send({ error: 'Invalid email format.' });
      } else {
        res.status(500).send({ error: `Failed to add user: ${error.message}` });
      }
    }
  }
);

/**
 * Firebase Function to delete a user (HTTPS DELETE Trigger).
 */
exports.deleteUser = onRequest(
  {
    enforceAppCheck: false, // Set to true in production
    region: "us-central1",  // Adjust to your region
  },
  async (req, res) => {
    // Set CORS headers for all responses
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'DELETE, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    res.set('Access-Control-Max-Age', '3600');

    logger.info("Received deleteUser request:", {
      method: req.method,
      url: req.url,
      headers: req.headers,
    });

    try {
      // Handle OPTIONS preflight request
      if (req.method === 'OPTIONS') {
        logger.info("Handling OPTIONS preflight request for deleteUser");
        res.status(204).send('');
        return;
      }

      // Check for DELETE request
      if (req.method !== 'DELETE') {
        logger.error("Invalid method for deleteUser:", req.method);
        res.status(405).send({ error: 'Method Not Allowed. Use DELETE.' });
        return;
      }

      // Verify Bearer token
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        logger.error("Missing or invalid Authorization header for deleteUser");
        res.status(403).send({ error: "Unauthorized: Missing or invalid token." });
        return;
      }

      const idToken = authHeader.split('Bearer ')[1];
      const decodedToken = await getAuth().verifyIdToken(idToken);
      logger.info("Token verified for user:", decodedToken.uid);

      // TODO: Enable admin check in production
      // if (!decodedToken.admin) {
      //   logger.error("Non-admin user attempted deletion:", decodedToken.uid);
      //   res.status(403).send({ error: "Must be an admin to delete users." });
      //   return;
      // }

      // Extract uid
      const uid = req.query.uid || (req.body && req.body.uid);
      if (!uid) {
        logger.error("User ID is missing");
        res.status(400).send({ error: 'User ID is required.' });
        return;
      }

      logger.info(`Attempting to delete user with UID: ${uid}`);

      // Delete from Firebase Auth
      await getAuth().deleteUser(uid);
      logger.info(`Successfully deleted user with UID: ${uid} from Firebase Auth`);

      // Delete from Realtime Database
      const database = getDatabase();
      const userRef = database.ref(`users/${uid}`);
      await userRef.remove();
      logger.info(`Successfully deleted user data with UID: ${uid} from Realtime Database`);

      res.status(200).send({ message: `User with UID ${uid} deleted successfully.` });
    } catch (error) {
      logger.error("Error deleting user:", {
        message: error.message,
        code: error.code,
        uid: req.query.uid || req.body?.uid,
      });
      if (error.code === 'auth/user-not-found') {
        res.status(404).send({ error: 'User not found.' });
      } else if (error.code === 'auth/invalid-id-token') {
        res.status(401).send({ error: 'Invalid or expired token.' });
      } else {
        res.status(500).send({ error: `Failed to delete user: ${error.message}` });
      }
    }
  }
);