// src\Navbar.js
import React, { useContext, useState, useEffect } from 'react';
import { Link, NavLink, useNavigate, useLocation } from 'react-router-dom';
import { AuthContext } from './components/AuthProvider';
import { auth } from './firebaseConfig';
import { signOut } from 'firebase/auth';
import { toast } from 'react-toastify';
import './Navbar.css';
import { FaBars, FaTimes } from 'react-icons/fa'; // Import icons

function Navbar() {
  const { currentUser, userRole } = useContext(AuthContext);
  const navigate = useNavigate();
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isLargeScreen, setIsLargeScreen] = useState(window.innerWidth >= 769);

  useEffect(() => {
    const handleResize = () => {
      setIsLargeScreen(window.innerWidth >= 769);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const handleSignOut = async () => {
    try {
      await signOut(auth);
      toast.success('Signed out successfully!');
      navigate('/login');
    } catch (error) {
      console.error('Sign out error:', error);
      toast.error('Failed to sign out.');
    }
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  const renderMenuItems = () => {
    const baseMenuItems = [
      { to: '/roster', label: 'Roster' , showOnMobile: false},
      { to: '/mobile-roster', label: 'Mobile Roster' },
      { to: '/employee-search', label: 'Search' },
      { to: '/about', label: 'About' },
    ];

    const adminMenuItems = [
      { to: '/', label: 'Home', showOnMobile: false }, // Exclude from mobile
      { to: '/employees', label: 'Dashboard', showOnMobile: false }, // Exclude from mobile
      { to: '/employees-docs', label: 'Employee Docs', showOnMobile: false }, // Exclude from mobile
      ...baseMenuItems,
    ];

    const userMenuItems = [
      ...baseMenuItems,
    ];

    let menuItems;
    if (userRole === 'admin') {
      menuItems = adminMenuItems;
    } else if (userRole === 'user') {
      menuItems = userMenuItems;
    } else {
      return null;
    }

    return (
      <>
        {menuItems.map((item) => (
          (isLargeScreen || item.showOnMobile !== false) && (
            <li key={item.to}>
              <NavLink
                to={item.to}
                className={({ isActive }) => `navbar-link ${isActive ? 'active' : ''}`}
                aria-label={`${item.label} page`}
                onClick={closeMobileMenu}
              >
                {item.label}
              </NavLink>
            </li>
          )
        ))}
        {currentUser && (
          <li>
            <button onClick={handleSignOut} className="navbar-signout mobile-signout">
              Sign Out
            </button>
          </li>
        )}
        {!currentUser && (
          <li>
            <Link to="/login" className="navbar-signin mobile-signin" onClick={closeMobileMenu}>
              Sign In
            </Link>
          </li>
        )}
      </>
    );
  };

  return (
    <nav className="navbar">
      <div className="navbar-container">
      <Link to="/" className="navbar-logo" aria-label="Go to home page">
      <img src="./hrm.png" alt="Logo" className="navbar-icon" />
  {isLargeScreen ? 'HRM' : 'HRM'}
</Link>

        <div className="mobile-menu-icon" onClick={toggleMobileMenu} aria-label="Toggle mobile navigation">
          {isMobileMenuOpen ? <FaTimes /> : <FaBars />}
        </div>
        <ul className={`navbar-menu ${isMobileMenuOpen ? 'open' : ''}`}>
          {renderMenuItems()}
        </ul>
        {/* The auth buttons are now inside the mobile menu */}
      </div>
    </nav>
  );
}

export default Navbar;