// src\contexts\actions\index.js
export {
  setRoster<PERSON>ear,
  setRoster<PERSON>onth,
  toggleRosterDraft,
  fetchRosterData,
  fetchAvailableYears,
  fetchAvailableMonths
} from './rosterActions';

export {
  addEmployee,
  updateEmployee,
  deleteEmployee,
  fetchEmployeesData
} from './employeeActions';

export {
  updateShift,
  deleteShift,
  fetchShiftsData
} from './shiftActions';

export {
  updateContactInfo,
  deleteContactInfo,
  fetchContactInfo
} from './contactInfoActions';

export {
  addCalendarMarker,
  updateCalendarMarker,
  deleteCalendarMarker,
  fetchCalendarMarkers
} from './calendarMarkerActions';   
//  employeeActions from './employeeActions';
// export * as rosterActions from './rosterActions';
// export * as shiftActions from './shiftActions';
// export * as contactInfoActions from './contactInfoActions';
// export * as calendarMarkerActions from './calendarMarkerActions';