function RosterAdditionalInfo({ shiftHours, shiftStartTimes, shiftEndTimes, orderedContactInfo }) {
    return (
      <div style={{ backgroundColor: '#f3f4f6', padding: '1rem', borderRadius: '0.5rem' }}>
        <h3 style={{ fontWeight: 'bold', marginBottom: '0.5rem' }}>Additional Information:</h3>
        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '2rem' }}>
          <div>
            <h4 style={{ fontWeight: 'bold', marginBottom: '0.5rem' }}>Shift Schedule:</h4>
            <ul style={{ listStyle: 'none', padding: '0' }}>
              {Object.entries(shiftHours || {}).map(([shift, hours]) => {
                const showTimes = shiftStartTimes[shift] !== '0:00' || shiftEndTimes[shift] !== '0:00';
                return (
                  <li key={shift}>
                    <strong>{shift}{showTimes ? ` (${shiftStartTimes[shift]}–${shiftEndTimes[shift]})` : ''}:</strong> {hours} hours
                  </li>
                );
              })}
            </ul>
          </div>
          <div>
            <h4 style={{ fontWeight: 'bold', marginBottom: '0.5rem' }}>Team Contacts:</h4>
            <ul style={{ listStyle: 'none', padding: '0' }}>
              {Object.entries(orderedContactInfo || {}).map(([role, data]) => (
                <li key={role}>
                  <strong style={{ display: 'inline-block', width: '150px' }}>
                    {role.replace(/([A-Z0-9])/g, ' $1').trim()}:
                  </strong>{' '}
                  {data.phone || 'N/A'}
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    );
  }
  
  export default RosterAdditionalInfo;