/* Reset html and body to ensure no white background */
html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  background: transparent;
}

/* Full-screen background pattern */
.login-bg {
  position: relative;
  overflow: hidden;
  height: 100vh;
  width: 100vw;
}

.login-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40"><path fill="%232dd4bf" fill-opacity="0.1" d="M20 4a4 4 0 100 8 4 4 0 000-8zm0 14a4 4 0 100 8 4 4 0 000-8zm0 14a4 4 0 100 8 4 4 0 000-8z"/></svg>') repeat;
  z-index: -1;
}

/* Fade-in animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Poppins font (if not using Google Fonts in index.html) */
.font-poppins {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

@media (max-width: 640px) {
  .max-w-md {
    width: 90%;
  }
}