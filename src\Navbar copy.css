.navbar {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10;
  width: 100%;
  background-color: #ffffff;
  padding: 1rem 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar-container {
  max-width: 1400px;
  width: 100%;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar-logo {
  color: #2563eb;
  font-weight: bold;
  font-size: 1.5rem;
  text-decoration: none;
}

.mobile-menu-icon {
  display: none; /* Hide on larger screens */
  font-size: 1.5rem;
  cursor: pointer;
  color: #1f2937;
}

.navbar-menu {
  display: flex;
  list-style: none;
  padding: 0;
  margin: 0;
  align-items: center;
}

.navbar-menu li {
  margin: 0 1.5rem;
}

.navbar-link {
  text-decoration: none;
  color: #1f2937;
  font-weight: 600;
  font-size: 1.1rem;
  transition: color 0.2s ease;
}

.navbar-link:hover {
  color: #2563eb;
}

.navbar-link.active {
  color: #2563eb;
  border-bottom: 2px solid #2563eb;
  padding-bottom: 0.2rem;
}

.navbar-auth {
  display: flex;
  align-items: center;
}
.navbar-logo {
  display: flex;
  align-items: center;
  font-size: 1.5rem;
  font-weight: bold;
}

.navbar-icon {
  width: 32px;
  height: 32px;
  margin-right: 10px;
}
.navbar-signin {
  text-decoration: none;
  color: #2563eb;
  font-weight: 600;
  font-size: 1rem;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  transition: background-color 0.2s ease;
}

.navbar-signin:hover {
  background-color: #eff6ff;
}

.navbar-signout {
  background-color: #6b7280;
  color: white;
  font-weight: 600;
  font-size: 1rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-left: 1rem;
}

.navbar-signout:hover {
  background-color: #b91c1c;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .navbar-container {
    display: flex;
    justify-content: space-between;
  }

  .mobile-menu-icon {
    display: block;
  }

  .navbar-menu {
    display: none;
    position: absolute;
    top: 60px; /* Adjust based on your navbar height */
    left: 0;
    width: 70%; /* Give it a percentage width */
    max-width: 300px; /* Set a maximum width for the dropdown */
    background-color: rgba(245, 245, 245,1);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
    flex-direction: column;
    align-items: flex-start; /* Align items to the left in the dropdown */
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
    padding: 1rem;
    border-radius: 0.5rem;
    backdrop-filter: blur(6px); /* Soft blur effect for modern UI */
    z-index: 100; /* Ensure it's above other content */
  }

  .navbar-menu.open {
    display: flex;
  }

  .navbar-menu li {
   
   margin: 0.5rem 0;
    width: 100%; /* Make each list item take full width of the dropdown */
  }

  .navbar-link,
  .navbar-signin.mobile-signin,
  .navbar-signout.mobile-signout {
  
    display: block; /* Make links and buttons take full width */
    padding: 0.75rem 1rem;
    text-align: center; /* Align text to the left in the dropdown */
    width: 100%; /* Adjust width to fit content */
    border-radius: 0.25rem;
  }

  .navbar-signin.mobile-signin:hover {
    background-color: #eff6ff;
  }

  .navbar-signout.mobile-signout {
    background-color: #6b7280; /* Example different color for emphasis */
    width:fit-content; /* Adjust width to fit content */
    margin: 0 auto; /* Center the signout button */
    color: white;
    border: none;
  }

  .navbar-signout.mobile-signout:hover {
    background-color: #b91c1c;
  }

  .navbar-auth {
    display: none; /* Hide the separate auth section on mobile */
  }
}