import { useState, useEffect } from 'react';
import { ref, onValue } from 'firebase/database';
import { database } from '../firebaseConfig';

export default function useFetchCalendarMarkers() {
  const [weekdayMarkers, setWeekdayMarkers] = useState({});
  const [holidayMarkers, setHolidayMarkers] = useState({});
  const [specialDayMarkers, setSpecialDayMarkers] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    setLoading(true);
    const markersRef = ref(database, 'calendar_markers');
    const unsubscribe = onValue(
      markersRef,
      (snapshot) => {
        const data = snapshot.val() || {};
        setWeekdayMarkers(data.weekday_markers || {});
        setHolidayMarkers(data.holiday_markers || {});
        setSpecialDayMarkers(data.special_day_markers || {});
        setLoading(false);
      },
      (err) => {
        setError(err.message);
        setLoading(false);
      }
    );
    return () => unsubscribe();
  }, []);

  return { weekdayMarkers, holidayMarkers, specialDayMarkers, loading, error };
}