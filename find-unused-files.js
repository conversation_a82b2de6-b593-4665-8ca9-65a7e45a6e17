const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Get all JS and JSX files in the src directory
const getAllJsFiles = (dir) => {
  let results = [];
  const list = fs.readdirSync(dir);

  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat && stat.isDirectory()) {
      // Recursively search directories
      results = results.concat(getAllJsFiles(filePath));
    } else {
      // Check if file is JS or JSX
      if (file.endsWith('.js') || file.endsWith('.jsx')) {
        results.push(filePath);
      }
    }
  });

  return results;
};

// Extract file references from a file (imports, requires, and component references)
const extractFileReferences = (filePath) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const references = new Set();

    // Static imports
    const importRegex = /import\s+(?:(?:{[^}]*}|\*\s+as\s+[^,]+|\w+)\s+from\s+)?['"]([^'"]+)['"]/g;
    let match;

    while ((match = importRegex.exec(content)) !== null) {
      let importPath = match[1];

      // Skip node_modules imports
      if (!importPath.startsWith('.') && !importPath.startsWith('/')) {
        continue;
      }

      // Resolve relative path
      if (importPath.startsWith('.')) {
        const dir = path.dirname(filePath);
        importPath = path.resolve(dir, importPath);

        // Handle imports without extension
        if (!importPath.endsWith('.js') && !importPath.endsWith('.jsx')) {
          // Try to resolve with extensions
          if (fs.existsSync(`${importPath}.js`)) {
            importPath = `${importPath}.js`;
          } else if (fs.existsSync(`${importPath}.jsx`)) {
            importPath = `${importPath}.jsx`;
          } else if (fs.existsSync(path.join(importPath, 'index.js'))) {
            importPath = path.join(importPath, 'index.js');
          } else if (fs.existsSync(path.join(importPath, 'index.jsx'))) {
            importPath = path.join(importPath, 'index.jsx');
          }
        }
      }

      references.add(importPath);
    }

    // Dynamic imports
    const dynamicImportRegex = /import\s*\(\s*['"]([^'"]+)['"]\s*\)/g;
    while ((match = dynamicImportRegex.exec(content)) !== null) {
      let importPath = match[1];
      if (importPath.startsWith('.')) {
        const dir = path.dirname(filePath);
        importPath = path.resolve(dir, importPath);
        references.add(importPath);
      }
    }

    // Require statements
    const requireRegex = /require\s*\(\s*['"]([^'"]+)['"]\s*\)/g;
    while ((match = requireRegex.exec(content)) !== null) {
      let importPath = match[1];
      if (importPath.startsWith('.')) {
        const dir = path.dirname(filePath);
        importPath = path.resolve(dir, importPath);
        references.add(importPath);
      }
    }

    // Extract component names from JSX
    const componentRegex = /<([A-Z][a-zA-Z0-9]*)/g;
    const componentNames = new Set();
    while ((match = componentRegex.exec(content)) !== null) {
      componentNames.add(match[1]);
    }

    return {
      paths: Array.from(references),
      componentNames: Array.from(componentNames)
    };
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return { paths: [], componentNames: [] };
  }
};

// Main function to find unused files
const findUnusedFiles = () => {
  const srcDir = path.resolve('./src');
  const allFiles = getAllJsFiles(srcDir);

  // Normalize paths for comparison
  const normalizedFiles = allFiles.map(file => path.normalize(file));

  // Track which files are imported or referenced
  const referencedFiles = new Set();
  const componentNameToFile = new Map();

  // First pass: map component names to files
  allFiles.forEach(file => {
    try {
      const fileName = path.basename(file, path.extname(file));
      // If the file name starts with uppercase, it's likely a component
      if (/^[A-Z]/.test(fileName)) {
        componentNameToFile.set(fileName, file);
      }
    } catch (error) {
      console.error(`Error processing file name ${file}:`, error.message);
    }
  });

  // Second pass: extract all references
  allFiles.forEach(file => {
    const { paths, componentNames } = extractFileReferences(file);

    // Add direct path references
    paths.forEach(importPath => {
      try {
        // Normalize the import path
        const normalizedImport = path.normalize(importPath);
        referencedFiles.add(normalizedImport);
      } catch (error) {
        console.error(`Error normalizing path ${importPath}:`, error.message);
      }
    });

    // Add component references
    componentNames.forEach(componentName => {
      if (componentNameToFile.has(componentName)) {
        referencedFiles.add(componentNameToFile.get(componentName));
      }
    });
  });

  // Find files that are not referenced anywhere
  const unusedFiles = normalizedFiles.filter(file => {
    // Skip entry point files and test files
    if (
      file.endsWith('index.js') ||
      file.endsWith('App.js') ||
      file.includes('setupTests.js') ||
      file.includes('.test.js') ||
      file.includes('reportWebVitals.js')
    ) {
      return false;
    }

    return !referencedFiles.has(file);
  });

  // Group unused files by directory for better readability
  const unusedFilesByDir = {};
  unusedFiles.forEach(file => {
    const relPath = path.relative(srcDir, file);
    const dir = path.dirname(relPath);
    if (!unusedFilesByDir[dir]) {
      unusedFilesByDir[dir] = [];
    }
    unusedFilesByDir[dir].push(relPath);
  });

  console.log('Potentially unused files:');
  console.log('------------------------');

  Object.keys(unusedFilesByDir).sort().forEach(dir => {
    console.log(`\n${dir === '.' ? 'Root directory' : dir}:`);
    unusedFilesByDir[dir].sort().forEach(file => {
      console.log(`  - ${path.basename(file)}`);
    });
  });

  console.log('\nTotal potentially unused files:', unusedFiles.length);

  return unusedFiles;
};

findUnusedFiles();
