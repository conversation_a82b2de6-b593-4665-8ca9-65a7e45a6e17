// src/hooks/useMemoization.js
import { useMemo, useCallback, useState, useEffect, useRef } from 'react';

/**
 * Custom hook for memoizing expensive calculations
 * @param {Function} calculationFn - The calculation function to memoize
 * @param {Array} dependencies - Dependencies array for the calculation
 * @returns {any} The memoized calculation result
 */
export const useMemoizedCalculation = (calculationFn, dependencies) => {
  return useMemo(() => {
    console.log('Running memoized calculation');
    return calculationFn();
  }, dependencies);
};

/**
 * Custom hook for memoizing event handlers
 * @param {Function} handlerFn - The event handler function to memoize
 * @param {Array} dependencies - Dependencies array for the handler
 * @returns {Function} The memoized event handler
 */
export const useMemoizedHandler = (handlerFn, dependencies) => {
  return useCallback(handlerFn, dependencies);
};

/**
 * Custom hook for debouncing values
 * @param {any} value - The value to debounce
 * @param {number} delay - Delay in milliseconds
 * @returns {any} The debounced value
 */
export const useDebounce = (value, delay = 300) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);

  return debouncedValue;
};

/**
 * Custom hook for throttling values
 * @param {any} value - The value to throttle
 * @param {number} limit - Throttle limit in milliseconds
 * @returns {any} The throttled value
 */
export const useThrottle = (value, limit = 300) => {
  const [throttledValue, setThrottledValue] = useState(value);
  const lastRan = useRef(Date.now());

  useEffect(() => {
    const handler = setTimeout(() => {
      if (Date.now() - lastRan.current >= limit) {
        setThrottledValue(value);
        lastRan.current = Date.now();
      }
    }, limit - (Date.now() - lastRan.current));

    return () => {
      clearTimeout(handler);
    };
  }, [value, limit]);

  return throttledValue;
};

/**
 * Custom hook for preventing unnecessary re-renders
 * @param {any} value - The value to stabilize
 * @param {Function} isEqual - Function to compare old and new values
 * @returns {any} The stabilized value
 */
export const useStableValue = (value, isEqual = (a, b) => a === b) => {
  const ref = useRef(value);

  if (!isEqual(ref.current, value)) {
    ref.current = value;
  }

  return ref.current;
};

/**
 * Custom hook for memoizing objects
 * @param {Object} obj - The object to memoize
 * @returns {Object} The memoized object
 */
export const useMemoizedObject = (obj) => {
  const ref = useRef(obj);
  
  // Only update the ref if the object has actually changed
  if (!shallowEqual(ref.current, obj)) {
    ref.current = obj;
  }
  
  return ref.current;
};

/**
 * Helper function to perform shallow equality check on objects
 * @param {Object} objA - First object
 * @param {Object} objB - Second object
 * @returns {boolean} True if objects are shallowly equal
 */
const shallowEqual = (objA, objB) => {
  if (objA === objB) {
    return true;
  }

  if (
    typeof objA !== 'object' ||
    objA === null ||
    typeof objB !== 'object' ||
    objB === null
  ) {
    return false;
  }

  const keysA = Object.keys(objA);
  const keysB = Object.keys(objB);

  if (keysA.length !== keysB.length) {
    return false;
  }

  for (let i = 0; i < keysA.length; i++) {
    const key = keysA[i];
    if (
      !Object.prototype.hasOwnProperty.call(objB, key) ||
      objA[key] !== objB[key]
    ) {
      return false;
    }
  }

  return true;
};

export default {
  useMemoizedCalculation,
  useMemoizedHandler,
  useDebounce,
  useThrottle,
  useStableValue,
  useMemoizedObject
};
