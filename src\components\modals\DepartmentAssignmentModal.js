import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

const DepartmentAssignmentModal = ({
  isOpen,
  onClose,
  user,
  modalType,
  selectedDepartment,
  onDepartmentChange,
  onAssign,
  onRemove,
  getAvailableDepartments,
  currentUserRole
}) => {
  const [availableDepartments, setAvailableDepartments] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load available departments when modal opens
  useEffect(() => {
    const loadDepartments = async () => {
      if (isOpen && user && modalType === 'add') {
        console.log('DepartmentAssignmentModal: Loading departments for user:', user);
        console.log('DepartmentAssignmentModal: Current user role:', currentUserRole);

        setIsLoading(true);
        try {
          const departments = await getAvailableDepartments(user.uid, currentUserRole);
          console.log('DepartmentAssignmentModal: Received departments:', departments);
          setAvailableDepartments(departments);
        } catch (error) {
          console.error('DepartmentAssignmentModal: Error loading departments:', error);
          setAvailableDepartments([]);
        } finally {
          setIsLoading(false);
        }
      }
    };

    loadDepartments();
  }, [isOpen, user, modalType, getAvailableDepartments, currentUserRole]);

  const handleAssign = async () => {
    if (!selectedDepartment || !user) return;

    setIsSubmitting(true);
    try {
      const success = await onAssign(user.uid, selectedDepartment);
      if (success) {
        onClose();
      }
    } catch (error) {
      console.error('Error assigning department:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRemove = async (departmentId) => {
    if (!departmentId || !user) return;

    setIsSubmitting(true);
    try {
      const success = await onRemove(user.uid, departmentId);
      if (success) {
        onClose();
      }
    } catch (error) {
      console.error('Error removing department:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen || !user) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">
            {modalType === 'add' ? '➕ Add Department' : '➖ Remove Department'}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
            disabled={isSubmitting}
          >
            ✕
          </button>
        </div>

        <div className="mb-4">
          <p className="text-sm text-gray-600 mb-2">
            <strong>User:</strong> {user.email}
          </p>
          <p className="text-sm text-gray-600 mb-4">
            <strong>Role:</strong> <span className={`px-2 py-1 rounded text-xs font-medium ${
              user.role === 'region-manager'
                ? 'bg-purple-100 text-purple-800'
                : 'bg-green-100 text-green-800'
            }`}>
              {user.role}
            </span>
          </p>
        </div>

        {modalType === 'add' ? (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Department to Assign:
            </label>

            {isLoading ? (
              <div className="text-center py-4">
                <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <p className="text-sm text-gray-500 mt-2">Loading departments...</p>
              </div>
            ) : availableDepartments.length > 0 ? (
              <div>
                <select
                  value={selectedDepartment}
                  onChange={(e) => onDepartmentChange(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  disabled={isSubmitting}
                >
                  <option value="">Select a department...</option>
                  {availableDepartments.map((dept) => (
                    <option key={dept.id} value={dept.id}>
                      {dept.name} ({dept.id})
                    </option>
                  ))}
                </select>

                <div className="mt-4 flex justify-end space-x-3">
                  <button
                    onClick={onClose}
                    className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
                    disabled={isSubmitting}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleAssign}
                    disabled={!selectedDepartment || isSubmitting}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? 'Assigning...' : 'Assign Department'}
                  </button>
                </div>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-500 mb-4">
                  {currentUserRole === 'region-manager'
                    ? 'No departments available to assign. You can only assign departments from your own managed departments.'
                    : 'No departments available to assign to this user.'
                  }
                </p>
                <button
                  onClick={onClose}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Close
                </button>
              </div>
            )}
          </div>
        ) : (
          <div>
            <p className="text-sm text-gray-600 mb-4">
              Current assigned departments:
            </p>

            {user.managedDepartments && user.managedDepartments.length > 0 ? (
              <div className="space-y-2 mb-4">
                {user.managedDepartments.map((dept) => (
                  <div
                    key={dept}
                    className="flex items-center justify-between p-3 border border-gray-200 rounded-md"
                  >
                    <span className="font-medium">{dept}</span>
                    <button
                      onClick={() => handleRemove(dept)}
                      disabled={isSubmitting}
                      className="px-3 py-1 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 disabled:bg-gray-400"
                    >
                      {isSubmitting ? 'Removing...' : 'Remove'}
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 mb-4">No departments currently assigned.</p>
            )}

            <div className="flex justify-end">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
                disabled={isSubmitting}
              >
                Close
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

DepartmentAssignmentModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  user: PropTypes.object,
  modalType: PropTypes.oneOf(['add', 'remove']).isRequired,
  selectedDepartment: PropTypes.string.isRequired,
  onDepartmentChange: PropTypes.func.isRequired,
  onAssign: PropTypes.func.isRequired,
  onRemove: PropTypes.func.isRequired,
  getAvailableDepartments: PropTypes.func.isRequired,
  currentUserRole: PropTypes.string.isRequired
};

export default DepartmentAssignmentModal;
