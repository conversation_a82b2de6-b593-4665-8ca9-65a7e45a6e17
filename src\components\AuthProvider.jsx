import React, { createContext, useState, useEffect } from 'react';
import { auth } from '../firebaseConfig';
import { onAuthStateChanged } from 'firebase/auth';
import { getDatabase, ref, get, set } from 'firebase/database';
import { toast } from 'react-toastify';
import { createEmptyDepartmentStructure } from '../utils/departmentUtils';

export const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [userRole, setUserRole] = useState(null);
  const [selectedDepartment, setSelectedDepartment] = useState(null);
  const [userDepartment, setUserDepartment] = useState(null);
  const [loading, setLoading] = useState(true);
  const [departments, setDepartments] = useState([]);
  const [isRegionManager, setIsRegionManager] = useState(false);
  const [isSuperUser, setIsSuperUser] = useState(false);
  const [isRegionUser, setIsRegionUser] = useState(false);
  const [managedDepartments, setManagedDepartments] = useState([]);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        setCurrentUser(user);
        const db = getDatabase();
        const userRef = ref(db, `users/${user.uid}`);
        const snapshot = await get(userRef);

        // Fetch departments for all authenticated users
        const departmentsRef = ref(db, 'departments');
        const deptSnapshot = await get(departmentsRef);
        let deptList = [];
        if (deptSnapshot.exists()) {
          const deptData = deptSnapshot.val();
          deptList = Object.entries(deptData).map(([deptId, dept]) => ({
            id: deptId,
            name: dept.department_name || deptId,

          }));
          setDepartments(deptList);
        } else {
          console.error('AuthProvider: No departments found');
          setDepartments([]);
        }

        if (snapshot.exists()) {
          const userData = snapshot.val();
          const role = userData.role;

          setUserRole(role);

          if (role === 'super-admin') {
            setIsRegionManager(false);
            setIsSuperUser(false);
            setIsRegionUser(false);
            setManagedDepartments([]);
            setSelectedDepartment(deptList.length > 0 ? deptList[0].id : null);
            setUserDepartment(deptList.length > 0 ? deptList[0].id : null);
          } else if (role === 'region-manager') {
            setIsRegionManager(true);
            setIsSuperUser(false);
            setIsRegionUser(false);
            const managedDepts = userData.managedDepartments || [];
            console.log('Managed Departments:', managedDepts);
            setManagedDepartments(managedDepts);
            // Set first managed department as selected, or first available if none managed
            const firstManagedDept = managedDepts.length > 0 ? managedDepts[0] : (deptList.length > 0 ? deptList[0].id : null);
            setSelectedDepartment(firstManagedDept);
            console.log('Selected Department from auth:',firstManagedDept);
            setUserDepartment(firstManagedDept); // Region managers don't have a single department
          } else if (role === 'super-user') {
            setIsRegionManager(false);
            setIsSuperUser(true);
            setIsRegionUser(false);
            setManagedDepartments([]);
            // Set first department as selected
            const firstDept = deptList.length > 0 ? deptList[0].id : null;
            setSelectedDepartment(firstDept);
            setUserDepartment(null); // Super-users don't have a single department
          } else if (role === 'region-user') {
            setIsRegionManager(false);
            setIsSuperUser(false);
            setIsRegionUser(true);
            const managedDepts = userData.managedDepartments || [];
            setManagedDepartments(managedDepts);
            // Set first managed department as selected, or first available if none managed
            const firstManagedDept = managedDepts.length > 0 ? managedDepts[0] : (deptList.length > 0 ? deptList[0].id : null);
            setSelectedDepartment(firstManagedDept);
            setUserDepartment(null); // Region-users don't have a single department
          } else {
            // Regular users/admins get their assigned department
            setIsRegionManager(false);
            setIsSuperUser(false);
            setIsRegionUser(false);
            setManagedDepartments([]);
            const userDept = userData.department || null;
            setUserDepartment(userDept);
            setSelectedDepartment(userDept);
          }
        } else {
          setUserRole(null);
          setIsRegionManager(false);
          setIsSuperUser(false);
          setIsRegionUser(false);
          setManagedDepartments([]);
          setSelectedDepartment(null);
          setUserDepartment(null);
        }
      } else {
        setCurrentUser(null);
        setUserRole(null);
        setIsRegionManager(false);
        setIsSuperUser(false);
        setIsRegionUser(false);
        setManagedDepartments([]);
        setSelectedDepartment(null);
        setUserDepartment(null);
        setDepartments([]);
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const isSuperAdmin = () => userRole === 'super-admin';
  const isRegionManagerRole = () => userRole === 'region-manager';
  const isSuperUserRole = () => userRole === 'super-user';
  const isRegionUserRole = () => userRole === 'region-user';

  // Helper function to check if user can switch departments
  const canSwitchDepartments = () => {
    return isSuperAdmin() || isRegionManager || isSuperUser || isRegionUser;
  };

  // Helper function to check if user can create departments
  const canCreateDepartments = () => {
    return isSuperAdmin() || isRegionManager;
  };

  // Function to create a new department with sample data
  const createNewDepartment = async (newDepartment) => {
    if (!isSuperAdmin() && !isRegionManager) {
      toast.error('Only super admins and region managers can create departments');
      return false;
    }

    try {
      const db = getDatabase();

      // Create department structure with sample data
      const departmentStructure = createEmptyDepartmentStructure(true); // true to include sample data

      // Add department name to the structure
      departmentStructure.department_name = newDepartment.name;

      // Add the new department to the database
      await set(ref(db, `departments/${newDepartment.id}`), departmentStructure);

      // If region-manager created the department, add it to their managedDepartments
      if (isRegionManager && currentUser) {
        const userRef = ref(db, `users/${currentUser.uid}`);
        const userSnapshot = await get(userRef);

        if (userSnapshot.exists()) {
          const userData = userSnapshot.val();
          const currentManagedDepts = userData.managedDepartments || [];

          // Add the new department to managedDepartments if not already included
          if (!currentManagedDepts.includes(newDepartment.id)) {
            const updatedManagedDepts = [...currentManagedDepts, newDepartment.id];
            await set(userRef, {
              ...userData,
              managedDepartments: updatedManagedDepts
            });

            // Update local state
            setManagedDepartments(updatedManagedDepts);
            console.log(`Added department ${newDepartment.id} to region-manager's managedDepartments`);
          }
        }
      }

      toast.success(`Department "${newDepartment.name}" created successfully with sample data!`);

      // Refresh departments list by re-fetching
      const departmentsRef = ref(db, 'departments');
      const deptSnapshot = await get(departmentsRef);

      if (deptSnapshot.exists()) {
        const deptData = deptSnapshot.val();
        const deptList = Object.entries(deptData).map(([deptId, dept]) => ({
          id: deptId,
          name: dept.department_name || deptId,
        }));

        setDepartments(deptList);

        // Select the newly created department
        setSelectedDepartment(newDepartment.id);
      }

      return true;
    } catch (error) {
      console.error('Error creating department:', error);
      toast.error(`Failed to create department: ${error.message}`);
      return false;
    }
  };

  const hasAccessToDepartment = (departmentId) => {
    if (isSuperAdmin() || isSuperUser) {
      return selectedDepartment ? selectedDepartment === departmentId : true;
    }
    if (isRegionManager || isRegionUser) {
      return managedDepartments.includes(departmentId);
    }
    if (userRole === 'admin' || userRole === 'user') {
      return userDepartment === departmentId;
    }
    return false;
  };

  const hasWriteAccessToDepartment = (deptId) => {
    if (!currentUser || !deptId) return false;

    if (isSuperAdmin()) {
      return true;
    }
    if (isRegionManager) {
      return managedDepartments.includes(deptId);
    }
    if (userRole === 'admin') {
      return userDepartment === deptId;
    }
    // super-user and region-user have NO write access
    return false;
  };

  const value = {
    currentUser,
    userRole,
    isSuperAdmin,
    isRegionManager,
    isRegionManagerRole,
    isSuperUser,
    isRegionUser,
    isSuperUserRole,
    isRegionUserRole,
    canSwitchDepartments,
    canCreateDepartments,
    selectedDepartment,
    setSelectedDepartment,
    userDepartment,
    setUserDepartment,
    departments,
    managedDepartments,
    loading,
    hasAccessToDepartment,
    hasWriteAccessToDepartment,
    createNewDepartment,
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};