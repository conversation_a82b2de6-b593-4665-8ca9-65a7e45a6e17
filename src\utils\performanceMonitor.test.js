// src/utils/performanceMonitor.test.js
import performanceMonitor from './performanceMonitor';

describe('PerformanceMonitor', () => {
  beforeEach(() => {
    // Clear measurements before each test
    performanceMonitor.clearMeasurements();
    
    // Mock console methods
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
    
    // Mock performance.now()
    jest.spyOn(performance, 'now')
      .mockImplementationOnce(() => 1000)  // First call (start)
      .mockImplementationOnce(() => 1100); // Second call (end)
  });
  
  afterEach(() => {
    // Restore mocks
    jest.restoreAllMocks();
  });
  
  test('measures operation duration correctly', () => {
    performanceMonitor.startMeasure('testOperation');
    performanceMonitor.endMeasure('testOperation');
    
    const report = performanceMonitor.getReport();
    
    expect(report.testOperation).toBeDefined();
    expect(report.testOperation.count).toBe(1);
    expect(report.testOperation.averageDuration).toBe(100);
    expect(report.testOperation.minDuration).toBe(100);
    expect(report.testOperation.maxDuration).toBe(100);
    expect(report.testOperation.totalDuration).toBe(100);
  });
  
  test('accumulates multiple measurements for the same operation', () => {
    // First measurement: 100ms
    performanceMonitor.startMeasure('testOperation');
    performanceMonitor.endMeasure('testOperation');
    
    // Mock performance.now() for second measurement: 200ms
    jest.spyOn(performance, 'now')
      .mockImplementationOnce(() => 2000)  // Start
      .mockImplementationOnce(() => 2200); // End
    
    // Second measurement
    performanceMonitor.startMeasure('testOperation');
    performanceMonitor.endMeasure('testOperation');
    
    const report = performanceMonitor.getReport();
    
    expect(report.testOperation.count).toBe(2);
    expect(report.testOperation.averageDuration).toBe(150); // (100 + 200) / 2
    expect(report.testOperation.minDuration).toBe(100);
    expect(report.testOperation.maxDuration).toBe(200);
    expect(report.testOperation.totalDuration).toBe(300);
  });
  
  test('logs warnings when duration exceeds threshold', () => {
    performanceMonitor.setLogLevel('warn');
    
    // Measurement that exceeds the default threshold (100ms)
    jest.spyOn(performance, 'now')
      .mockImplementationOnce(() => 1000)  // Start
      .mockImplementationOnce(() => 1200); // End (200ms)
    
    performanceMonitor.startMeasure('slowOperation');
    performanceMonitor.endMeasure('slowOperation');
    
    expect(console.warn).toHaveBeenCalled();
  });
  
  test('does not log when duration is below threshold', () => {
    performanceMonitor.setLogLevel('warn');
    
    // Measurement that does not exceed the default threshold (100ms)
    jest.spyOn(performance, 'now')
      .mockImplementationOnce(() => 1000)  // Start
      .mockImplementationOnce(() => 1050); // End (50ms)
    
    performanceMonitor.startMeasure('fastOperation');
    performanceMonitor.endMeasure('fastOperation', 100);
    
    expect(console.warn).not.toHaveBeenCalled();
  });
  
  test('respects log level setting', () => {
    // Set log level to 'error'
    performanceMonitor.setLogLevel('error');
    
    // Measurement that exceeds threshold
    jest.spyOn(performance, 'now')
      .mockImplementationOnce(() => 1000)  // Start
      .mockImplementationOnce(() => 1200); // End (200ms)
    
    performanceMonitor.startMeasure('slowOperation');
    performanceMonitor.endMeasure('slowOperation');
    
    // Should use console.error instead of console.warn
    expect(console.warn).not.toHaveBeenCalled();
    expect(console.error).toHaveBeenCalled();
  });
  
  test('can be disabled', () => {
    // Disable performance monitoring
    performanceMonitor.setEnabled(false);
    
    performanceMonitor.startMeasure('testOperation');
    performanceMonitor.endMeasure('testOperation');
    
    const report = performanceMonitor.getReport();
    
    // Report should be empty
    expect(Object.keys(report).length).toBe(0);
  });
  
  test('measures function execution', () => {
    const testFn = () => {
      return 'result';
    };
    
    const result = performanceMonitor.measureFunction(testFn, 'testFunction');
    
    // Function should execute and return result
    expect(result).toBe('result');
    
    // Measurement should be recorded
    const report = performanceMonitor.getReport();
    expect(report.testFunction).toBeDefined();
  });
  
  test('measures async function execution', async () => {
    const testAsyncFn = async () => {
      return 'async result';
    };
    
    const result = await performanceMonitor.measureAsyncFunction(testAsyncFn, 'testAsyncFunction');
    
    // Function should execute and return result
    expect(result).toBe('async result');
    
    // Measurement should be recorded
    const report = performanceMonitor.getReport();
    expect(report.testAsyncFunction).toBeDefined();
  });
});
