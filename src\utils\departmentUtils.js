/**
 * Creates a department structure with sample data
 * @param {boolean} includeSampleData - Whether to include sample data
 * @returns {Object} Department structure with sample data
 */
export const createEmptyDepartmentStructure = (includeSampleData = true) => {
  // Get current year and month
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-indexed

  // Base structure
  const structure = {
    department_name: "",
    config: {
      maxAdmins: 2  // Default value
    },
    calendar_markers: {
      holiday_markers: {},
      special_day_markers: {},
      weekday_markers: {}
    },
    contactInfo: {},
    employees: {},
    rosters: {
      [currentYear]: {}
    },
    rostersDraft: {
      [currentYear]: {}
    },
    shifts: {},
    telegram_subscribers: {},
    whatsapp_subscribers: {}
  };

  // If includeSampleData is false, return the empty structure
  if (!includeSampleData) {
    return structure;
  }

  // Add sample employee
  const sampleEmployeeId = "EMP001";
  structure.employees[sampleEmployeeId] = {
    createdAt: new Date().toISOString(),
    email: "",
    employeeId: sampleEmployeeId,
    name: "Sample Employee",
    phone: "",
    position: "Staff",
    rowIndex: 1,
    srcNumber: sampleEmployeeId,
    updatedAt: new Date().toISOString()
  };

  // Add sample shifts
  structure.shifts = {
    "D": {
      color: "#ffffff",
      description: "Day Shift",
      endTime: "17:30",
      hours: 10,
      name: "D",
      startTime: "8:00"
    },
    "N": {
      color: "#ddebf7",
      description: "Night Shift",
      endTime: "8:00",
      hours: 12,
      name: "N",
      startTime: "20:00"
    },
    "O": {
      color: "#93c47d",
      description: "Off",
      endTime: "0:00",
      hours: 0,
      name: "O",
      startTime: "0:00"
    },
    "X": {
      color: "#ffff00",
      description: "Leave",
      endTime: "",
      hours: 0,
      name: "X",
      startTime: ""
    }
  };

  // Create sample roster data for current month
  const daysInMonth = new Date(currentYear, currentMonth, 0).getDate();
  const shifts = Array(daysInMonth + 1).fill(null); // +1 because shifts array is 1-indexed

  // Fill shifts with a pattern (Day shift, Night shift, Off)
  for (let i = 1; i <= daysInMonth; i++) {
    const dayOfWeek = new Date(currentYear, currentMonth - 1, i).getDay();
    if (dayOfWeek === 5 || dayOfWeek === 6) { // Friday or Saturday
      shifts[i] = "O"; // Weekend off
    } else {
      shifts[i] = i % 3 === 0 ? "N" : (i % 3 === 1 ? "D" : "O");
    }
  }

  // Add sample roster data
  const rosterData = {
    employeeShifts: {
      [sampleEmployeeId]: {
        aircraft: "0:00",
        leaveDays: 0,
        name: "Sample Employee",
        office: "0:00",
        overtime: "0:00",
        overtimeDays: 0,
        position: "Staff",
        rowIndex: 1,
        shifts: shifts,
        sickDays: 0,
        srcNumber: sampleEmployeeId,
        workDays: shifts.filter(s => s !== "O" && s !== "X" && s !== null).length,
        workTime: `${shifts.filter(s => s !== "O" && s !== "X" && s !== null).length * 8}:00`
      }
    },
    month: currentMonth,
    year: currentYear,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  // Add roster data to both live and draft rosters
  structure.rosters[currentYear][currentMonth] = rosterData;
  structure.rostersDraft[currentYear][currentMonth] = JSON.parse(JSON.stringify(rosterData)); // Deep copy

  return structure;
};
