{"departments": {"dept_001": {"whatsapp_subscibers": {"\"+962792812995\"": {"status": "active", "subscribed": true}}, "whatsapp_subscribers": {"+962792812995": {"subscribed": true}}, "shifts": {"D": {"color": "#ffffff", "description": "Day Shift", "endTime": "17:30", "hours": 10, "name": "D", "startTime": "8:00"}, "D1": {"color": "#fff2af", "description": "Day Shift 1", "endTime": "17:00", "hours": 10, "name": "D1", "startTime": "6:00"}, "D2": {"color": "#4a90e2", "description": "Day shift 2", "endTime": "18:00", "hours": 10, "name": "D2", "startTime": "8:00"}, "DO": {"color": "#ffffff", "description": "Day overtime ", "endTime": "17:00", "hours": 0, "name": "DO", "startTime": "08:10"}, "DT": {"color": "#808080", "description": "Duty Travel", "endTime": "18:00", "hours": 8, "name": "DT", "startTime": "10:00"}, "N1": {"color": "#ddebf7", "description": "Night Shift 1", "endTime": "3:30", "hours": 12, "name": "N1", "startTime": "16:30"}, "N2": {"color": "#ddebf7", "description": "Night Shift 2", "endTime": "5:00", "hours": 12, "name": "N2", "startTime": "18:00"}, "O": {"color": "#93c47d", "description": "Off", "endTime": "0:00", "hours": 0, "name": "O", "startTime": "0:00"}, "SL": {"color": "#6d9eeb", "description": "Sick Leave", "endTime": "", "hours": 0, "name": "SL", "startTime": ""}, "TR": {"color": "#00ffff", "description": "Training ", "endTime": "15:00", "hours": 6, "name": "TR", "startTime": "9:00"}, "X": {"color": "#ffff00", "description": "Leave", "endTime": "", "hours": 0, "name": "X", "startTime": ""}}, "telegram_subscribers": {"1411377937": {"status": "active", "subscribed": false}, "6231238168": {"status": "active", "subscribed": true}, "7741742088": {"status": "active", "subscribed": true}}, "calendar_markers": {"holiday_markers": {"-OPpKc_3Tcvq_-eD04-0": {"color": "#4a5899", "createdAt": "2025-05-09T13:57:36.577Z", "endDate": {"day": 29, "month": 12, "year": 2026}, "name": "Christmas ", "recurring": true, "startDate": {"day": 25, "month": 12, "year": 2026}, "updatedAt": "2025-05-10T08:57:59.286Z"}, "-OPpLGcCjCz7YIEdUXr4": {"color": "#7d9e2c", "createdAt": "2025-05-09T14:00:24.628Z", "endDate": {"day": 10, "month": 6, "year": 2025}, "name": "EDI", "recurring": false, "startDate": {"day": 5, "month": 6, "year": 2025}, "updatedAt": "2025-05-09T14:00:24.628Z"}}, "special_day_markers": {"-OPpR2ZrRVV2C9OXTvQR": {"color": "#d08eea", "createdAt": "2025-05-09T14:25:39.924Z", "endDate": {"day": 29, "month": 3, "year": 2025}, "name": "<PERSON><PERSON>", "startDate": {"day": 1, "month": 3, "year": 2025}, "updatedAt": "2025-05-09T14:25:39.924Z"}}, "weekday_markers": {"-OPpJiHPcNAOfUj6Bple": {"color": "#d35555", "createdAt": "2025-05-09T13:53:37.814Z", "name": "Friday", "updatedAt": "2025-05-09T13:53:51.580Z", "weekday": "Friday"}, "-OPqIZW5tjufEsTklqxu": {"color": "#d35555", "createdAt": "2025-05-09T18:28:12.224Z", "name": "Saturday", "updatedAt": "2025-05-10T10:41:56.803Z", "weekday": "Saturday"}}}, "config": {"maxAdmins": 2}, "contactInfo": {"AdminLogistics": {"phone": "(+973) 33 9815 62"}, "DutyPhone1": {"phone": "(+973) 33 6464 21"}, "DutyPhone2": {"phone": "(+973) 33 6464 21"}, "DutyPhone3": {"phone": "(+973) 33 544211"}, "Manager": {"phone": "(+973) 33 6464 20"}, "new": {"phone": "+9627012812997"}}, "employees": {"T00003": {"createdAt": "2025-04-18T09:46:03.454Z", "email": "", "employeeId": "T00003", "name": "Employee1", "phone": "", "position": "LMM", "rowIndex": 1, "srcNumber": "T00003", "updatedAt": "2025-04-18T09:46:03.454Z"}, "T00004": {"createdAt": "2025-04-18T09:46:03.848Z", "email": "", "employeeId": "T00004", "name": "Employee2", "phone": "", "position": "A.LC", "rowIndex": 2, "srcNumber": "T00004", "updatedAt": "2025-04-18T09:46:03.848Z"}, "T00005": {"createdAt": "2025-04-18T09:46:04.241Z", "email": "", "employeeId": "T00005", "name": "Employee3", "phone": "", "position": "L.C", "rowIndex": 3, "srcNumber": "T00005", "updatedAt": "2025-04-18T09:46:04.241Z"}, "T00006": {"createdAt": "2025-04-18T09:46:04.634Z", "email": "", "employeeId": "T00006", "name": "Employee4", "phone": "", "position": "MTL", "rowIndex": 4, "srcNumber": "3806", "updatedAt": "2025-04-21T18:51:21.661Z"}, "T00007": {"createdAt": "2025-04-18T09:46:05.037Z", "email": "", "employeeId": "T00007", "name": "Employee5", "phone": "", "position": "MTL", "rowIndex": 5, "srcNumber": "3808", "updatedAt": "2025-04-18T09:46:05.037Z"}}, "rosters": {"2024": {"createdAt": "2025-06-01T00:00:00Z", "updatedAt": "2025-06-01T00:00:00Z", "year": 2024, "4": {"employeeShifts": {"T00003": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee1", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "LMM", "rowIndex": 1, "shifts": [null, "D", "D", "D", "O", "O", "D1", "D", "D", "D", "D", "O", "O", "X", "N2", "DT", "D2", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00003", "workDays": 21, "workTime": "5040:00"}, "T00004": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee2", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "A.LC", "rowIndex": 2, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "TR", "TR", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00004", "workDays": 22, "workTime": "208:00"}, "T00005": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee3", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "L.C", "rowIndex": 3, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "D", "D", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00005", "workDays": 22, "workTime": "216:00"}, "T00006": {"aircraft": "79:12", "leaveDays": 1, "name": "Employee4", "office": "118:48", "overtime": "0:00", "overtimeDays": 0, "position": "LMS", "rowIndex": 4, "shifts": [null, "D1", "D1", "O", "O", "D1", "X", "D1", "O", "D1", "O", "O", "O", "D1", "D", "O", "O", "D", "D", "D", "D1", "D1", "D1", "D1", "D1", "O", "O", "O", "D1", "D1", "D1"], "sickDays": 0, "srcNumber": "3806", "workDays": 18, "workTime": "180:00"}, "T00007": {"aircraft": "165:00", "leaveDays": 0, "name": "Employee5", "office": "55:00", "overtime": "0:00", "overtimeDays": 0, "position": "MTL", "rowIndex": 5, "shifts": [null, "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O"], "sickDays": 0, "srcNumber": "3808", "workDays": 20, "workTime": "224:00"}}, "month": 4, "year": 2024, "createdAt": "2024-04-01T00:00:00Z", "updatedAt": "2025-04-25T08:21:17.510Z"}}, "2025": {}}, "rostersDraft": {"2024": {}, "2025": {}}}, "dept_002": {}, "dept_003": {}, "dept_004": {}}, "users": {"gPZNww5dXOhIudce2JP76e2vBuK2": {"email": "<EMAIL>", "role": "admin", "department": "dept_001", "uid": "gPZNww5dXOhIudce2JP76e2vBuK2"}}}