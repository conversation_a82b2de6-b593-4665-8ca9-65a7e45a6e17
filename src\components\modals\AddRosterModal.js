// src/components/modals/AddRosterModal.js
import React from 'react';

/**
 * AddRosterModal component - Modal for adding a new roster
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {Object} props.nextRoster - The next roster to add
 * @param {Object} props.addRosterResult - Result of adding a roster
 * @param {boolean} props.addRosterLoading - Whether adding a roster is in progress
 * @param {Function} props.closeModal - Function to close the modal
 * @param {Function} props.confirmAddRoster - Function to confirm adding a roster
 * @returns {JSX.Element|null} AddRosterModal component or null if not open
 */
const AddRosterModal = ({
  isOpen,
  nextRoster,
  addRosterResult,
  addRosterLoading,
  closeModal,
  confirmAddRoster,
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h2 className="text-xl font-bold mb-4">Create New Roster</h2>
        {!addRosterResult ? (
          <>
            <p className="text-sm text-gray-700 mb-4">
              You are about to create a roster for{" "}
              <strong>
                {new Date(nextRoster?.year, nextRoster?.month - 1).toLocaleString('default', { month: 'long' })}{" "}
                {nextRoster?.year}
              </strong>
              , copying data from the previous month.
            </p>
            <p className="text-sm text-gray-700 mb-4">
              This will initialize the roster with adjusted shift data. Do you want to proceed?
            </p>
            {addRosterLoading && (
              <div className="flex justify-center mb-4">
                <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-600"></div>
              </div>
            )}
            <div className="flex justify-end space-x-2">
              <button
                onClick={closeModal}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                disabled={addRosterLoading}
              >
                Cancel
              </button>
              <button
                onClick={confirmAddRoster}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                disabled={addRosterLoading}
              >
                Confirm
              </button>
            </div>
          </>
        ) : addRosterResult.success ? (
          <>
            <p className="text-sm text-green-600 mb-4">
              Roster for{" "}
              <strong>
                {new Date(nextRoster?.year, nextRoster?.month - 1).toLocaleString('default', { month: 'long' })}{" "}
                {nextRoster?.year}
              </strong>{" "}
              was successfully created.
            </p>
            <div className="flex justify-end">
              <button
                onClick={closeModal}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Close
              </button>
            </div>
          </>
        ) : (
          <>
            <p className="text-sm text-red-600 mb-4">
              Failed to create roster: {addRosterResult.error}
            </p>
            <div className="flex justify-end space-x-2">
              <button
                onClick={closeModal}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
              >
                Cancel
              </button>
              <button
                onClick={confirmAddRoster}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Retry
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default AddRosterModal;
