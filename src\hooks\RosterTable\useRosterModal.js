// src/hooks/RosterTable/useRosterModal.js
import { useState, useEffect } from 'react';

/**
 * Custom hook to manage roster modal state
 * 
 * @param {Array} rosterData - The roster data
 * @param {number} daysInMonth - Number of days in the month
 * @returns {Object} Modal state and functions
 */
const useRosterModal = (rosterData, daysInMonth) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalTechId, setModalTechId] = useState('');
  const [modalStartDay, setModalStartDay] = useState(1);
  const [modalEndDay, setModalEndDay] = useState(1);
  const [modalShifts, setModalShifts] = useState({});

  // Update modal shifts when start or end day changes
  useEffect(() => {
    if (!modalTechId || !isModalOpen) return;
    
    const tech = rosterData.find(t => t.employeeId === modalTechId);
    if (!tech) return;

    const start = parseInt(modalStartDay);
    const end = parseInt(modalEndDay);

    // Use functional update to avoid dependency on modalShifts
    setModalShifts(prevShifts => {
      const newShifts = {};
      for (let day = start; day <= end && day <= daysInMonth; day++) {
        const dayStr = day.toString();
        // Preserve existing selections in the modal if they exist
        newShifts[dayStr] = prevShifts[dayStr] || tech[dayStr] || '';
      }
      return newShifts;
    });
  }, [modalStartDay, modalEndDay, modalTechId, isModalOpen, rosterData, daysInMonth]);

  // Open modal with initial data
  const openModal = (techId, day) => {
    const tech = rosterData.find(t => t.employeeId === techId);
    if (tech) {
      const dayNum = parseInt(day);
      setModalTechId(techId);
      setModalStartDay(dayNum);
      setModalEndDay(dayNum);
      // Clear previous shifts and set only the current day's shift
      setModalShifts({ [day]: tech[day] || '' });
      setIsModalOpen(true);
    }
  };

  // Close modal and reset state
  const closeModal = () => {
    setIsModalOpen(false);
    setModalTechId('');
    setModalStartDay(1);
    setModalEndDay(1);
    setModalShifts({});
  };

  // Get modal data
  const getModalData = () => ({
    modalTechId,
    modalStartDay,
    modalEndDay,
    modalShifts
  });

  return {
    isModalOpen,
    modalTechId,
    modalStartDay,
    modalEndDay,
    modalShifts,
    setModalTechId,
    setModalStartDay,
    setModalEndDay,
    setModalShifts,
    openModal,
    closeModal,
    getModalData
  };
};

export default useRosterModal;
