// src/hooks/RosterTable/useRosterHistory.js
import { useState, useCallback, useContext, useEffect } from 'react';
import updateRosterDataGen from '../../updateRosterDataGen.js';
import { AuthContext } from '../../components/AuthProvider';

/**
 * Custom hook to manage roster history for undo functionality
 *
 * @param {Array} initialRosterData - Initial roster data
 * @param {number} year - Current year
 * @param {number} month - Current month
 * @param {number} daysInMonth - Number of days in the month
 * @returns {Object} History state and functions
 */
const useRosterHistory = (initialRosterData, year, month, daysInMonth) => {
  // Get auth context values
  const authContext = useContext(AuthContext);

  const [history, setHistory] = useState([initialRosterData]);
  const [historyIndex, setHistoryIndex] = useState(0);
  const [changedCells, setChangedCells] = useState([]);
  const [isSaving, setIsSaving] = useState(false);

  // Reset history when initial roster data changes (e.g., when switching months)
  useEffect(() => {
    console.log('🔄 Resetting history with new roster data:', initialRosterData?.length || 0, 'employees');
    setHistory([initialRosterData]);
    setHistoryIndex(0);
    setChangedCells([]);
  }, [initialRosterData]);

  // Add a new state to history
  const addToHistory = useCallback((newRosterData) => {
    const newHistory = [...history.slice(0, historyIndex + 1), newRosterData];
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
    return newRosterData;
  }, [history, historyIndex]);

  // Undo the last change
  const handleUndo = useCallback(() => {
    if (historyIndex <= 0) return null;

    const newIndex = historyIndex - 1;
    setHistoryIndex(newIndex);
    const previousRosterData = [...history[newIndex]];

    const currentRosterData = history[newIndex + 1];
    const newChangedCells = [...changedCells];

    currentRosterData.forEach((tech, idx) => {
      const prevTech = previousRosterData[idx];
      for (let day = 1; day <= daysInMonth; day++) {
        const dayStr = day.toString();
        if (tech[dayStr] !== prevTech[dayStr]) {
          const changeIndex = newChangedCells.findIndex(cell =>
            cell.techId === tech.employeeId && cell.day === dayStr
          );
          if (changeIndex !== -1) {
            newChangedCells.splice(changeIndex, 1);
          }
        }
      }
    });

    setChangedCells(newChangedCells);
    return previousRosterData;
  }, [history, historyIndex, changedCells, daysInMonth]);

  // Save changes
  const handleSave = useCallback(async (rosterData, isPublish = false) => {
    if (isSaving) return false;
    setIsSaving(true);

    try {
      // Pass authContext and selectedDepartment to updateRosterDataGen
      await updateRosterDataGen(year, month, rosterData, isPublish, authContext, authContext.selectedDepartment);
      setHistory([rosterData]);
      setHistoryIndex(0);
      setChangedCells([]);
      return true;
    } catch (error) {
      console.error('Failed to save roster data:', error);
      alert(`Failed to save roster data: ${error.message}`);
      return false;
    } finally {
      setIsSaving(false);
    }
  }, [isSaving, year, month, authContext]);

  // Track cell changes
  const trackCellChange = useCallback((techId, day) => {
    setChangedCells(prev => {
      if (!prev.some(cell => cell.techId === techId && cell.day === day)) {
        return [...prev, { techId, day }];
      }
      return prev;
    });
  }, []);

  return {
    history,
    historyIndex,
    changedCells,
    isSaving,
    addToHistory,
    handleUndo,
    handleSave,
    trackCellChange,
    setChangedCells
  };
};

export default useRosterHistory;
