// src/hooks/useEmployeeReorder.js
import { useState, useCallback, useContext } from 'react';
import { AuthContext } from '../components/AuthProvider';
import { previewEmployeeUpdate, previewEmployeeUpdateFromMonth, updateEmployee } from '../contexts/actions/employeeActions';
import { useDataDispatch } from '../contexts/DataContext';

/**
 * Simple hook for employee reordering with preview modal
 * @param {number} year - The year of the roster being viewed
 * @param {number} month - The month of the roster being viewed
 */
const useEmployeeReorder = (year, month) => {
  const { selectedDepartment, isSuperAdmin, isRegionManager, isSuperUser, isRegionUser } = useContext(AuthContext);
  const dispatch = useDataDispatch();

  // Modal state
  const [isReorderModalOpen, setIsReorderModalOpen] = useState(false);
  const [reorderPreviewData, setReorderPreviewData] = useState(null);
  const [reorderResult, setReorderResult] = useState(null);
  const [isReordering, setIsReordering] = useState(false);

  // Helper function to determine if we should use selectedDepartment
  const shouldUseSelectedDepartment = () => isSuperAdmin() || isRegionManager || isSuperUser || isRegionUser;

  // Handle employee reorder request
  const handleEmployeeReorder = useCallback(async (dragData, targetEmployee) => {
    try {
      const { employeeId, employeeName, currentRowIndex } = dragData;
      const newRowIndex = targetEmployee.rowIndex;

      // Don't do anything if dropping in the same position
      if (currentRowIndex === newRowIndex) {
        console.log('Same position, no reorder needed');
        return;
      }

      console.log(`Reordering: ${employeeName} from position ${currentRowIndex} to ${newRowIndex}`);
      console.log('Debug - Employee ID:', employeeId);
      console.log('Debug - Department:', shouldUseSelectedDepartment() ? selectedDepartment : 'user department');
      console.log('Debug - Year/Month:', year, month);

      // Determine department to use for preview (same logic as update)
      const deptToUse = shouldUseSelectedDepartment() ? selectedDepartment : null;

      // Get preview data and show confirmation modal - use the month-specific preview
      const preview = await previewEmployeeUpdateFromMonth(employeeId, { rowIndex: newRowIndex }, year, month, deptToUse);
      setReorderPreviewData({
        ...preview,
        draggedEmployee: { employeeId, employeeName, currentRowIndex },
        targetEmployee: { employeeId: targetEmployee.employeeId, employeeName: targetEmployee.Name, rowIndex: newRowIndex }
      });
      setReorderResult(null);
      setIsReorderModalOpen(true);
    } catch (err) {
      console.error('Failed to preview employee reorder:', err);
      alert('Failed to preview employee reorder: ' + err.message);
    }
  }, []);

  // Confirm employee reorder
  const confirmReorder = useCallback(async () => {
    if (!reorderPreviewData) return;

    setIsReordering(true);
    try {
      const { draggedEmployee, newRowIndex } = reorderPreviewData;

      // Determine department to use
      const deptToUse = shouldUseSelectedDepartment() ? selectedDepartment : null;

      // Call the actual updateEmployee function
      // This will update future draft rosters starting from current date
      const success = await updateEmployee(
        dispatch,
        draggedEmployee.employeeId,
        { rowIndex: newRowIndex },
        deptToUse
      );

      if (success) {
        setReorderResult({ success: true });

        // The roster data will be automatically updated through the onValue listener
        // so the UI will update automatically with the new employee order
      } else {
        setReorderResult({ success: false, error: 'Failed to reorder employee' });
      }
    } catch (err) {
      setReorderResult({ success: false, error: err.message });
    } finally {
      setIsReordering(false);
    }
  }, [reorderPreviewData, dispatch, selectedDepartment, shouldUseSelectedDepartment, year, month]);

  // Close modal
  const closeReorderModal = useCallback(() => {
    setIsReorderModalOpen(false);
    setReorderPreviewData(null);
    setReorderResult(null);
  }, []);

  return {
    // Modal state
    isReorderModalOpen,
    reorderPreviewData,
    reorderResult,
    isReordering,

    // Functions
    handleEmployeeReorder,
    confirmReorder,
    closeReorderModal
  };
};

export default useEmployeeReorder;
