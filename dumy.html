<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Employee Documentation Control</title>
  <script src="https://cdn.jsdelivr.net/npm/react@18.2.0/umd/react.development.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/react-dom@18.2.0/umd/react-dom.development.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/@babel/standalone@7.22.5/babel.min.js"></script>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body>
  <div id="root"></div>

  <script type="text/babel">
    const { useState, useEffect } = React;

    const EmployeeDocs = () => {
      // Dummy employee data
      const [employee, setEmployee] = useState({
        id: "E0001",
        name: "<PERSON>",
        documents: [
          { type: "Passport", number: "********", expirationDate: "2025-06-15", attachment: null },
          { type: "Residence Permit", number: "RP987654", expirationDate: "2025-05-01", attachment: null },
          { type: "Driving Permit", number: "DL456789", expirationDate: "2025-08-20", attachment: null },
          { type: "Airport Pass", number: "AP654321", expirationDate: "2025-04-30", attachment: null },
          { type: "Work Permit", number: "WP112233", expirationDate: "2025-07-10", attachment: null },
          { type: "Work License", number: "WL445566", expirationDate: "2025-03-15", attachment: null },
        ],
      });

      const [notifications, setNotifications] = useState([]);

      // Function to calculate days until expiration
      const getDaysUntilExpiration = (expirationDate) => {
        const today = new Date();
        const expDate = new Date(expirationDate);
        const diffTime = expDate - today;
        return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      };

      // Function to determine status and notifications
      const checkExpirationStatus = () => {
        const today = new Date();
        const newNotifications = [];

        const updatedDocs = employee.documents.map((doc) => {
          const daysUntilExp = getDaysUntilExpiration(doc.expirationDate);
          let status = "Valid";
          if (daysUntilExp <= 0) {
            status = "Expired";
            newNotifications.push(`${doc.type} (${doc.number}) has expired!`);
          } else if (daysUntilExp <= 30) {
            status = "Critical";
            newNotifications.push(`${doc.type} (${doc.number}) expires in ${daysUntilExp} days!`);
          } else if (daysUntilExp <= 90) {
            status = "Expiring Soon";
            newNotifications.push(`${doc.type} (${doc.number}) expires in ${daysUntilExp} days.`);
          }
          return { ...doc, status, daysUntilExp };
        });

        setEmployee({ ...employee, documents: updatedDocs });
        setNotifications(newNotifications);
      };

      // Run expiration check on mount
      useEffect(() => {
        checkExpirationStatus();
      }, []);

      // Mock file upload
      const handleFileUpload = (docIndex) => {
        // Simulate file upload
        const updatedDocs = [...employee.documents];
        updatedDocs[docIndex].attachment = `mock-file-${docIndex}.pdf`;
        setEmployee({ ...employee, documents: updatedDocs });
        alert(`File uploaded for ${updatedDocs[docIndex].type}: ${updatedDocs[docIndex].attachment}`);
      };

      // Remove attachment
      const handleRemoveAttachment = (docIndex) => {
        const updatedDocs = [...employee.documents];
        updatedDocs[docIndex].attachment = null;
        setEmployee({ ...employee, documents: updatedDocs });
        alert(`Attachment removed for ${updatedDocs[docIndex].type}`);
      };

      return (
        <div className="container mx-auto p-6">
          <h1 className="text-3xl font-bold text-gray-800 mb-6">
            Employee Documentation Control
          </h1>

          {/* Employee Info */}
          <div className="bg-white shadow-md rounded-lg p-4 mb-6">
            <h2 className="text-xl font-semibold text-gray-700">
              Employee: {employee.name} (ID: {employee.id})
            </h2>
          </div>

          {/* Notifications */}
          {notifications.length > 0 && (
            <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-6 rounded">
              <h3 className="font-semibold">Expiration Notifications</h3>
              <ul className="list-disc pl-5">
                {notifications.map((notification, index) => (
                  <li key={index}>{notification}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Documents Table */}
          <div className="bg-white shadow-md rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Document Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Document Number
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Expiration Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Attachment
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {employee.documents.map((doc, index) => (
                  <tr
                    key={index}
                    className={
                      doc.status === "Expired"
                        ? "bg-red-100"
                        : doc.status === "Critical"
                        ? "bg-orange-100"
                        : doc.status === "Expiring Soon"
                        ? "bg-yellow-100"
                        : ""
                    }
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {doc.type}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {doc.number}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {doc.expirationDate}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <span
                        className={
                          doc.status === "Expired"
                            ? "text-red-600 font-semibold"
                            : doc.status === "Critical"
                            ? "text-orange-600 font-semibold"
                            : doc.status === "Expiring Soon"
                            ? "text-yellow-600 font-semibold"
                            : "text-green-600 font-semibold"
                        }
                      >
                        {doc.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {doc.attachment ? (
                        <span className="text-blue-600">{doc.attachment}</span>
                      ) : (
                        "No attachment"
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      {doc.attachment ? (
                        <button
                          onClick={() => handleRemoveAttachment(index)}
                          className="text-red-600 hover:text-red-800 mr-2"
                        >
                          Remove
                        </button>
                      ) : (
                        <button
                          onClick={() => handleFileUpload(index)}
                          className="text-blue-600 hover:text-blue-800 mr-2"
                        >
                          Upload
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      );
    };

    // Render the app
    const root = ReactDOM.createRoot(document.getElementById('root'));
    root.render(<EmployeeDocs />);
  </script>
</body>
</html>