
const getDepartmentPath = (isDepartmentSpecific = true, selectedDepartment = null, userRole, userDepartment) => {
  console.log(`getDepartmentPath: isDepartmentSpecific: ${isDepartmentSpecific}, selectedDepartment: ${selectedDepartment}, userRole: ${userRole}, userDepartment: ${userDepartment}`);

  // If not department-specific or user not logged in, return empty path (${})
  if (!isDepartmentSpecific || userRole === null) {
    console.log('getDepartmentPath: Not department specific or user not logged in, returning empty path');
    return '';
  }

  // Super-admin uses selectedDepartment if provided, otherwise their own department (if they have one)
  if (userRole === 'super-admin') {
    if (selectedDepartment) {
      console.log(`getDepartmentPath: Super-admin, using selectedDepartment: ${selectedDepartment}`);
      return `departments/${selectedDepartment}/`;
    }
  } else { // Regular users/admins
    return userDepartment ? `departments/${userDepartment}/` : '';
  }
  return '';
};

export default getDepartmentPath;