// src/components/RosterTable/RosterFooter.js
import React from 'react';

/**
 * RosterFooter component - Renders the footer of the roster table with totals
 * 
 * @param {Object} props - Component props
 * @param {Object} props.totals - Totals for various metrics
 * @param {number} props.daysInMonth - Number of days in the month
 * @returns {JSX.Element} RosterFooter component
 */
const RosterFooter = ({ totals, daysInMonth }) => {
  return (
    <tfoot>
      <tr style={{ backgroundColor: '#e5e7eb', fontWeight: 'bold' }}>
        <td colSpan={3} style={{ border: '1px solid #d1d5db', padding: '0.5rem 1rem', textAlign: 'right' }}>Totals:</td>
        {Array.from({ length: daysInMonth }, (_, i) => (
          <td key={i} style={{ border: '1px solid #d1d5db', height: '40px' }}></td>
        ))}
        <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', height: '40px' }}>{totals['Leave days']}</td>
        <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', height: '40px' }}>{totals['Sick days']}</td>
        <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', height: '40px' }}>{totals['Work days']}</td>
        <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', height: '40px' }}>{totals['Work Time']}</td>
        <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', height: '40px' }}>{totals['Overtime days']}</td>
        <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', height: '40px' }}>{totals['Overtime']}</td>
        <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', height: '40px' }}>{totals['Aircraft']}</td>
        <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', height: '40px' }}>{totals['Office']}</td>
      </tr>
    </tfoot>
  );
};

export default RosterFooter;
