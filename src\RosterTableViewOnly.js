// File: src/RosterTableViewOnly.js
import React from 'react';

// Simplified Roster Row Component for view-only mode
const RosterRowViewOnly = ({ tech, idx, shiftColors, daysInMonth }) => {
  return (
    <tr key={tech.employeeId} style={{ backgroundColor: idx % 2 === 0 ? '#f9fafb' : 'white' }}>
      <td style={{ border: '1px solid #d1d5db', padding: '0.5rem 1rem', width: 'auto', minWidth: '150px', whiteSpace: 'nowrap', height: '40px', textAlign: 'left' }}>
        {tech['Name']}
      </td>
      <td style={{ border: '1px solid #d1d5db', padding: '0.5rem 1rem', fontWeight: '500', width: '120px', minWidth: '120px', height: '40px' }}>{tech['Staff/SRC#']}</td>
      <td style={{ border: '1px solid #d1d5db', padding: '0.5rem 1rem', width: '80px', minWidth: '80px', height: '40px' }}>{tech['Position']}</td>
      {Array.from({ length: daysInMonth }, (_, i) => {
        const day = (i + 1).toString();
        const shift = tech[day];
        const isMissing = shift === undefined;

        return (
          <td
            key={i}
            style={{
              border: '1px solid #d1d5db',
              padding: '0.25rem 0.5rem',
              textAlign: 'center',
              backgroundColor: isMissing ? '#fee2e2' : (shiftColors[shift] || 'transparent'),
              width: '40px',
              minWidth: '40px',
              height: '40px',
            }}
          >
            {isMissing ? (
              <span title="Shift data missing for this day" style={{ color: '#b91c1c' }}>
                ?
              </span>
            ) : (
              shift || ''
            )}
          </td>
        );
      })}
      <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', height: '40px' }}>{tech['Leave days'] || 0}</td>
      <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', height: '40px' }}>{tech['Sick days'] || 0}</td>
      <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', height: '40px' }}>{tech['Work days'] || 0}</td>
      <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', height: '40px' }}>{tech['Work Time'] || '0:00'}</td>
      <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', height: '40px' }}>{tech['Overtime days'] || 0}</td>
      <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', height: '40px' }}>{tech['Overtime'] || '0:00'}</td>
      <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', height: '40px' }}>{tech['Aircraft'] || '0:00'}</td>
      <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', height: '40px' }}>{tech['Office'] || '0:00'}</td>
    </tr>
  );
};

const RosterTableViewOnly = ({ rosterData, shiftTypes, shiftHours, shiftColors, daysOfWeek, formatHoursMinutes, year, month }) => {
  // Calculate the number of days in the given month
  const parsedYear = parseInt(year, 10);
  const parsedMonth = parseInt(month, 10);
  const daysInMonth = (!isNaN(parsedYear) && !isNaN(parsedMonth))
    ? new Date(parsedYear, parsedMonth, 0).getDate()
    : 30;

  // Fallback shiftColors
  const defaultShiftColors = {
    'D': '#ffffff',
    'D1': '#fff2af',
    'D2': '#4a90e2',
    'D3': '#4a90e2',
    'N': '#d9e1f2',
    'N1': '#d9e1f2',
    'N2': '#d9e1f2',
    'N3': '#d9e1f2',
    'O': '#93C47D',
    'X': '#f1c40f',
    'R': '#f1c40f',
    'SL': '#6d9eeb',
    'TR': '#00FFFF',
    'DT': '#808080'
  };

  const effectiveShiftColors = { ...defaultShiftColors, ...shiftColors };

  // Calculate totals for the footer
  const totals = rosterData.reduce((acc, tech) => {
    acc['Leave days'] += parseInt(tech['Leave days']) || 0;
    acc['Sick days'] += parseInt(tech['Sick days']) || 0;
    acc['Work days'] += parseInt(tech['Work days']) || 0;
    const workTimeParts = (tech['Work Time'] || '0:00').split(':').map(Number);
    acc['Work Time'] += workTimeParts[0] * 60 + workTimeParts[1];
    acc['Overtime days'] += parseInt(tech['Overtime days']) || 0;
    const overtimeParts = (tech['Overtime'] || '0:00').split(':').map(Number);
    acc['Overtime'] += overtimeParts[0] * 60 + overtimeParts[1];
    const aircraftParts = (tech['Aircraft'] || '0:00').split(':').map(Number);
    acc['Aircraft'] += aircraftParts[0] * 60 + aircraftParts[1];
    const officeParts = (tech['Office'] || '0:00').split(':').map(Number);
    acc['Office'] += officeParts[0] * 60 + officeParts[1];
    return acc;
  }, { 'Leave days': 0, 'Sick days': 0, 'Work days': 0, 'Work Time': 0, 'Overtime days': 0, 'Overtime': 0, 'Aircraft': 0, 'Office': 0 });

  totals['Work Time'] = `${Math.floor(totals['Work Time'] / 60)}:${(totals['Work Time'] % 60).toString().padStart(2, '0')}`;
  totals['Overtime'] = `${Math.floor(totals['Overtime'] / 60)}:${(totals['Overtime'] % 60).toString().padStart(2, '0')}`;
  totals['Aircraft'] = `${Math.floor(totals['Aircraft'] / 60)}:${(totals['Aircraft'] % 60).toString().padStart(2, '0')}`;
  totals['Office'] = `${Math.floor(totals['Office'] / 60)}:${(totals['Office'] % 60).toString().padStart(2, '0')}`;

  return (
    <div>
      <div style={{ overflowX: 'auto', marginBottom: '2rem', position: 'relative' }}>
        <table style={{ minWidth: '100%', borderCollapse: 'collapse', border: '1px solid #d1d5db', tableLayout: 'auto', willChange: 'transform' }}>
          <thead>
            <tr style={{ backgroundColor: '#e5e7eb' }}>
              <th rowSpan="2" style={{ border: '1px solid #d1d5db', padding: '0.5rem 1rem', width: 'auto', minWidth: '150px', whiteSpace: 'nowrap', height: '80px', textAlign: 'center', verticalAlign: 'middle' }}>Name</th>
              <th rowSpan="2" style={{ border: '1px solid #d1d5db', padding: '0.5rem 1rem', width: '120px', minWidth: '120px', whiteSpace: 'wrap', textAlign: 'center', height: '80px', verticalAlign: 'middle' }}>Staff/SRC#</th>
              <th rowSpan="2" style={{ border: '1px solid #d1d5db', padding: '0.5rem 1rem', width: '80px', minWidth: '80px', whiteSpace: 'wrap', textAlign: 'center', height: '80px', verticalAlign: 'middle' }}>Position</th>
              {Array.from({ length: daysInMonth }, (_, i) => (
                <th key={i} style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '40px', minWidth: '40px', height: '40px' }}>
                  {daysOfWeek && daysOfWeek[i] ? daysOfWeek[i] : ''}
                </th>
              ))}
              <th style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', whiteSpace: 'wrap', height: 'auto' }}>Leave Days</th>
              <th style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', whiteSpace: 'wrap', height: 'auto' }}>Sick Days</th>
              <th style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', whiteSpace: 'wrap', height: 'auto' }}>Work Days</th>
              <th style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', whiteSpace: 'wrap', height: 'auto' }}>Work Time</th>
              <th style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', whiteSpace: 'wrap', height: 'auto' }}>Overtime Days</th>
              <th style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', whiteSpace: 'wrap', height: 'auto' }}>Overtime</th>
              <th style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', whiteSpace: 'wrap', height: 'auto' }}>Aircraft</th>
              <th style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', whiteSpace: 'wrap', height: 'auto' }}>Office</th>
            </tr>
            <tr style={{ backgroundColor: '#e5e7eb' }}>
              {Array.from({ length: daysInMonth }, (_, i) => (
                <th key={i} style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '40px', minWidth: '40px', height: '40px' }}>{i + 1}</th>
              ))}
              <th style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', whiteSpace: 'wrap', height: 'auto' }}></th>
              <th style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', whiteSpace: 'wrap', height: 'auto' }}></th>
              <th style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', whiteSpace: 'wrap', height: 'auto' }}></th>
              <th style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', whiteSpace: 'wrap', height: 'auto' }}></th>
              <th style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', whiteSpace: 'wrap', height: 'auto' }}></th>
              <th style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', whiteSpace: 'wrap', height: 'auto' }}></th>
              <th style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', whiteSpace: 'wrap', height: 'auto' }}></th>
              <th style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', whiteSpace: 'wrap', height: 'auto' }}></th>
            </tr>
          </thead>
          <tbody>
            {rosterData.map((tech, idx) => (
              <RosterRowViewOnly
                key={tech.employeeId}
                tech={tech}
                idx={idx}
                shiftColors={effectiveShiftColors}
                daysInMonth={daysInMonth}
              />
            ))}
          </tbody>
          <tfoot>
            <tr style={{ backgroundColor: '#e5e7eb', fontWeight: 'bold' }}>
              <td colSpan={3} style={{ border: '1px solid #d1d5db', padding: '0.5rem 1rem', textAlign: 'right' }}>Totals:</td>
              {Array.from({ length: daysInMonth }, (_, i) => (
                <td key={i} style={{ border: '1px solid #d1d5db', height: '40px' }}></td>
              ))}
              <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', height: '40px' }}>{totals['Leave days']}</td>
              <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', height: '40px' }}>{totals['Sick days']}</td>
              <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', height: '40px' }}>{totals['Work days']}</td>
              <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', height: '40px' }}>{totals['Work Time']}</td>
              <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', height: '40px' }}>{totals['Overtime days']}</td>
              <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', height: '40px' }}>{totals['Overtime']}</td>
              <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', height: '40px' }}>{totals['Aircraft']}</td>
              <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', height: '40px' }}>{totals['Office']}</td>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>
  );
};

export default RosterTableViewOnly;