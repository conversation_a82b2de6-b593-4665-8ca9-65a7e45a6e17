import React, { useState, useEffect } from 'react';

const EmployeeDocs = () => {
  // Employee database (from previous roster data)
  const employeeDB = [
    { employeeId: 'T00003', name: '<PERSON><PERSON>', srcNumber: '********' },
    { employeeId: 'T00004', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', srcNumber: '********' },
    { employeeId: 'T00005', name: '<PERSON><PERSON><PERSON>', srcNumber: 'T0200980' },
    { employeeId: 'T00006', name: '<PERSON><PERSON><PERSON><PERSON>KA<PERSON>', srcNumber: '3806' },
    { employeeId: 'T00007', name: '<PERSON><PERSON><PERSON><PERSON>', srcNumber: '3808' },
    { employeeId: 'T00008', name: '<PERSON><PERSON><PERSON>', srcNumber: '3810' },
    { employeeId: 'T00009', name: '<PERSON>', srcNumber: '3814' },
    { employeeId: 'T00010', name: '<PERSON>', srcNumber: '3811' },
    { employeeId: 'T00011', name: '<PERSON>', srcNumber: '3803' },
    { employeeId: 'T00012', name: 'Lar Alvin CAPARAS', srcNumber: '3802' },
    { employeeId: 'T00013', name: 'Ahmed Taher ALSAGHEER', srcNumber: '3807' },
    { employeeId: 'T00014', name: 'Jalal ISMAEEL', srcNumber: '3813' },
    { employeeId: 'T00015', name: 'Mohammed NOOH', srcNumber: '3815' },
    { employeeId: 'T00016', name: 'Mohamed ROSTAM', srcNumber: '3816' },
    { employeeId: 'T00017', name: 'Abdulla SAEED', srcNumber: '3817' },
    { employeeId: 'T00018', name: 'Ahmed Abbas SEDAIF', srcNumber: '3818' },
    { employeeId: 'T00019', name: 'Ahmed Shaaban', srcNumber: '3820' },
    { employeeId: 'T00020', name: 'Munendra Singh', srcNumber: 'TBC' },
  ];

  // Predefined document types for dropdown
  const documentTypes = [
    'Passport',
    'Residence Permit',
    'Driving Permit',
    'Airport Pass',
    'Work Permit',
    'Work License',
  ];

  // Dummy data for three employees with a list of documents
  const [employeeDocs, setEmployeeDocs] = useState([
    {
      employeeId: 'T00003',
      name: 'Employee 1',
      srcNumber: '********',
      documents: [
        { type: 'Passport', number: 'P123456', expirationDate: '2025-05-01', attachment: 'passport_luay.pdf', status: 'Valid' },
        { type: 'Residence Permit', number: 'R789012', expirationDate: '2025-06-15', attachment: 'residence_luay.pdf', status: 'Valid' },
        { type: 'Driving Permit', number: 'D345678', expirationDate: '2024-12-31', attachment: 'driving_luay.pdf', status: 'Expiring Soon' },
      ],
    },
    {
      employeeId: 'T00004',
      name: 'Employee 2',
      srcNumber: '********',
      documents: [
        { type: 'Passport', number: 'P901234', expirationDate: '2025-03-15', attachment: 'passport_balkrishna.pdf', status: 'Expired' },
        { type: 'Work Permit', number: 'W567890', expirationDate: '2025-10-01', attachment: 'workpermit_balkrishna.pdf', status: 'Valid' },
      ],
    },
    {
      employeeId: 'T00005',
      name: 'Employee 3',
      srcNumber: 'T0200980',
      documents: [
        { type: 'Passport', number: 'P567890', expirationDate: '2025-12-01', attachment: 'passport_lemmi.pdf', status: 'Valid' },
        { type: 'Residence Permit', number: 'R123456', expirationDate: '2024-04-01', attachment: 'residence_lemmi.pdf', status: 'Expired' },
        { type: 'Airport Pass', number: 'A789012', expirationDate: '2025-04-30', attachment: 'airport_lemmi.pdf', status: 'Critical' },
      ],
    },
  ]);

  // State for notifications
  const [notifications, setNotifications] = useState([]);

  // State for modal
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('add'); // 'add', 'edit', or 'addDoc'
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [selectedDocIndex, setSelectedDocIndex] = useState(null);
  const [formData, setFormData] = useState({
    type: '',
    customType: '',
    number: '',
    expirationDate: '',
    attachment: '',
  });

  // Check expiration status and update notifications
  useEffect(() => {
    const today = new Date('2025-04-26');
    const newNotifications = [];

    employeeDocs.forEach((employee) => {
      employee.documents.forEach((doc) => {
        if (!doc.expirationDate) return;
        const expiration = new Date(doc.expirationDate);
        const diffDays = Math.ceil((expiration - today) / (1000 * 60 * 60 * 24));

        if (expiration < today) {
          doc.status = 'Expired';
          newNotifications.push(`${employee.name}'s ${doc.type} has expired on ${doc.expirationDate}.`);
        } else if (diffDays <= 7) {
          doc.status = 'Critical';
          newNotifications.push(`${employee.name}'s ${doc.type} is critically expiring on ${doc.expirationDate}.`);
        } else if (diffDays <= 30) {
          doc.status = 'Expiring Soon';
          newNotifications.push(`${employee.name}'s ${doc.type} is expiring soon on ${doc.expirationDate}.`);
        } else {
          doc.status = 'Valid';
        }
      });
    });

    setNotifications(newNotifications);
  }, [employeeDocs]);

  // Open modal for adding a new employee record
  const handleAddRecord = () => {
    setModalMode('add');
    setFormData({
      type: '',
      customType: '',
      number: '',
      expirationDate: '',
      attachment: '',
    });
    setSelectedEmployee(null);
    setSelectedDocIndex(null);
    setIsModalOpen(true);
  };

  // Open modal for adding a document to an existing employee
  const handleAddDocument = (employee) => {
    setModalMode('addDoc');
    setFormData({
      type: '',
      customType: '',
      number: '',
      expirationDate: '',
      attachment: '',
    });
    setSelectedEmployee(employee);
    setSelectedDocIndex(null);
    setIsModalOpen(true);
  };

  // Open modal for editing an existing document
  const handleEditDocument = (employee, docIndex) => {
    setModalMode('edit');
    const doc = employee.documents[docIndex];
    setFormData({
      type: documentTypes.includes(doc.type) ? doc.type : 'Custom',
      customType: documentTypes.includes(doc.type) ? '' : doc.type,
      number: doc.number,
      expirationDate: doc.expirationDate,
      attachment: doc.attachment || '',
    });
    setSelectedEmployee(employee);
    setSelectedDocIndex(docIndex);
    setIsModalOpen(true);
  };

  // Handle form submission (add or edit)
  const handleSubmit = () => {
    if (modalMode === 'add' && !selectedEmployee) {
      alert('Please select an employee.');
      return;
    }

    const docType = formData.type === 'Custom' ? formData.customType : formData.type;
    if (!docType) {
      alert('Please select or enter a document type.');
      return;
    }

    const newDoc = {
      type: docType,
      number: formData.number,
      expirationDate: formData.expirationDate,
      attachment: formData.attachment || '',
      status: 'Valid', // Will be updated by useEffect
    };

    if (modalMode === 'add') {
      // Add new employee with the document
      const newEmployee = {
        employeeId: selectedEmployee.employeeId,
        name: selectedEmployee.name,
        srcNumber: selectedEmployee.srcNumber,
        documents: [newDoc],
      };
      setEmployeeDocs([...employeeDocs, newEmployee]);
    } else if (modalMode === 'addDoc') {
      // Add document to existing employee
      setEmployeeDocs(
        employeeDocs.map((emp) =>
          emp.employeeId === selectedEmployee.employeeId
            ? { ...emp, documents: [...emp.documents, newDoc] }
            : emp
        )
      );
    } else {
      // Edit existing document
      setEmployeeDocs(
        employeeDocs.map((emp) =>
          emp.employeeId === selectedEmployee.employeeId
            ? {
                ...emp,
                documents: emp.documents.map((doc, idx) =>
                  idx === selectedDocIndex ? newDoc : doc
                ),
              }
            : emp
        )
      );
    }
    setIsModalOpen(false);
  };

  // Handle employee selection from dropdown
  const handleEmployeeSelect = (e) => {
    const selectedId = e.target.value;
    const employee = employeeDB.find((emp) => emp.employeeId === selectedId);
    setSelectedEmployee(employee || null);
  };

  // Handle input changes in the modal form
  const handleInputChange = (field, value) => {
    setFormData({ ...formData, [field]: value });
  };

  // Simulate file upload (set attachment)
  const handleFileUpload = (employee, docIndex) => {
    const attachment = prompt('Enter attachment file name (e.g., document.pdf):');
    if (attachment) {
      setEmployeeDocs(
        employeeDocs.map((emp) =>
          emp.employeeId === employee.employeeId
            ? {
                ...emp,
                documents: emp.documents.map((doc, idx) =>
                  idx === docIndex ? { ...doc, attachment } : doc
                ),
              }
            : emp
        )
      );
    }
  };

  // Remove attachment
  const handleRemoveAttachment = (employee, docIndex) => {
    setEmployeeDocs(
      employeeDocs.map((emp) =>
        emp.employeeId === employee.employeeId
          ? {
              ...emp,
              documents: emp.documents.map((doc, idx) =>
                idx === docIndex ? { ...doc, attachment: '' } : doc
              ),
            }
          : emp
      )
    );
  };

  return (
    <div style={{ padding: '4.5rem' }}>
      <h2 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#1f2937', marginBottom: '1rem' }}>
        Employee Documentation Control
      </h2>

      {/* Notifications */}
      {notifications.length > 0 && (
        <div
          style={{
            backgroundColor: '#fef9c3',
            borderLeft: '4px solid #f59e0b',
            color: '#92400e',
            padding: '1rem',
            marginBottom: '1.5rem',
            borderRadius: '0.25rem',
          }}
        >
          <h3 style={{ fontWeight: '600', marginBottom: '0.5rem' }}>
            Expiration Notifications
          </h3>
          <ul style={{ listStyleType: 'disc', paddingLeft: '1.25rem' }}>
            {notifications.map((notification, index) => (
              <li key={index} style={{ fontSize: '0.875rem' }}>
                {notification}
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Add New Record Button */}
      <button
        onClick={handleAddRecord}
        style={{
          padding: '0.5rem 1rem',
          backgroundColor: '#3b82f6',
          color: '#ffffff',
          borderRadius: '0.25rem',
          border: 'none',
          cursor: 'pointer',
          marginBottom: '1.5rem',
          transition: 'background-color 0.2s ease',
        }}
        onMouseEnter={(e) => (e.target.style.backgroundColor = '#2563eb')}
        onMouseLeave={(e) => (e.target.style.backgroundColor = '#3b82f6')}
        aria-label="Add new employee documentation record"
      >
        Add New Employee Record
      </button>

      {/* Employee Sections */}
      {employeeDocs.map((employee) => (
        <div key={employee.employeeId} style={{ marginBottom: '2rem' }}>
          {/* Employee Info */}
          <div
            style={{
              backgroundColor: '#ffffff',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              borderRadius: '0.5rem',
              padding: '1rem',
              marginBottom: '1.5rem',
            }}
          >
            <h2
              style={{
                fontSize: '1.25rem',
                fontWeight: '600',
                color: '#374151',
              }}
            >
              Employee: {employee.name} (ID: {employee.employeeId})
            </h2>
            <button
              onClick={() => handleAddDocument(employee)}
              style={{
                marginTop: '0.5rem',
                padding: '0.25rem 0.75rem',
                backgroundColor: '#10b981',
                color: '#ffffff',
                borderRadius: '0.25rem',
                border: 'none',
                cursor: 'pointer',
                transition: 'background-color 0.2s ease',
              }}
              onMouseEnter={(e) => (e.target.style.backgroundColor = '#059669')}
              onMouseLeave={(e) => (e.target.style.backgroundColor = '#10b981')}
              aria-label={`Add new document for ${employee.name}`}
            >
              Add Document
            </button>
          </div>

          {/* Documents Table */}
          <div
            style={{
              backgroundColor: '#ffffff',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              borderRadius: '0.5rem',
              overflow: 'hidden',
            }}
          >
            <table style={{ minWidth: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr
                  style={{
                    backgroundColor: '#f3f4f6',
                    borderBottom: '1px solid #e5e7eb',
                  }}
                >
                  <th
                    style={{
                      padding: '0.75rem 1.5rem',
                      textAlign: 'left',
                      fontSize: '0.75rem',
                      fontWeight: '500',
                      color: '#6b7280',
                      textTransform: 'uppercase',
                    }}
                  >
                    Document Type
                  </th>
                  <th
                    style={{
                      padding: '0.75rem 1.5rem',
                      textAlign: 'left',
                      fontSize: '0.75rem',
                      fontWeight: '500',
                      color: '#6b7280',
                      textTransform: 'uppercase',
                    }}
                  >
                    Document Number
                  </th>
                  <th
                    style={{
                      padding: '0.75rem 1.5rem',
                      textAlign: 'left',
                      fontSize: '0.75rem',
                      fontWeight: '500',
                      color: '#6b7280',
                      textTransform: 'uppercase',
                    }}
                  >
                    Expiration Date
                  </th>
                  <th
                    style={{
                      padding: '0.75rem 1.5rem',
                      textAlign: 'left',
                      fontSize: '0.75rem',
                      fontWeight: '500',
                      color: '#6b7280',
                      textTransform: 'uppercase',
                    }}
                  >
                    Status
                  </th>
                  <th
                    style={{
                      padding: '0.75rem 1.5rem',
                      textAlign: 'left',
                      fontSize: '0.75rem',
                      fontWeight: '500',
                      color: '#6b7280',
                      textTransform: 'uppercase',
                    }}
                  >
                    Attachment
                  </th>
                  <th
                    style={{
                      padding: '0.75rem 1.5rem',
                      textAlign: 'left',
                      fontSize: '0.75rem',
                      fontWeight: '500',
                      color: '#6b7280',
                      textTransform: 'uppercase',
                    }}
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {employee.documents.map((doc, index) => (
                  <tr
                    key={index}
                    style={{
                      borderBottom: '1px solid #e5e7eb',
                      backgroundColor:
                        doc.status === 'Expired'
                          ? '#fee2e2'
                          : doc.status === 'Critical'
                          ? '#ffedd5'
                          : doc.status === 'Expiring Soon'
                          ? '#fef9c3'
                          : '#ffffff',
                    }}
                  >
                    <td
                      style={{
                        padding: '1rem 1.5rem',
                        fontSize: '0.875rem',
                        color: '#374151',
                        whiteSpace: 'nowrap',
                      }}
                    >
                      {doc.type}
                    </td>
                    <td
                      style={{
                        padding: '1rem 1.5rem',
                        fontSize: '0.875rem',
                        color: '#374151',
                        whiteSpace: 'nowrap',
                      }}
                    >
                      {doc.number || '-'}
                    </td>
                    <td
                      style={{
                        padding: '1rem 1.5rem',
                        fontSize: '0.875rem',
                        color: '#374151',
                        whiteSpace: 'nowrap',
                      }}
                    >
                      {doc.expirationDate || '-'}
                    </td>
                    <td
                      style={{
                        padding: '1rem 1.5rem',
                        fontSize: '0.875rem',
                        whiteSpace: 'nowrap',
                      }}
                    >
                      <span
                        style={{
                          color:
                            doc.status === 'Expired'
                              ? '#dc2626'
                              : doc.status === 'Critical'
                              ? '#f97316'
                              : doc.status === 'Expiring Soon'
                              ? '#ca8a04'
                              : '#16a34a',
                          fontWeight: '600',
                        }}
                      >
                        {doc.status}
                      </span>
                    </td>
                    <td
                      style={{
                        padding: '1rem 1.5rem',
                        fontSize: '0.875rem',
                        color: '#374151',
                        whiteSpace: 'nowrap',
                      }}
                    >
                      {doc.attachment ? (
                        <span style={{ color: '#3b82f6' }}>{doc.attachment}</span>
                      ) : (
                        'No attachment'
                      )}
                    </td>
                    <td
                      style={{
                        padding: '1rem 1.5rem',
                        fontSize: '0.875rem',
                        whiteSpace: 'nowrap',
                      }}
                    >
                      {doc.attachment ? (
                        <button
                          onClick={() => handleRemoveAttachment(employee, index)}
                          style={{
                            color: '#dc2626',
                            marginRight: '0.5rem',
                            transition: 'color 0.2s ease',
                          }}
                          onMouseEnter={(e) => (e.target.style.color = '#b91c1c')}
                          onMouseLeave={(e) => (e.target.style.color = '#dc2626')}
                        >
                          Remove
                        </button>
                      ) : (
                        <button
                          onClick={() => handleFileUpload(employee, index)}
                          style={{
                            color: '#3b82f6',
                            marginRight: '0.5rem',
                            transition: 'color 0.2s ease',
                          }}
                          onMouseEnter={(e) => (e.target.style.color = '#2563eb')}
                          onMouseLeave={(e) => (e.target.style.color = '#3b82f6')}
                        >
                          Upload
                        </button>
                      )}
                      <button
                        onClick={() => handleEditDocument(employee, index)}
                        style={{
                          color: '#10b981',
                          transition: 'color 0.2s ease',
                        }}
                        onMouseEnter={(e) => (e.target.style.color = '#059669')}
                        onMouseLeave={(e) => (e.target.style.color = '#10b981')}
                      >
                        Edit
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      ))}

      {/* Modal */}
      {isModalOpen && (
        <div
          style={{
            position: 'fixed',
            top: '0',
            left: '0',
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: '1000',
          }}
        >
          <div
            style={{
              backgroundColor: '#ffffff',
              padding: '1.5rem',
              borderRadius: '0.5rem',
              width: '90%',
              maxWidth: '600px',
              maxHeight: '80vh',
              overflowY: 'auto',
            }}
          >
            <h3
              style={{
                fontSize: '1.25rem',
                fontWeight: '600',
                color: '#1f2937',
                marginBottom: '1rem',
              }}
            >
              {modalMode === 'add'
                ? 'Add New Employee Documentation'
                : modalMode === 'addDoc'
                ? `Add Document for ${selectedEmployee.name}`
                : 'Edit Document'}
            </h3>

            {/* Employee Selection (only for adding new employee record) */}
            {modalMode === 'add' && (
              <div style={{ marginBottom: '1rem' }}>
                <label
                  style={{
                    display: 'block',
                    fontSize: '0.875rem',
                    color: '#374151',
                    marginBottom: '0.25rem',
                  }}
                >
                  Select Employee
                </label>
                <select
                  value={selectedEmployee ? selectedEmployee.employeeId : ''}
                  onChange={handleEmployeeSelect}
                  style={{
                    width: '100%',
                    padding: '0.5rem',
                    fontSize: '0.875rem',
                    borderRadius: '0.25rem',
                    border: '1px solid #d1d5db',
                    outline: 'none',
                  }}
                >
                  <option value="">-- Select Employee --</option>
                  {employeeDB
                    .filter(
                      (emp) =>
                        !employeeDocs.some((doc) => doc.employeeId === emp.employeeId)
                    )
                    .map((emp) => (
                      <option key={emp.employeeId} value={emp.employeeId}>
                        {emp.name} ({emp.srcNumber})
                      </option>
                    ))}
                </select>
              </div>
            )}

            {/* Document Form */}
            <div style={{ marginBottom: '1rem' }}>
              <label
                style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  color: '#374151',
                  marginBottom: '0.25rem',
                }}
              >
                Document Type
              </label>
              <select
                value={formData.type}
                onChange={(e) => handleInputChange('type', e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.5rem',
                  fontSize: '0.875rem',
                  borderRadius: '0.25rem',
                  border: '1px solid #d1d5db',
                  outline: 'none',
                  marginBottom: formData.type === 'Custom' ? '0.5rem' : '0',
                }}
              >
                <option value="">-- Select Document Type --</option>
                {documentTypes.map((type) => (
                  <option key={type} value={type}>
                    {type}
                  </option>
                ))}
                <option value="Custom">Custom</option>
              </select>
              {formData.type === 'Custom' && (
                <input
                  type="text"
                  placeholder="Enter custom document type"
                  value={formData.customType}
                  onChange={(e) => handleInputChange('customType', e.target.value)}
                  style={{
                    width: '100%',
                    padding: '0.5rem',
                    fontSize: '0.875rem',
                    borderRadius: '0.25rem',
                    border: '1px solid #d1d5db',
                    outline: 'none',
                    marginTop: '0.5rem',
                  }}
                />
              )}
            </div>

            <div style={{ marginBottom: '1rem' }}>
              <label
                style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  color: '#374151',
                  marginBottom: '0.25rem',
                }}
              >
                Document Number
              </label>
              <input
                type="text"
                value={formData.number}
                onChange={(e) => handleInputChange('number', e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.5rem',
                  fontSize: '0.875rem',
                  borderRadius: '0.25rem',
                  border: '1px solid #d1d5db',
                  outline: 'none',
                }}
              />
            </div>

            <div style={{ marginBottom: '1rem' }}>
              <label
                style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  color: '#374151',
                  marginBottom: '0.25rem',
                }}
              >
                Expiration Date
              </label>
              <input
                type="date"
                value={formData.expirationDate}
                onChange={(e) => handleInputChange('expirationDate', e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.5rem',
                  fontSize: '0.875rem',
                  borderRadius: '0.25rem',
                  border: '1px solid #d1d5db',
                  outline: 'none',
                }}
              />
            </div>

            <div style={{ marginBottom: '1rem' }}>
              <label
                style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  color: '#374151',
                  marginBottom: '0.25rem',
                }}
              >
                Attachment (File Name)
              </label>
              <input
                type="text"
                placeholder="e.g., document.pdf"
                value={formData.attachment}
                onChange={(e) => handleInputChange('attachment', e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.5rem',
                  fontSize: '0.875rem',
                  borderRadius: '0.25rem',
                  border: '1px solid #d1d5db',
                  outline: 'none',
                }}
              />
            </div>

            {/* Modal Buttons */}
            <div style={{ display: 'flex', gap: '1rem', marginTop: '1rem' }}>
              <button
                onClick={handleSubmit}
                style={{
                  padding: '0.5rem 1rem',
                  backgroundColor: '#3b82f6',
                  color: '#ffffff',
                  borderRadius: '0.25rem',
                  border: 'none',
                  cursor: 'pointer',
                  transition: 'background-color 0.2s ease',
                }}
                onMouseEnter={(e) => (e.target.style.backgroundColor = '#2563eb')}
                onMouseLeave={(e) => (e.target.style.backgroundColor = '#3b82f6')}
                aria-label={modalMode === 'add' ? 'Save new record' : 'Save edited record'}
              >
                Save
              </button>
              <button
                onClick={() => setIsModalOpen(false)}
                style={{
                  padding: '0.5rem 1rem',
                  backgroundColor: '#6b7280',
                  color: '#ffffff',
                  borderRadius: '0.25rem',
                  border: 'none',
                  cursor: 'pointer',
                  transition: 'background-color 0.2s ease',
                }}
                onMouseEnter={(e) => (e.target.style.backgroundColor = '#4b5563')}
                onMouseLeave={(e) => (e.target.style.backgroundColor = '#6b7280')}
                aria-label="Cancel and close modal"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EmployeeDocs;