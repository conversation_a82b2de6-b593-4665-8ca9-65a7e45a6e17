import { useState, useEffect, useCallback, useContext } from 'react';
import { ref, set, onValue } from 'firebase/database';
import { database } from '../../firebaseConfig';
import { toast } from 'react-toastify';
import { AuthContext } from '../../components/AuthProvider';

/**
 * Custom hook for handling subscriber data management (Telegram/WhatsApp)
 * Extracted from AdminUserManager.js to improve maintainability
 */
export const useSubscriberData = (departmentPath) => {
  const { userDepartment, selectedDepartment, isSuperAdmin, isRegionManager, isSuperUser, isRegionUser } = useContext(AuthContext);

  // State management
  const [telegramSubscribers, setTelegramSubscribers] = useState([]);
  const [whatsappSubscribers, setWhatsappSubscribers] = useState([]);
  const [duplicateTelegramIds, setDuplicateTelegramIds] = useState([]);
  const [duplicateWhatsappNumbers, setDuplicateWhatsappNumbers] = useState([]);
  const [subscriberActionLoading, setSubscriberActionLoading] = useState({});

  // ✅ Fetch Telegram subscribers
  useEffect(() => {
    console.log('Fetching Telegram subscribers with department path:', departmentPath);

    if (!departmentPath) {
      console.log('Waiting for department path to be set before fetching Telegram subscribers');
      return () => {}; // Return empty cleanup function
    }

    const telegramRef = ref(database, `${departmentPath}telegram_subscribers`);
    const unsubscribeTelegram = onValue(
      telegramRef,
      (snapshot) => {
        const data = snapshot.val();
        const telegramList = data
          ? Object.entries(data).map(([id, subscriber]) => ({
              id,
              subscribed: subscriber.subscribed !== false,
              status: subscriber.status || 'active',
            }))
          : [];

        console.log('Telegram subscribers:', telegramList);
        setTelegramSubscribers(telegramList);

        // Detect duplicate Telegram IDs
        const idCounts = {};
        telegramList.forEach(sub => {
          if (sub.id && sub.id !== '') {
            idCounts[sub.id] = (idCounts[sub.id] || 0) + 1;
          }
        });
        const duplicates = Object.entries(idCounts)
          .filter(([_, count]) => count > 1)
          .map(([id]) => id);
        setDuplicateTelegramIds(duplicates);
      },
      (error) => {
        console.error('Error fetching Telegram subscribers:', error);
        toast.error('Failed to load Telegram subscribers.');
      }
    );

    return () => {
      unsubscribeTelegram();
    };
  }, [departmentPath]);

  // ✅ Fetch WhatsApp subscribers
  useEffect(() => {
    console.log('Fetching WhatsApp subscribers with department path:', departmentPath);

    if (!departmentPath) {
      console.log('Waiting for department path to be set before fetching WhatsApp subscribers');
      return () => {}; // Return empty cleanup function
    }

    const whatsappRef = ref(database, `${departmentPath}whatsapp_subscribers`);
    const unsubscribeWhatsapp = onValue(
      whatsappRef,
      (snapshot) => {
        const data = snapshot.val();
        const whatsappList = data
          ? Object.entries(data).map(([number, subscriber]) => ({
              number,
              subscribed: subscriber.subscribed !== false,
              status: subscriber.status || 'active',
            }))
          : [];

        console.log('WhatsApp subscribers:', whatsappList);
        setWhatsappSubscribers(whatsappList);

        // Detect duplicate WhatsApp numbers
        const numberCounts = {};
        whatsappList.forEach(sub => {
          if (sub.number && sub.number !== '') {
            const cleanNumber = sub.number.replace(/"/g, '');
            numberCounts[cleanNumber] = (numberCounts[cleanNumber] || 0) + 1;
          }
        });
        const duplicates = Object.entries(numberCounts)
          .filter(([_, count]) => count > 1)
          .map(([number]) => number);
        setDuplicateWhatsappNumbers(duplicates);
      },
      (error) => {
        console.error('Error fetching WhatsApp subscribers:', error);
        toast.error('Failed to load WhatsApp subscribers.');
      }
    );

    return () => {
      unsubscribeWhatsapp();
    };
  }, [departmentPath]);

  // ✅ Toggle subscription status for Telegram/WhatsApp
  const handleToggleSubscription = useCallback(
    async (type, id, currentSubscribed, isSubmitting, isInitializing) => {
      if (isSubmitting || isInitializing || subscriberActionLoading[id]) {
        return;
      }

      const newSubscribed = !currentSubscribed;
      const path = type === 'telegram' 
        ? `${departmentPath}telegram_subscribers/${id}` 
        : `${departmentPath}whatsapp_subscribers/${id}`;
      const displayId = type === 'telegram' ? id : id.replace(/"/g, '');

      if (!window.confirm(`${newSubscribed ? 'Resubscribe' : 'Unsubscribe'} ${displayId} from ${type} notifications?`)) return;

      setSubscriberActionLoading((prev) => ({ ...prev, [id]: true }));
      try {
        await set(ref(database, path), {
          status: 'active',
          subscribed: newSubscribed,
        });
        toast.success(`${displayId} ${newSubscribed ? 'resubscribed to' : 'unsubscribed from'} ${type} notifications.`);
      } catch (error) {
        toast.error(`Failed to update ${type} subscription for ${displayId}.`);
        console.error(`Error updating ${type} subscription:`, error);
      } finally {
        setSubscriberActionLoading((prev) => ({ ...prev, [id]: false }));
      }
    },
    [subscriberActionLoading, departmentPath]
  );

  // ✅ Get subscriber statistics
  const getSubscriberStats = useCallback(() => {
    const telegramStats = {
      total: telegramSubscribers.length,
      subscribed: telegramSubscribers.filter(sub => sub.subscribed).length,
      unsubscribed: telegramSubscribers.filter(sub => !sub.subscribed).length,
      duplicates: duplicateTelegramIds.length
    };

    const whatsappStats = {
      total: whatsappSubscribers.length,
      subscribed: whatsappSubscribers.filter(sub => sub.subscribed).length,
      unsubscribed: whatsappSubscribers.filter(sub => !sub.subscribed).length,
      duplicates: duplicateWhatsappNumbers.length
    };

    return { telegram: telegramStats, whatsapp: whatsappStats };
  }, [telegramSubscribers, whatsappSubscribers, duplicateTelegramIds, duplicateWhatsappNumbers]);

  // ✅ Check if subscriber management is available for current user
  const hasSubscriberAccess = useCallback(() => {
    // Only admin-level users and above can manage subscribers
    return isSuperAdmin() || isRegionManager || isSuperUser || isRegionUser;
  }, [isSuperAdmin, isRegionManager, isSuperUser, isRegionUser]);

  // ✅ Get filtered subscribers based on search/filter criteria
  const getFilteredSubscribers = useCallback((type, searchTerm = '', showOnlySubscribed = false) => {
    const subscribers = type === 'telegram' ? telegramSubscribers : whatsappSubscribers;
    
    return subscribers.filter(sub => {
      const identifier = type === 'telegram' ? sub.id : sub.number.replace(/"/g, '');
      
      // Apply search filter
      const matchesSearch = !searchTerm || identifier.toLowerCase().includes(searchTerm.toLowerCase());
      
      // Apply subscription filter
      const matchesSubscription = !showOnlySubscribed || sub.subscribed;
      
      return matchesSearch && matchesSubscription;
    });
  }, [telegramSubscribers, whatsappSubscribers]);

  // ✅ Bulk operations for subscribers
  const handleBulkSubscriptionUpdate = useCallback(async (type, subscriberIds, newSubscriptionStatus, isSubmitting, isInitializing) => {
    if (isSubmitting || isInitializing || subscriberIds.length === 0) {
      return;
    }

    const action = newSubscriptionStatus ? 'subscribe' : 'unsubscribe';
    const confirmMessage = `${action} ${subscriberIds.length} ${type} subscribers?`;
    
    if (!window.confirm(confirmMessage)) return;

    let successCount = 0;
    let errorCount = 0;

    for (const id of subscriberIds) {
      try {
        const path = type === 'telegram' 
          ? `${departmentPath}telegram_subscribers/${id}` 
          : `${departmentPath}whatsapp_subscribers/${id}`;
        
        await set(ref(database, path), {
          status: 'active',
          subscribed: newSubscriptionStatus,
        });
        successCount++;
      } catch (error) {
        console.error(`Error updating ${type} subscription for ${id}:`, error);
        errorCount++;
      }
    }

    if (successCount > 0) {
      toast.success(`Successfully ${action}d ${successCount} ${type} subscribers.`);
    }
    if (errorCount > 0) {
      toast.error(`Failed to update ${errorCount} ${type} subscribers.`);
    }
  }, [departmentPath]);

  // ✅ Export subscriber data
  const exportSubscriberData = useCallback((type) => {
    const subscribers = type === 'telegram' ? telegramSubscribers : whatsappSubscribers;
    const csvData = subscribers.map(sub => ({
      identifier: type === 'telegram' ? sub.id : sub.number.replace(/"/g, ''),
      subscribed: sub.subscribed ? 'Yes' : 'No',
      status: sub.status
    }));

    const csvContent = [
      ['Identifier', 'Subscribed', 'Status'],
      ...csvData.map(row => [row.identifier, row.subscribed, row.status])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${type}_subscribers_${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
    window.URL.revokeObjectURL(url);

    toast.success(`${type} subscriber data exported successfully.`);
  }, [telegramSubscribers, whatsappSubscribers]);

  return {
    // State
    telegramSubscribers,
    whatsappSubscribers,
    duplicateTelegramIds,
    duplicateWhatsappNumbers,
    subscriberActionLoading,

    // Actions
    handleToggleSubscription,
    handleBulkSubscriptionUpdate,

    // Utility functions
    getSubscriberStats,
    hasSubscriberAccess,
    getFilteredSubscribers,
    exportSubscriberData
  };
};
