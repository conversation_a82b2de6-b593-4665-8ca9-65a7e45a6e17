{"name": "rost", "version": "0.1.0", "private": true, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^5.0.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "file-saver": "^2.0.5", "firebase": "^11.6.0", "firebase-admin": "^13.3.0", "firebase-functions": "^6.3.2", "framer-motion": "^12.9.2", "fs": "^0.0.1-security", "node-telegram-bot-api": "^0.66.0", "papaparse": "^5.5.2", "prop-types": "^15.8.1", "react": "^19.1.0", "react-calendar": "^5.1.0", "react-color": "^2.19.3", "react-datepicker": "^8.3.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.1", "react-icons": "^5.5.0", "react-router-dom": "^7.5.1", "react-scripts": "5.0.1", "react-toastify": "^11.0.5", "react-tooltip": "^5.28.1", "react-window": "^1.8.11", "recharts": "^2.15.2", "tailwind-merge": "^3.2.0", "telegraf": "^4.16.3", "web-vitals": "^2.1.4", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0", "yup": "^1.6.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"customize-cra": "^1.0.0", "react-app-rewired": "^2.2.1"}}