// src/useFetchShiftData.js
import { ref, get } from 'firebase/database';
import { database } from './firebaseConfig.js';
import { useState, useEffect } from 'react';

const useFetchShiftData = () => {
  const [shiftData, setShiftData] = useState(null);
  const [shiftTypes, setShiftTypes] = useState([]);
  const [shiftHours, setShiftHours] = useState({});
  const [shiftColors, setShiftColors] = useState({});
  const [shiftStartTimes, setshiftStartTimes] = useState({});
  const [shiftEndTimes, setshiftEndTimes] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchShiftData = async () => {
      setLoading(true);
      setError(null);

      try {
        const shiftsRef = ref(database, 'shifts');
        const snapshot = await get(shiftsRef);

        if (snapshot.exists()) {
          const data = snapshot.val();
          console.log('Shift Data:', data);

          // Extract shiftTypes, shiftHours, and shiftColors
          const types = Object.keys(data);
          const hours = {};
          const colors = {};
          const startTimes = {};
          const endTimes = {};
          Object.entries(data).forEach(([key, value]) => {
            hours[key] = value.hours || 0;
            colors[key] = value.color || '#D3D3D3'; // Default gray if color missing
            startTimes[key] = value.startTime || '';
            endTimes[key] = value.endTime || '';
          });

          setShiftData(data);
          setShiftTypes(types);
          setShiftHours(hours);
          setShiftColors(colors);

          setshiftStartTimes(startTimes);
          setshiftEndTimes(endTimes);
        } else {
          setShiftData({});
          setShiftTypes([]);
          setShiftHours({});
          setShiftColors({});
          setshiftStartTimes({});
          setshiftEndTimes({});
          console.log('No shift data available');
        }
      } catch (err) {
        setError(err);
        console.error('Error fetching shift data:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchShiftData();
  }, []);

  return { shiftData, shiftTypes, shiftHours, shiftColors,shiftStartTimes, shiftEndTimes, loading, error };
};

export default useFetchShiftData;