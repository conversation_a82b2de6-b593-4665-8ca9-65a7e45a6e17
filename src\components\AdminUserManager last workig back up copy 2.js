import React, { useState, useEffect, useCallback, useMemo, Component, useContext } from 'react';
import PropTypes from 'prop-types';
import { auth, database } from '../firebaseConfig';
import { sendPasswordResetEmail, onAuthStateChanged } from 'firebase/auth';
import { ref, set, get, onValue } from 'firebase/database';
import { toast } from 'react-toastify';
import { AuthContext } from './AuthProvider';
import RoleEditModal from './RoleEditModal';
import DepartmentAssignmentModal from './modals/DepartmentAssignmentModal';

// REMINDER: Before moving to production, you MUST:
// 1. Set Firebase Realtime Database rules to secure 'users', 'employees', 'config/maxAdmins', 'telegram_subscribers', and 'whatsapp_subscribers'.
// 2. Create an admin account in the 'users' database with role: 'admin'.
// 3. Deploy 'addUser' and 'deleteUser' Cloud Functions.

// Error Boundary Component
class AdminUserManagerErrorBoundary extends Component {
  state = { hasError: false, error: null };

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-4 bg-red-100 text-red-700 rounded-md">
          <h3 className="font-bold">Something went wrong.</h3>
          <p>{this.state.error?.message || 'An unexpected error occurred.'}</p>
          <p>Please try refreshing the page or contact support.</p>
        </div>
      );
    }
    return this.props.children;
  }
}

const AdminUserManager = ({ employees: propEmployees, isSubmitting, isInitializing }) => {
  const { userDepartment, selectedDepartment, isSuperAdmin, isRegionManager, isSuperUser, isRegionUser } = useContext(AuthContext);

  const [selectedEmployee, setSelectedEmployee] = useState('');
  const [users, setUsers] = useState([]);
  const [maxAdmins, setMaxAdmins] = useState(2);
  const [telegramSubscribers, setTelegramSubscribers] = useState([]);
  const [whatsappSubscribers, setWhatsappSubscribers] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState({});
  const [localEmployees, setLocalEmployees] = useState(propEmployees || []);
  const [isAuthReady, setIsAuthReady] = useState(false);
  const [duplicateEmails, setDuplicateEmails] = useState([]);
  const [duplicateTelegramIds, setDuplicateTelegramIds] = useState([]);
  const [duplicateWhatsappNumbers, setDuplicateWhatsappNumbers] = useState([]);
  const [departmentPath, setDepartmentPath] = useState('');

  // ✅ NEW: Modal state for role editing
  const [editingUser, setEditingUser] = useState(null);
  const [isRoleModalOpen, setIsRoleModalOpen] = useState(false);

  // ✅ NEW: Modal states for department management
  const [isDepartmentModalOpen, setIsDepartmentModalOpen] = useState(false);
  const [departmentModalUser, setDepartmentModalUser] = useState(null);
  const [departmentModalType, setDepartmentModalType] = useState('add'); // 'add' or 'remove'
  const [selectedDepartmentForAssignment, setSelectedDepartmentForAssignment] = useState('');
  const [regionManagersAndUsers, setRegionManagersAndUsers] = useState([]);
  const [myRegionUsers, setMyRegionUsers] = useState([]);

  // Set department path
  useEffect(() => {
    const deptToUse = isRegionManager || isSuperAdmin() || isSuperUser || isRegionUser ? selectedDepartment : userDepartment;
    if (deptToUse) {
      const path = `departments/${deptToUse}/`;
      setDepartmentPath(path);
    } else {
      setDepartmentPath('');
    }
  }, [userDepartment, selectedDepartment, isRegionManager, isSuperAdmin, isSuperUser, isRegionUser]);

  // Wait for auth state to settle
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (user) {
        console.log('User signed in:', user.uid);
      } else {
        console.warn('No user signed in. Please sign in to test deletion.');
        toast.warn('Sign in to test user management features.');
      }
      setIsAuthReady(true);
    }, (error) => {
      console.error('Auth state error:', error);
      toast.error('Failed to initialize authentication.');
      setIsAuthReady(true);
    });
    return () => unsubscribe();
  }, []);

  // Update localEmployees based on propEmployees
  useEffect(() => {
    console.log('propEmployees received:', propEmployees?.map(emp => ({ employeeId: emp.employeeId, name: emp.name, email: emp.email })));

    if (!propEmployees) {
      console.warn('propEmployees is null or undefined, setting empty array...');
      setLocalEmployees([]);
      return;
    }

    setLocalEmployees(propEmployees);
    console.log('Updated localEmployees:', propEmployees.map(emp => ({ employeeId: emp.employeeId, name: emp.name, email: emp.email })));

    // Detect duplicate emails
    const emailCounts = {};
    propEmployees.forEach(emp => {
      if (emp.email && emp.email !== '') {
        emailCounts[emp.email] = (emailCounts[emp.email] || 0) + 1;
      }
    });
    const duplicates = Object.entries(emailCounts)
      .filter(([_, count]) => count > 1)
      .map(([email]) => email);
    setDuplicateEmails(duplicates);
  }, [propEmployees]);

  // Fetch users, maxAdmins, telegram_subscribers, and whatsapp_subscribers
  useEffect(() => {
    console.log('Fetching data with department path:', departmentPath);

    // Only fetch department-specific data if we have a department path
    if (!departmentPath) {
      console.log('Waiting for department path to be set before fetching data');
      return () => {}; // Return empty cleanup function
    }

    // Determine which department to filter by
    const targetDepartment = isRegionManager || isSuperAdmin() || isSuperUser || isRegionUser
      ? selectedDepartment
      : userDepartment;

    if (!targetDepartment) {
      console.warn('No department selected or assigned, cannot fetch users');
      setUsers([]);
      return () => {}; // Return empty cleanup function
    }

    const usersRef = ref(database, 'users');
    const unsubscribeUsers = onValue(
      usersRef,
      (snapshot) => {
        const data = snapshot.val();
        const allUsers = data
          ? Object.entries(data)
              .map(([uid, user]) => ({
                uid,
                email: user.email || 'Unknown',
                role: user.role || 'user',
                department: user.department || null,
              }))
          : [];

        // Filter users by department
        const userList = allUsers.filter((user) => {
          const hasValidData = user.uid && user.email !== 'Unknown';
          const belongsToDepartment = user.department === targetDepartment;
          return hasValidData && belongsToDepartment;
        });

        if (data) {
          console.log(`Department filtering results for ${targetDepartment}:`, {
            totalUsersInDatabase: Object.keys(data).length,
            usersInTargetDepartment: userList.length,
            usersWithoutDepartment: allUsers.filter(u => !u.department).length,
            targetDepartment: targetDepartment
          });
        }

        console.log(`Filtered users for department ${targetDepartment}:`, userList);
        setUsers(userList);
      },
      (error) => {
        console.error('Error fetching users:', error);
        toast.error('Failed to load users.');
      }
    );

    const maxAdminsRef = ref(database, `${departmentPath}config/maxAdmins`);
    get(maxAdminsRef)
      .then((snapshot) => {
        if (snapshot.exists()) {
          console.log('Found maxAdmins in department config:', snapshot.val());
          setMaxAdmins(snapshot.val());
        } else {
          console.warn(`maxAdmins not set in department ${userDepartment}. Using default: 2`);
          toast.warn('Admin limit not configured. Contact support.');
        }
      })
      .catch((error) => {
        console.error('Error fetching maxAdmins:', error);
        toast.error('Failed to load admin limit.');
      });

    const telegramSubscribersRef = ref(database, `${departmentPath}telegram_subscribers`);
    const unsubscribeTelegram = onValue(
      telegramSubscribersRef,
      (snapshot) => {
        const data = snapshot.val();
        console.log('Telegram subscribers data:', data);
        const subscriberList = data
          ? Object.entries(data).map(([id, details]) => ({
              id,
              status: details.status || 'unknown',
              subscribed: details.subscribed || false,
            }))
          : [];
        setTelegramSubscribers(subscriberList);
        // Detect duplicate Telegram IDs
        const idCounts = {};
        subscriberList.forEach(sub => {
          idCounts[sub.id] = (idCounts[sub.id] || 0) + 1;
        });
        const duplicateIds = Object.entries(idCounts)
          .filter(([_, count]) => count > 1)
          .map(([id]) => id);
        setDuplicateTelegramIds(duplicateIds);
      },
      (error) => {
        console.error('Error fetching Telegram subscribers:', error);
        toast.error('Failed to load Telegram subscribers.');
      }
    );

    const whatsappSubscribersRef = ref(database, `${departmentPath}whatsapp_subscribers`);
    const unsubscribeWhatsapp = onValue(
      whatsappSubscribersRef,
      (snapshot) => {
        const data = snapshot.val();
        console.log('WhatsApp subscribers data:', data);
        const subscriberList = data
          ? Object.entries(data).map(([number, details]) => ({
              number,
              status: details.status || 'unknown',
              subscribed: details.subscribed || false,
            }))
          : [];
        setWhatsappSubscribers(subscriberList);
        // Detect duplicate WhatsApp numbers
        const numberCounts = {};
        subscriberList.forEach(sub => {
          numberCounts[sub.number] = (numberCounts[sub.number] || 0) + 1;
        });
        const duplicateNumbers = Object.entries(numberCounts)
          .filter(([_, count]) => count > 1)
          .map(([number]) => number);
        setDuplicateWhatsappNumbers(duplicateNumbers);
      },
      (error) => {
        console.error('Error fetching WhatsApp subscribers:', error);
        toast.error('Failed to load WhatsApp subscribers.');
      }
    );

    return () => {
      unsubscribeUsers();
      unsubscribeTelegram();
      unsubscribeWhatsapp();
    };
  }, [departmentPath, selectedDepartment, userDepartment, isRegionManager, isSuperAdmin, isSuperUser, isRegionUser]);

  // Map employees to select options, filtering out those who already have user accounts
  const employeeOptions = useMemo(() => {
    console.log('Recalculating employeeOptions:', localEmployees.map(emp => ({ employeeId: emp.employeeId, name: emp.name, email: emp.email })));

    // Get list of emails that already have user accounts
    const existingUserEmails = new Set(users.map(user => user.email.toLowerCase()));

    const availableEmployees = localEmployees
      .filter((emp) => emp && emp.email && emp.email !== '')
      .filter((emp) => !existingUserEmails.has(emp.email.toLowerCase())) // Filter out existing users
      .map((emp) => ({
        employeeId: emp.employeeId,
        name: emp.name,
        email: emp.email,
      }))
      .sort((a, b) => a.name.localeCompare(b.name));

    console.log('Available employees (not yet users):', availableEmployees.length, 'out of', localEmployees.length);
    console.log('Existing user emails:', Array.from(existingUserEmails));

    return availableEmployees;
  }, [localEmployees, users]);

  const selectedEmail = employeeOptions.find((opt) => opt.name === selectedEmployee)?.email || '';

  // ✅ NEW: Role permission helper functions
  const getCurrentUserRole = () => {
    if (isSuperAdmin()) return 'super-admin';
    if (isRegionManager) return 'region-manager';
    if (isSuperUser) return 'super-user';
    if (isRegionUser) return 'region-user';

    // Find current user in users list to check if admin
    const currentUser = auth.currentUser;
    if (currentUser) {
      const userRecord = users.find(u => u.email === currentUser.email);
      if (userRecord?.role === 'admin') return 'admin';
    }

    return 'user';
  };

  const getCurrentUserId = () => {
    return auth.currentUser?.uid || null;
  };

  const getRoleLevel = (role) => {
    const levels = {
      'super-admin': 4,      // Highest - can manage all
      'region-manager': 3,   // Can manage admin, user
      'admin': 2,           // Can manage admin, user
      'user': 1             // Cannot manage anyone
    };
    return levels[role] || 0;
  };

  const canModifyUser = (currentUserRole, targetUserRole) => {
    const currentLevel = getRoleLevel(currentUserRole);
    const targetLevel = getRoleLevel(targetUserRole);

    // Special case: super-admin can manage all EXCEPT other super-admins
    if (currentUserRole === 'super-admin') {
      return targetUserRole !== 'super-admin'; // ✅ UPDATED: Cannot manage other super-admins
    }

    // Special case: admin can manage other admins (same level) and users (below)
    if (currentUserRole === 'admin' && targetUserRole === 'admin') {
      return true;
    }

    // For all other cases: can only manage users BELOW their level
    return currentLevel > targetLevel;
  };

  const canPromoteToRole = (currentUserRole, targetRole) => {
    const roleHierarchy = {
      'super-admin': ['user', 'admin', 'region-manager', 'super-user', 'region-user'], // ✅ UPDATED: Cannot create super-admin
      'region-manager': ['user', 'admin', 'region-user'], // ✅ UPDATED: Can create region-user
      'admin': ['user', 'admin'],
      'user': []
    };

    return roleHierarchy[currentUserRole]?.includes(targetRole) || false;
  };

  const isProtectedRole = (role) => {
    return ['super-admin', 'region-manager', 'admin'].includes(role);
  };

  const isOwnAccount = (targetUserId) => {
    return getCurrentUserId() === targetUserId;
  };

  // ✅ NEW: Helper function to check if user deletion should be blocked
  const canDeleteUser = (currentUserRole, targetUser, targetUserId) => {
    const currentUserId = getCurrentUserId();

    // Block self-deletion for protected roles
    if (targetUserId === currentUserId && ['super-admin', 'region-manager', 'admin'].includes(targetUser.role)) {
      return false;
    }

    // Block region-manager from deleting super-admin
    if (currentUserRole === 'region-manager' && targetUser.role === 'super-admin') {
      return false;
    }

    // Block admin from deleting super-admin or region-manager
    if (currentUserRole === 'admin' && ['super-admin', 'region-manager'].includes(targetUser.role)) {
      return false;
    }

    // Block users from deleting anyone
    if (currentUserRole === 'user') {
      return false;
    }

    return true;
  };

  const getDeletionBlockReason = (currentUserRole, targetUser, targetUserId) => {
    const currentUserId = getCurrentUserId();

    if (targetUserId === currentUserId && ['super-admin', 'region-manager', 'admin'].includes(targetUser.role)) {
      return 'Cannot delete your own account for security';
    }

    if (currentUserRole === 'region-manager' && targetUser.role === 'super-admin') {
      return 'Cannot delete super-admin - insufficient privileges';
    }

    if (currentUserRole === 'admin' && ['super-admin', 'region-manager'].includes(targetUser.role)) {
      return `Cannot delete ${targetUser.role} - insufficient privileges`;
    }

    if (currentUserRole === 'user') {
      return 'Users cannot delete accounts';
    }

    return null;
  };

  // ✅ NEW: Department Management Helper Functions
  const getRegionManagersAndUsers = useCallback(async () => {
    try {
      const usersRef = ref(database, 'users');
      const snapshot = await get(usersRef);

      if (!snapshot.exists()) return [];

      const data = snapshot.val();
      const regionUsers = Object.entries(data)
        .map(([uid, user]) => ({
          uid,
          email: user.email || 'Unknown',
          role: user.role || 'user',
          department: user.department || null,
          managedDepartments: user.managedDepartments || [],
          displayName: user.displayName || user.email || 'Unknown'
        }))
        .filter(user => ['region-manager', 'region-user'].includes(user.role))
        .sort((a, b) => {
          // Sort by role first (region-manager before region-user), then by email
          if (a.role !== b.role) {
            return a.role === 'region-manager' ? -1 : 1;
          }
          return a.email.localeCompare(b.email);
        });

      return regionUsers;
    } catch (error) {
      console.error('Error fetching region managers and users:', error);
      toast.error('Failed to load region managers and users.');
      return [];
    }
  }, []);

  const getMyRegionUsers = useCallback(async () => {
    try {
      const currentUserId = getCurrentUserId();
      if (!currentUserId) return [];

      // First, get current region-manager's managed departments
      const currentUserRef = ref(database, `users/${currentUserId}`);
      const currentUserSnapshot = await get(currentUserRef);

      if (!currentUserSnapshot.exists()) {
        console.warn('Current user data not found');
        return [];
      }

      const currentUserData = currentUserSnapshot.val();
      const myManagedDepartments = currentUserData.managedDepartments || [];

      console.log('Region-manager managed departments:', myManagedDepartments);

      if (myManagedDepartments.length === 0) {
        console.log('Region-manager has no managed departments');
        return [];
      }

      // Now get all users and filter region-users in controlled departments
      const usersRef = ref(database, 'users');
      const snapshot = await get(usersRef);

      if (!snapshot.exists()) return [];

      const data = snapshot.val();
      const myRegionUsers = Object.entries(data)
        .map(([uid, user]) => ({
          uid,
          email: user.email || 'Unknown',
          role: user.role || 'user',
          department: user.department || null,
          managedDepartments: user.managedDepartments || [],
          displayName: user.displayName || user.email || 'Unknown',
          createdBy: user.createdBy || null
        }))
        .filter(user =>
          user.role === 'region-user' &&
          user.department &&
          myManagedDepartments.includes(user.department)
        )
        .sort((a, b) => a.email.localeCompare(b.email));

      console.log(`Found ${myRegionUsers.length} region-users in controlled departments:`,
        myRegionUsers.map(u => `${u.email} (${u.department})`));

      return myRegionUsers;
    } catch (error) {
      console.error('Error fetching my region users:', error);
      toast.error('Failed to load your region users.');
      return [];
    }
  }, []);

  const getAllDepartments = useCallback(async () => {
    try {
      const departmentsRef = ref(database, 'departments');
      const snapshot = await get(departmentsRef);

      if (!snapshot.exists()) return [];

      const data = snapshot.val();
      return Object.entries(data).map(([deptId, dept]) => ({
        id: deptId,
        name: dept.department_name || deptId
      }));
    } catch (error) {
      console.error('Error fetching departments:', error);
      return [];
    }
  }, []);

  const getDepartmentConflicts = useCallback((departmentId, excludeUserId = null) => {
    const conflicts = [];

    regionManagersAndUsers.forEach(user => {
      if (user.uid === excludeUserId) return;
      if (user.role === 'region-manager' && user.managedDepartments.includes(departmentId)) {
        conflicts.push(user);
      }
    });

    return conflicts;
  }, [regionManagersAndUsers]);

  const canAssignDepartment = useCallback(async (departmentId, targetUserId, currentUserRole) => {
    // Super-admin can assign any department
    if (currentUserRole === 'super-admin') {
      // Check for conflicts only if assigning to region-manager
      const targetUser = regionManagersAndUsers.find(u => u.uid === targetUserId);
      if (targetUser?.role === 'region-manager') {
        const conflicts = getDepartmentConflicts(departmentId, targetUserId);
        return conflicts.length === 0;
      }
      return true;
    }

    // Region-manager can only assign from their own managed departments
    if (currentUserRole === 'region-manager') {
      // Get current region-manager's managed departments
      const currentUserId = getCurrentUserId();
      const currentUserEmail = auth.currentUser?.email;

      console.log('canAssignDepartment: Region-manager check started:', {
        currentUserId,
        currentUserEmail,
        departmentId,
        targetUserId
      });

      // Try multiple sources to find current user's managed departments
      let currentUserData = null;
      let dataSource = '';

      // 1. Try regionManagersAndUsers (populated for super-admin view)
      if (regionManagersAndUsers.length > 0) {
        currentUserData = regionManagersAndUsers.find(u => u.uid === currentUserId);
        if (currentUserData) dataSource = 'regionManagersAndUsers';
      }

      // 2. Try users array (main user list)
      if (!currentUserData && users.length > 0) {
        currentUserData = users.find(u => u.uid === currentUserId || u.email === currentUserEmail);
        if (currentUserData) dataSource = 'users';
      }

      // 3. If still not found, fetch directly from database (for region-managers)
      if (!currentUserData && currentUserId) {
        console.log('canAssignDepartment: Fetching user data directly from database');
        try {
          const userRef = ref(database, `users/${currentUserId}`);
          const snapshot = await get(userRef);
          if (snapshot.exists()) {
            currentUserData = {
              uid: currentUserId,
              ...snapshot.val()
            };
            dataSource = 'database';
            console.log('canAssignDepartment: Fetched from database:', currentUserData);
          }
        } catch (error) {
          console.error('canAssignDepartment: Error fetching from database:', error);
        }
      }

      console.log('canAssignDepartment: User data found:', {
        dataSource,
        currentUserData: currentUserData ? {
          uid: currentUserData.uid,
          email: currentUserData.email,
          role: currentUserData.role,
          managedDepartments: currentUserData.managedDepartments
        } : null
      });

      // If still not found or no managed departments
      if (!currentUserData) {
        console.error('canAssignDepartment: Current user data not found');
        return false;
      }

      if (!currentUserData.managedDepartments || !Array.isArray(currentUserData.managedDepartments)) {
        console.warn('canAssignDepartment: No managedDepartments found for region-manager:', {
          uid: currentUserData.uid,
          email: currentUserData.email,
          managedDepartments: currentUserData.managedDepartments
        });
        return false;
      }

      const myManagedDepartments = currentUserData.managedDepartments;
      const canAssign = myManagedDepartments.includes(departmentId);

      console.log('canAssignDepartment: Final check:', {
        departmentId,
        myManagedDepartments,
        canAssign,
        dataSource
      });

      return canAssign;
    }

    return false;
  }, [regionManagersAndUsers, getDepartmentConflicts, users]);

  const getAvailableDepartmentsForUser = useCallback(async (targetUserId, currentUserRole) => {
    console.log('getAvailableDepartmentsForUser called:', { targetUserId, currentUserRole });

    const allDepartments = await getAllDepartments();
    console.log('All departments:', allDepartments);

    const targetUser = regionManagersAndUsers.find(u => u.uid === targetUserId) ||
                      myRegionUsers.find(u => u.uid === targetUserId);

    console.log('Target user found:', targetUser);

    if (!targetUser) {
      console.warn('Target user not found');
      return [];
    }

    const currentlyAssigned = targetUser.managedDepartments || [];
    console.log('Currently assigned departments:', currentlyAssigned);

    const availableDepartments = [];

    for (const dept of allDepartments) {
      // Don't show already assigned departments
      if (currentlyAssigned.includes(dept.id)) {
        console.log(`Department ${dept.id} already assigned, skipping`);
        continue;
      }

      // Check if current user can assign this department
      const canAssign = await canAssignDepartment(dept.id, targetUserId, currentUserRole);
      console.log(`Can assign ${dept.id}:`, canAssign);

      if (canAssign) {
        availableDepartments.push(dept);
      }
    }

    console.log('Available departments for assignment:', availableDepartments);
    return availableDepartments;
  }, [getAllDepartments, regionManagersAndUsers, myRegionUsers, canAssignDepartment]);

  // ✅ NEW: Department Assignment Functions
  const handleAddDepartmentToUser = useCallback(async (userId, departmentId) => {
    try {
      const currentUserRole = getCurrentUserRole();

      // Validate permission
      if (!(await canAssignDepartment(departmentId, userId, currentUserRole))) {
        const conflicts = getDepartmentConflicts(departmentId, userId);
        if (conflicts.length > 0) {
          toast.error(`Cannot assign ${departmentId}: Already assigned to ${conflicts[0].email}`);
        } else {
          toast.error('You do not have permission to assign this department.');
        }
        return false;
      }

      const userRef = ref(database, `users/${userId}/managedDepartments`);
      const snapshot = await get(userRef);
      const currentDepartments = snapshot.exists() ? snapshot.val() : [];

      if (currentDepartments.includes(departmentId)) {
        toast.warning('Department already assigned to this user.');
        return false;
      }

      const updatedDepartments = [...currentDepartments, departmentId];
      await set(userRef, updatedDepartments);

      toast.success(`Successfully assigned ${departmentId} to user.`);

      // Refresh data
      if (currentUserRole === 'super-admin') {
        const updatedData = await getRegionManagersAndUsers();
        setRegionManagersAndUsers(updatedData);
      } else if (currentUserRole === 'region-manager') {
        const updatedData = await getMyRegionUsers();
        setMyRegionUsers(updatedData);
      }

      return true;
    } catch (error) {
      console.error('Error adding department to user:', error);
      toast.error('Failed to assign department. Please try again.');
      return false;
    }
  }, [canAssignDepartment, getDepartmentConflicts, getRegionManagersAndUsers, getMyRegionUsers]);

  const handleRemoveDepartmentFromUser = useCallback(async (userId, departmentId) => {
    try {
      const userRef = ref(database, `users/${userId}/managedDepartments`);
      const snapshot = await get(userRef);
      const currentDepartments = snapshot.exists() ? snapshot.val() : [];

      if (!currentDepartments.includes(departmentId)) {
        toast.warning('Department not assigned to this user.');
        return false;
      }

      const updatedDepartments = currentDepartments.filter(dept => dept !== departmentId);
      await set(userRef, updatedDepartments);

      toast.success(`Successfully removed ${departmentId} from user.`);

      // Refresh data
      const currentUserRole = getCurrentUserRole();
      if (currentUserRole === 'super-admin') {
        const updatedData = await getRegionManagersAndUsers();
        setRegionManagersAndUsers(updatedData);
      } else if (currentUserRole === 'region-manager') {
        const updatedData = await getMyRegionUsers();
        setMyRegionUsers(updatedData);
      }

      return true;
    } catch (error) {
      console.error('Error removing department from user:', error);
      toast.error('Failed to remove department. Please try again.');
      return false;
    }
  }, [getRegionManagersAndUsers, getMyRegionUsers]);

  const openDepartmentModal = useCallback((user, type) => {
    setDepartmentModalUser(user);
    setDepartmentModalType(type);
    setSelectedDepartmentForAssignment('');
    setIsDepartmentModalOpen(true);
  }, []);

  const closeDepartmentModal = useCallback(() => {
    setIsDepartmentModalOpen(false);
    setDepartmentModalUser(null);
    setDepartmentModalType('add');
    setSelectedDepartmentForAssignment('');
  }, []);

  const getNextAllowedRole = (currentUserRole, targetUserCurrentRole, targetUserId) => {
    // Users have no role management access
    if (currentUserRole === 'user') {
      return null;
    }

    // Prevent protected roles from changing their own roles
    if (isOwnAccount(targetUserId) && isProtectedRole(targetUserCurrentRole)) {
      return null;
    }

    const allowedRoles = {
      'super-admin': {
        'user': 'admin',
        'admin': 'region-manager',
        'region-manager': 'super-user',
        'super-user': 'region-user',
        'region-user': 'user'                      // ✅ UPDATED: Cycles back to user, not super-admin
        // ✅ REMOVED: 'super-admin': 'user' - super-admin cannot manage other super-admins
      },
      'region-manager': {
        'user': 'admin',
        'admin': 'region-user',                    // ✅ UPDATED: Can promote admin to region-user
        'region-user': 'user'                      // ✅ NEW: Can demote region-user to user
      },
      'admin': {
        'user': 'admin',
        'admin': 'user'
      }
    };

    return allowedRoles[currentUserRole]?.[targetUserCurrentRole] || null;
  };

  const hasRoleManagementAccess = () => {
    const currentUserRole = getCurrentUserRole();
    return ['super-admin', 'region-manager', 'admin'].includes(currentUserRole);
  };

  const getAllowedRoles = (currentUserRole) => {
    const permissions = {
      'super-admin': ['region-manager', 'super-user', 'region-user', 'admin', 'user'], // ✅ UPDATED: Cannot create super-admin
      'region-manager': ['region-user', 'admin', 'user'], // ✅ UPDATED: Can create region-user
      'admin': ['admin', 'user'],
      'user': ['none']
    };
    return permissions[currentUserRole] || ['none'];
  };

  // ✅ FIXED: Modal helper functions
  const getAvailableRolesForUser = (currentUserRole, targetUser) => {
    // Get all roles that the current user can assign
    const allPossibleRoles = getAllowedRoles(currentUserRole);

    console.log('Debug getAvailableRolesForUser:', {
      currentUserRole,
      targetUser: targetUser.role,
      allPossibleRoles
    });

    return allPossibleRoles
      .filter(role => role !== 'none' && role !== 'all roles') // Filter out display values
      .filter(role => role !== targetUser.role) // Exclude current role
      .filter(role => canPromoteToRole(currentUserRole, role)) // Double-check permission
      .map(role => ({
        value: role,
        label: getRoleDisplayName(role)
      }));
  };

  const getRoleDisplayName = (role) => {
    const roleDisplayNames = {
      'user': 'User',
      'admin': 'Administrator',
      'region-manager': 'Region Manager',
      'super-user': 'Super User',
      'region-user': 'Region User',
      'super-admin': 'Super Administrator'
    };
    return roleDisplayNames[role] || role;
  };

  const handleOpenRoleModal = (user) => {
    console.log('Opening role modal for user:', user);
    console.log('Current user role:', getCurrentUserRole());
    console.log('Available roles:', getAvailableRolesForUser(getCurrentUserRole(), user));
    setEditingUser(user);
    setIsRoleModalOpen(true);
  };

  const handleCloseRoleModal = () => {
    setEditingUser(null);
    setIsRoleModalOpen(false);
  };

  const handleModalRoleSave = async (uid, newRole) => {
    // Use the existing handleRoleChange logic but adapted for modal
    const currentUserRole = getCurrentUserRole();
    const currentUserId = getCurrentUserId();

    // Validation checks
    if (currentUserRole === 'user') {
      throw new Error('Users do not have access to role management.');
    }

    if (!canModifyUser(currentUserRole, editingUser.role)) {
      throw new Error(`You cannot modify users with ${editingUser.role} role.`);
    }

    if (uid === currentUserId && isProtectedRole(editingUser.role)) {
      throw new Error(`You cannot change your own role as ${editingUser.role}.`);
    }

    if (!canPromoteToRole(currentUserRole, newRole)) {
      throw new Error(`You don't have permission to assign ${newRole} role.`);
    }

    // Admin limit check
    if (newRole === 'admin') {
      const currentAdmins = users.filter((u) => u.role === 'admin').length;
      if (currentAdmins >= maxAdmins) {
        throw new Error(`Cannot create more admins. Maximum allowed: ${maxAdmins}. Current: ${currentAdmins}`);
      }
    }

    // Perform the role change
    await set(ref(database, `users/${uid}/role`), newRole);

    // Handle managed departments
    if (newRole === 'region-manager' || newRole === 'region-user') {
      await set(ref(database, `users/${uid}/managedDepartments`), []);
      toast.success(`Role changed to ${newRole}. Please configure managed departments separately.`);
    } else {
      if (editingUser.role === 'region-manager' || editingUser.role === 'region-user') {
        await set(ref(database, `users/${uid}/managedDepartments`), null);
      }
      toast.success(`Role changed to ${newRole}.`);
    }
  };

  const handleAddUser = async () => {
    if (isSubmitting || isInitializing || isLoading || !selectedEmail) {
      if (!selectedEmail) toast.error('Please select an employee with a valid email.');
      return;
    }

    setIsLoading(true);
    try {
      const token = await auth.currentUser.getIdToken();
      const addUserUrl = process.env.REACT_APP_ADD_USER_FUNCTION_URL;

      if (!addUserUrl) {
        throw new Error('ADD_USER_FUNCTION_URL is not configured. Please check your environment variables.');
      }

      const response = await fetch(addUserUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: selectedEmail,
          displayName: selectedEmployee,
        }),
      });

      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.error || 'Failed to add user');
      }

      // Set all required user information
      const currentDept = isRegionManager || isSuperAdmin() || isSuperUser || isRegionUser ? selectedDepartment : userDepartment;
      if (result.uid) {
        // Set all user properties individually
        const userRef = ref(database, `users/${result.uid}`);
        const userData = {
          department: currentDept,
          role: 'user', // Default role
          managedDepartments: [], // Empty array for managedDepartments
          email: selectedEmail, // Ensure email is stored
          displayName: selectedEmployee, // Ensure displayName is stored
          uid: result.uid // Store the uid
        };

        await set(userRef, userData);
        console.log('User created with complete information:', {
          uid: result.uid,
          email: selectedEmail,
          displayName: selectedEmployee,
          department: currentDept,
          role: 'user',
          managedDepartments: []
        });
      }

      await sendPasswordResetEmail(auth, selectedEmail);
      toast.success(`User created for ${selectedEmployee}. A password reset email has been sent.`);
      setSelectedEmployee('');
    } catch (error) {
      if (error.message.includes('email-already-in-use')) {
        toast.error('This email is already registered. Please delete the existing account or contact support.');
      } else if (error.message.includes('too-many-requests')) {
        toast.error('Too many attempts. Please try again later.');
      } else {
        toast.error(`Failed to create user: ${error.message}`);
      }
      console.error('Error creating user:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRoleChange = useCallback(
    async (uid, currentRole) => {
      if (isSubmitting || isInitializing || actionLoading[uid]) {
        return;
      }

      const currentUserRole = getCurrentUserRole();
      const currentUserId = getCurrentUserId();

      // ✅ NEW: Block users from any role management
      if (currentUserRole === 'user') {
        toast.error('Users do not have access to role management.');
        return;
      }

      // ✅ NEW: Check if user can modify target user
      if (!canModifyUser(currentUserRole, currentRole)) {
        toast.error(`You cannot modify users with ${currentRole} role. You can only manage users below your level.`);
        return;
      }

      // ✅ NEW: Prevent protected roles from changing their own roles
      if (uid === currentUserId && isProtectedRole(currentRole)) {
        toast.error(`You cannot change your own role as ${currentRole}. This is for security purposes.`);
        return;
      }

      // ✅ NEW: Get next allowed role based on permissions
      const newRole = getNextAllowedRole(currentUserRole, currentRole, uid);

      if (!newRole) {
        if (isOwnAccount(uid) && isProtectedRole(currentRole)) {
          toast.error(`Protected roles cannot change their own permissions.`);
        } else {
          toast.error(`No role changes available for this user.`);
        }
        return;
      }

      if (newRole === currentRole) {
        toast.info(`No role change available for this user.`);
        return;
      }

      // ✅ NEW: Double-check permission for new role
      if (!canPromoteToRole(currentUserRole, newRole)) {
        toast.error(`You don't have permission to assign ${newRole} role.`);
        return;
      }

      // ✅ ENHANCED: Admin limit check with role-specific messaging
      if (newRole === 'admin') {
        const currentAdmins = users.filter((u) => u.role === 'admin').length;
        if (currentAdmins >= maxAdmins) {
          if (currentUserRole === 'admin') {
            toast.error(`Cannot create more admins. Maximum allowed in this department: ${maxAdmins}. Current admins: ${currentAdmins}`);
          } else {
            toast.error(`Cannot assign more admins. Maximum allowed: ${maxAdmins}.`);
          }
          return;
        }
      }

      const targetUser = users.find((u) => u.uid === uid);
      const confirmMessage = isOwnAccount(uid)
        ? `Change your own role to ${newRole}? This action cannot be undone.`
        : `Change role for ${targetUser?.email || 'Unknown'} to ${newRole}?`;

      if (!window.confirm(confirmMessage)) return;

      setActionLoading((prev) => ({ ...prev, [uid]: true }));
      try {
        await set(ref(database, `users/${uid}/role`), newRole);

        // If changing to region-manager or region-user, we might need to set managedDepartments
        if (newRole === 'region-manager' || newRole === 'region-user') {
          // For now, set empty array - this should be configured separately
          await set(ref(database, `users/${uid}/managedDepartments`), []);
          toast.success(`Role changed to ${newRole}. Please configure managed departments separately.`);
        } else {
          // Remove managedDepartments if not region-manager or region-user
          if (currentRole === 'region-manager' || currentRole === 'region-user') {
            await set(ref(database, `users/${uid}/managedDepartments`), null);
          }
          toast.success(`Role changed to ${newRole}.`);
        }
      } catch (error) {
        toast.error('Failed to change role.');
        console.error('Error changing role:', error);
      } finally {
        setActionLoading((prev) => ({ ...prev, [uid]: false }));
      }
    },
    [users, maxAdmins, isSubmitting, isInitializing, actionLoading]
  );

  const handleResetPassword = useCallback(
    async (email, uid) => {
      if (isSubmitting || isInitializing || actionLoading[uid]) {
        return;
      }

      if (!window.confirm(`Send password reset email to ${email}?`)) return;

      setActionLoading((prev) => ({ ...prev, [uid]: true }));
      try {
        await sendPasswordResetEmail(auth, email);
        toast.success(`Password reset email sent to ${email}.`);
      } catch (error) {
        if (error.code === 'auth/too-many-requests') {
          toast.error('Too many attempts. Please try again later.');
        } else {
          toast.error('Failed to send password reset email.');
        }
        console.error('Error sending password reset:', error);
      } finally {
        setActionLoading((prev) => ({ ...prev, [uid]: false }));
      }
    },
    [isSubmitting, isInitializing, actionLoading]
  );

  const handleDeleteUser = useCallback(
    async (uid, email) => {
      if (isSubmitting || isInitializing || actionLoading[uid]) {
        return;
      }

      if (!uid) {
        toast.error('Cannot delete user: Invalid user ID.');
        console.error('Invalid UID in handleDeleteUser:', { uid, email });
        return;
      }

      const currentUserRole = getCurrentUserRole();
      const currentUserId = getCurrentUserId();
      const targetUser = users.find(u => u.uid === uid);

      // ✅ NEW: Prevent self-deletion for protected roles
      if (uid === currentUserId) {
        if (currentUserRole === 'super-admin') {
          toast.error('🚫 Super-admin cannot delete their own account for security reasons.');
          return;
        }
        if (currentUserRole === 'region-manager') {
          toast.error('🚫 Region-manager cannot delete their own account for security reasons.');
          return;
        }
        if (currentUserRole === 'admin') {
          toast.error('🚫 Admin cannot delete their own account for security reasons.');
          return;
        }
      }

      // ✅ NEW: Prevent region-manager from deleting super-admin accounts
      if (currentUserRole === 'region-manager' && targetUser?.role === 'super-admin') {
        toast.error('🚫 Region-manager cannot delete super-admin accounts. Insufficient privileges.');
        return;
      }

      // ✅ NEW: Prevent admin from deleting super-admin or region-manager accounts
      if (currentUserRole === 'admin' && (targetUser?.role === 'super-admin' || targetUser?.role === 'region-manager')) {
        toast.error(`🚫 Admin cannot delete ${targetUser.role} accounts. Insufficient privileges.`);
        return;
      }

      // ✅ NEW: Prevent user role from deleting anyone
      if (currentUserRole === 'user') {
        toast.error('🚫 Users do not have permission to delete accounts.');
        return;
      }

      const deleteUrl = `${process.env.REACT_APP_DELETE_USER_FUNCTION_URL}?uid=${uid}`;

      if (!process.env.REACT_APP_DELETE_USER_FUNCTION_URL) {
        toast.error('DELETE_USER_FUNCTION_URL is not configured. Please check your environment variables.');
        console.error('Environment variable missing:', {
          REACT_APP_DELETE_USER_FUNCTION_URL: process.env.REACT_APP_DELETE_USER_FUNCTION_URL
        });
        return;
      }

      // Enhanced confirmation message with role information
      const confirmMessage = uid === currentUserId
        ? `⚠️ Delete your own account (${email})? This cannot be undone and you will be logged out.`
        : `⚠️ Delete user ${email} (${targetUser?.role || 'unknown role'})? This cannot be undone.`;

      if (!window.confirm(confirmMessage)) return;

      setActionLoading((prev) => ({ ...prev, [uid]: true }));
      try {
        const token = await auth.currentUser.getIdToken();
        const response = await fetch(deleteUrl, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        const result = await response.json();
        if (!response.ok) {
          throw new Error(result.error || 'Failed to delete user');
        }

        console.log('Delete user response:', result);
        toast.success(`User ${email} deleted.`);
      } catch (error) {
        if (error.message.includes('Failed to fetch')) {
          toast.error('Failed to delete user: Network or CORS error. Check server configuration.');
        } else {
          toast.error(`Failed to delete user: ${error.message}`);
        }
        console.error('Error deleting user:', error, { uid, email, deleteUrl });
      } finally {
        setActionLoading((prev) => ({ ...prev, [uid]: false }));
      }
    },
    [isSubmitting, isInitializing, actionLoading]
  );

  const handleToggleSubscription = useCallback(
    async (type, id, currentSubscribed) => {
      if (isSubmitting || isInitializing || actionLoading[id]) {
        return;
      }

      const newSubscribed = !currentSubscribed;
      const path = type === 'telegram' ? `${departmentPath}telegram_subscribers/${id}` : `${departmentPath}whatsapp_subscribers/${id}`;
      const displayId = type === 'telegram' ? id : id.replace(/"/g, '');

      if (!window.confirm(`${newSubscribed ? 'Resubscribe' : 'Unsubscribe'} ${displayId} from ${type} notifications?`)) return;

      setActionLoading((prev) => ({ ...prev, [id]: true }));
      try {
        await set(ref(database, path), {
          status: 'active',
          subscribed: newSubscribed,
        });
        toast.success(`${displayId} ${newSubscribed ? 'resubscribed to' : 'unsubscribed from'} ${type} notifications.`);
      } catch (error) {
        toast.error(`Failed to update ${type} subscription for ${displayId}.`);
        console.error(`Error updating ${type} subscription:`, error);
      } finally {
        setActionLoading((prev) => ({ ...prev, [id]: false }));
      }
    },
    [isSubmitting, isInitializing, actionLoading, departmentPath]
  );

  // ✅ NEW: Load department management data (after all functions are defined)
  useEffect(() => {
    const loadDepartmentData = async () => {
      const currentUserRole = getCurrentUserRole();

      if (currentUserRole === 'super-admin') {
        const data = await getRegionManagersAndUsers();
        setRegionManagersAndUsers(data);
      } else if (currentUserRole === 'region-manager') {
        const data = await getMyRegionUsers();
        setMyRegionUsers(data);
      }
    };

    if (!isInitializing && (isSuperAdmin() || isRegionManager)) {
      loadDepartmentData();
    }
  }, [isInitializing, isSuperAdmin, isRegionManager, getRegionManagersAndUsers, getMyRegionUsers]);

  if (!isAuthReady) {
    return (
      <div className="mb-8">
        <h2 className="text-xl font-bold mb-4">Admin User Manager</h2>
        <p className="text-gray-500">Initializing authentication...</p>
      </div>
    );
  }

  return (
    <AdminUserManagerErrorBoundary>
      <div className="mb-8">
        <h2 className="text-xl font-bold mb-4">Admin User Manager</h2>
        <div className="mb-4">
          <p className="text-sm text-blue-600 font-medium mb-1">
            Managing users for department: {isRegionManager || isSuperAdmin() || isSuperUser || isRegionUser ? selectedDepartment : userDepartment || 'Unknown'}
          </p>

          <p className="text-sm text-gray-600">
            Your role: {getCurrentUserRole()} |
            {getCurrentUserRole() === 'user'
              ? 'No role management access'
              : `Can manage: ${getAllowedRoles(getCurrentUserRole()).join(', ')}`
            }
          </p>
          {getCurrentUserRole() === 'admin' && (
            <p className="text-sm text-green-600">
              ✅ As admin, you can create and manage other admins in your department
            </p>
          )}
          <p className="text-sm text-orange-600">
            🔒 You cannot change your own role or manage users above your level
          </p>
          <p className="text-sm text-red-600">
            🚫 Account deletion restrictions: Super-admin/Region-manager/Admin cannot delete their own accounts. Region-manager cannot delete super-admin accounts.
          </p>
          <p className="text-sm text-gray-700">
            Maximum Admins Allowed: {maxAdmins} (Current Admins: {users.filter((u) => u.role === 'admin').length})
          </p>
          {duplicateEmails.length > 0 && (
            <p className="text-sm text-red-600 mt-2">
              Warning: Duplicate emails detected: {duplicateEmails.join(', ')}. Please ensure unique emails for each employee.
            </p>
          )}
          {duplicateTelegramIds.length > 0 && (
            <p className="text-sm text-red-600 mt-2">
              Warning: Duplicate Telegram IDs detected: {duplicateTelegramIds.join(', ')}. Please ensure unique IDs.
            </p>
          )}
          {duplicateWhatsappNumbers.length > 0 && (
            <p className="text-sm text-red-600 mt-2">
              Warning: Duplicate WhatsApp numbers detected: {duplicateWhatsappNumbers.map(num => num.replace(/"/g, '')).join(', ')}. Please ensure unique numbers.
            </p>
          )}
        </div>
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <select
            value={selectedEmployee}
            onChange={(e) => setSelectedEmployee(e.target.value)}
            className="p-2 border rounded-md w-64"
            disabled={isSubmitting || isInitializing || isLoading || employeeOptions.length === 0}
            aria-label="Select employee to add as user"
          >
            <option value="">
              {employeeOptions.length === 0 ? 'No employees available' : 'Select Employee'}
            </option>
            {employeeOptions.map((emp) => (
              <option key={emp.employeeId} value={emp.name}>
                {emp.name} ({emp.email})
              </option>
            ))}
          </select>
          <button
            onClick={handleAddUser}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400"
            disabled={isSubmitting || isInitializing || isLoading || !selectedEmail || employeeOptions.length === 0}
            aria-label="Add selected employee as user"
          >
            {isLoading ? 'Adding...' : 'Add User'}
          </button>
        </div>
        {employeeOptions.length === 0 && localEmployees.length > 0 && (
          <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <p className="text-sm text-yellow-800">
              ℹ️ All employees with email addresses already have user accounts.
              No new users can be created at this time.
            </p>
          </div>
        )}

        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-2">Users</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border border-gray-300 p-2 text-left">Email</th>
                  <th className="border border-gray-300 p-2 text-left">Department</th>
                  <th className="border border-gray-300 p-2 text-left">Role</th>
                  <th className="border border-gray-300 p-2 text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {users.map((user) => (
                  <tr key={user.uid} className="hover:bg-gray-50">
                    <td className="border border-gray-300 p-2">{user.email}</td>
                    <td className="border border-gray-300 p-2">{user.department || 'Not Set'}</td>
                    <td className="border border-gray-300 p-2">{user.role}</td>
                    <td className="border border-gray-300 p-2 flex gap-2">
                      {hasRoleManagementAccess() ? (
                        <button
                          onClick={() => handleOpenRoleModal(user)}
                          className={`${
                            !canModifyUser(getCurrentUserRole(), user.role)
                              ? 'text-red-600 cursor-not-allowed'
                              : user.uid === getCurrentUserId() && isProtectedRole(user.role)
                                ? 'text-orange-600 cursor-not-allowed'
                                : 'text-blue-600 hover:underline'
                          } disabled:text-gray-400`}
                          disabled={
                            isSubmitting ||
                            isInitializing ||
                            actionLoading[user.uid] ||
                            !canModifyUser(getCurrentUserRole(), user.role) ||
                            (user.uid === getCurrentUserId() && isProtectedRole(user.role))
                          }
                          aria-label={`Edit role for ${user.email}`}
                          title={
                            !canModifyUser(getCurrentUserRole(), user.role)
                              ? `Cannot modify ${user.role} - insufficient privileges`
                              : user.uid === getCurrentUserId() && isProtectedRole(user.role)
                                ? 'Cannot change your own role for security'
                                : `Edit role for ${user.email}`
                          }
                        >
                          {!canModifyUser(getCurrentUserRole(), user.role)
                            ? '🚫 Restricted'
                            : user.uid === getCurrentUserId() && isProtectedRole(user.role)
                              ? '🔒 Protected'
                              : 'Edit Role'
                          }
                        </button>
                      ) : (
                        <span className="text-gray-400 text-sm">No Access</span>
                      )}
                      <button
                        onClick={() => handleResetPassword(user.email, user.uid)}
                        className="text-green-600 hover:underline disabled:text-gray-400"
                        disabled={isSubmitting || isInitializing || actionLoading[user.uid]}
                        aria-label={`Send password reset email to ${user.email}`}
                      >
                        {actionLoading[user.uid] ? 'Sending...' : 'Reset Password'}
                      </button>
                      <button
                        onClick={() => handleDeleteUser(user.uid, user.email)}
                        className={`${
                          !canDeleteUser(getCurrentUserRole(), user, user.uid)
                            ? 'text-red-400 cursor-not-allowed'
                            : 'text-red-600 hover:underline'
                        } disabled:text-gray-400`}
                        disabled={
                          isSubmitting ||
                          isInitializing ||
                          actionLoading[user.uid] ||
                          !canDeleteUser(getCurrentUserRole(), user, user.uid)
                        }
                        aria-label={`Delete user ${user.email}`}
                        title={
                          getDeletionBlockReason(getCurrentUserRole(), user, user.uid) || `Delete user ${user.email}`
                        }
                      >
                        {actionLoading[user.uid]
                          ? 'Deleting...'
                          : !canDeleteUser(getCurrentUserRole(), user, user.uid)
                            ? (user.uid === getCurrentUserId() ? '🔒 Protected' : '🚫 Restricted')
                            : 'Delete'
                        }
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            {users.length === 0 && (
              <p className="text-gray-500 mt-2">
                No users found for department: {isRegionManager || isSuperAdmin() || isSuperUser || isRegionUser ? selectedDepartment : userDepartment || 'Unknown'}
              </p>
            )}
          </div>
        </div>
          {/* ✅ NEW: Department Management Section for Super-Admin */}
        {isSuperAdmin() && (
          <div className="mb-8">
            <h3 className="text-lg font-semibold mb-4">🏢 Department Management (Super-Admin)</h3>
            <p className="text-sm text-blue-600 mb-4">
              📋 Manage department assignments for region-managers and region-users. Each department can only be assigned to one region-manager.
            </p>

            <div className="overflow-x-auto">
              <table className="min-w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-blue-50">
                    <th className="border border-gray-300 p-3 text-left">User Email</th>
                    <th className="border border-gray-300 p-3 text-left">Role</th>
                    <th className="border border-gray-300 p-3 text-left">Assigned Departments</th>
                    <th className="border border-gray-300 p-3 text-left">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {regionManagersAndUsers.map((user) => (
                    <tr key={user.uid} className="hover:bg-gray-50">
                      <td className="border border-gray-300 p-3">{user.email}</td>
                      <td className="border border-gray-300 p-3">
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          user.role === 'region-manager'
                            ? 'bg-purple-100 text-purple-800'
                            : 'bg-green-100 text-green-800'
                        }`}>
                          {user.role}
                        </span>
                      </td>
                      <td className="border border-gray-300 p-3">
                        <div className="flex flex-wrap gap-1">
                          {user.managedDepartments.length > 0 ? (
                            user.managedDepartments.map((dept) => (
                              <span
                                key={dept}
                                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                              >
                                {dept}
                                <button
                                  onClick={() => handleRemoveDepartmentFromUser(user.uid, dept)}
                                  className="ml-1 text-red-600 hover:text-red-800"
                                  title={`Remove ${dept} from ${user.email}`}
                                >
                                  ×
                                </button>
                              </span>
                            ))
                          ) : (
                            <span className="text-gray-500 text-sm">No departments assigned</span>
                          )}
                        </div>
                      </td>
                      <td className="border border-gray-300 p-3">
                        <button
                          onClick={() => openDepartmentModal(user, 'add')}
                          className="text-blue-600 hover:underline text-sm"
                          title={`Add department to ${user.email}`}
                        >
                          + Add Department
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {regionManagersAndUsers.length === 0 && (
                <p className="text-gray-500 mt-4 text-center">No region-managers or region-users found.</p>
              )}
            </div>
          </div>
        )}

        {/* ✅ NEW: Department Management Section for Region-Manager */}
        {isRegionManager && (
          <div className="mb-8">
            <h3 className="text-lg font-semibold mb-4">👥 My Region-Users Department Management</h3>
            <p className="text-sm text-orange-600 mb-4">
              📋 Manage department assignments for region-users in departments you control. You can only assign departments from your own managed departments.
            </p>

            <div className="overflow-x-auto">
              <table className="min-w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-orange-50">
                    <th className="border border-gray-300 p-3 text-left">User Email</th>
                    <th className="border border-gray-300 p-3 text-left">Role</th>
                    <th className="border border-gray-300 p-3 text-left">Assigned Departments</th>
                    <th className="border border-gray-300 p-3 text-left">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {myRegionUsers.map((user) => (
                    <tr key={user.uid} className="hover:bg-gray-50">
                      <td className="border border-gray-300 p-3">{user.email}</td>
                      <td className="border border-gray-300 p-3">
                        <span className="px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">
                          {user.role}
                        </span>
                      </td>
                      <td className="border border-gray-300 p-3">
                        <div className="flex flex-wrap gap-1">
                          {user.managedDepartments.length > 0 ? (
                            user.managedDepartments.map((dept) => (
                              <span
                                key={dept}
                                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800"
                              >
                                {dept}
                                <button
                                  onClick={() => handleRemoveDepartmentFromUser(user.uid, dept)}
                                  className="ml-1 text-red-600 hover:text-red-800"
                                  title={`Remove ${dept} from ${user.email}`}
                                >
                                  ×
                                </button>
                              </span>
                            ))
                          ) : (
                            <span className="text-gray-500 text-sm">No departments assigned</span>
                          )}
                        </div>
                      </td>
                      <td className="border border-gray-300 p-3">
                        <button
                          onClick={() => openDepartmentModal(user, 'add')}
                          className="text-orange-600 hover:underline text-sm"
                          title={`Add department to ${user.email}`}
                        >
                          + Add Department
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {myRegionUsers.length === 0 && (
                <p className="text-gray-500 mt-4 text-center">No region-users found in departments you control.</p>
              )}
            </div>
          </div>
        )}
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-2">Telegram Subscribers</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border border-gray-300 p-2 text-left">Telegram ID</th>
                  <th className="border border-gray-300 p-2 text-left">Status</th>
                  <th className="border border-gray-300 p-2 text-left">Subscribed</th>
                  <th className="border border-gray-300 p-2 text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {telegramSubscribers.map((sub) => (
                  <tr key={sub.id} className="hover:bg-gray-50">
                    <td className="border border-gray-300 p-2">{sub.id}</td>
                    <td className="border border-gray-300 p-2">{sub.status}</td>
                    <td className="border border-gray-300 p-2">{sub.subscribed ? 'Yes' : 'No'}</td>
                    <td className="border border-gray-300 p-2">
                      <button
                        onClick={() => handleToggleSubscription('telegram', sub.id, sub.subscribed)}
                        className="text-blue-600 hover:underline disabled:text-gray-400"
                        disabled={isSubmitting || isInitializing || actionLoading[sub.id]}
                        aria-label={`${sub.subscribed ? 'Unsubscribe' : 'Resubscribe'} Telegram ID ${sub.id}`}
                      >
                        {actionLoading[sub.id] ? 'Updating...' : sub.subscribed ? 'Unsubscribe' : 'Resubscribe'}
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            {telegramSubscribers.length === 0 && <p className="text-gray-500 mt-2">No Telegram subscribers found.</p>}
          </div>
        </div>
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-2">WhatsApp Subscribe</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border border-gray-300 p-2 text-left">Phone Number</th>
                  <th className="border border-gray-300 p-2 text-left">Status</th>
                  <th className="border border-gray-300 p-2 text-left">Subscribed</th>
                  <th className="border border-gray-300 p-2 text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {whatsappSubscribers.map((sub) => (
                  <tr key={sub.number} className="hover:bg-gray-50">
                    <td className="border border-gray-300 p-2">{sub.number.replace(/"/g, '')}</td>
                    <td className="border border-gray-300 p-2">{sub.status}</td>
                    <td className="border border-gray-300 p-2">{sub.subscribed ? 'Yes' : 'No'}</td>
                    <td className="border border-gray-300 p-2">
                      <button
                        onClick={() => handleToggleSubscription('whatsapp', sub.number, sub.subscribed)}
                        className="text-blue-600 hover:underline disabled:text-gray-400"
                        disabled={isSubmitting || isInitializing || actionLoading[sub.number]}
                        aria-label={`${sub.subscribed ? 'Unsubscribe' : 'Resubscribe'} WhatsApp number ${sub.number.replace(/"/g, '')}`}
                      >
                        {actionLoading[sub.number] ? 'Updating...' : sub.subscribed ? 'Unsubscribe' : 'Resubscribe'}
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            {whatsappSubscribers.length === 0 && <p className="text-gray-500 mt-2">No WhatsApp subscribers found.</p>}
          </div>
        </div>



        {/* ✅ NEW: Role Edit Modal */}
        <RoleEditModal
          isOpen={isRoleModalOpen}
          onClose={handleCloseRoleModal}
          user={editingUser}
          availableRoles={editingUser ? getAvailableRolesForUser(getCurrentUserRole(), editingUser) : []}
          onSave={handleModalRoleSave}
          currentUserRole={getCurrentUserRole()}
          maxAdmins={maxAdmins}
          currentAdminCount={users.filter((u) => u.role === 'admin').length}
        />

        {/* ✅ NEW: Department Assignment Modal */}
        <DepartmentAssignmentModal
          isOpen={isDepartmentModalOpen}
          onClose={closeDepartmentModal}
          user={departmentModalUser}
          modalType={departmentModalType}
          selectedDepartment={selectedDepartmentForAssignment}
          onDepartmentChange={setSelectedDepartmentForAssignment}
          onAssign={handleAddDepartmentToUser}
          onRemove={handleRemoveDepartmentFromUser}
          getAvailableDepartments={getAvailableDepartmentsForUser}
          currentUserRole={getCurrentUserRole()}
        />
      </div>
    </AdminUserManagerErrorBoundary>
  );
};

AdminUserManager.propTypes = {
  employees: PropTypes.arrayOf(
    PropTypes.shape({
      employeeId: PropTypes.string.isRequired,
      rowIndex: PropTypes.number,
      name: PropTypes.string,
      srcNumber: PropTypes.string,
      position: PropTypes.string,
      phone: PropTypes.string,
      email: PropTypes.string,
      createdAt: PropTypes.string,
      updatedAt: PropTypes.string,
    })
  ),
  isSubmitting: PropTypes.bool.isRequired,
  isInitializing: PropTypes.bool.isRequired,
};

export default React.memo(AdminUserManager);