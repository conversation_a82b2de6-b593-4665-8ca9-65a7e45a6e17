const { initializeApp } = require('firebase-admin/app');
const { getAuth } = require('firebase-admin/auth');
const { getDatabase } = require('firebase-admin/database');
const {  onValueUpdated} = require("firebase-functions/v2/database");
const logger = require("firebase-functions/logger");
const { onRequest } = require('firebase-functions/v2/https');
const { Telegraf } = require('telegraf');
const twilio = require('twilio');
const accountSid = '**********************************';
const authToken = 'fbe39da5b0dc369a28022b5d9a01e203';
const client = new twilio(accountSid, authToken);
const whatsappFrom = '+***********';
const whatsappTo = '+************';

initializeApp();
// Securely retrieve the Telegram Bot token from environment configuration
const telegramBotToken = '**********************************************';
if (!telegramBotToken) {
    logger.error("Telegram Bot Token is not configured in environment variables!");
    // Consider throwing an error to prevent function execution
    // throw new Error("Telegram Bot Token is not configured.");
}

// Initialize Telegraf bot (only if token is available)
const bot = telegramBotToken ? new Telegraf(telegramBotToken) : null;
const db = getDatabase();

// --- Helper Functions ---

/**
 * Checks if a department exists in the database.
 * @param {string} deptId - The department ID to check.
 * @returns {Promise<boolean>} - True if department exists, false otherwise.
 */
const checkDepartmentExists = async (deptId) => {
    try {
        const deptRef = db.ref(`/departments/${deptId}`);
        const snapshot = await deptRef.get();
        const exists = snapshot.exists();
        logger.info(`Department ${deptId} exists: ${exists}`);
        return exists;
    } catch (error) {
        logger.error(`Error checking department ${deptId}:`, error);
        return false;
    }
};

/**
 * Adds a user to a department's telegram subscribers.
 * @param {string} chatId - The Telegram chat ID.
 * @param {string} deptId - The department ID.
 * @returns {Promise<boolean>} - True if successful, false otherwise.
 */
const addUserToDepartment = async (chatId, deptId) => {
    try {
        const subscriberRef = db.ref(`/departments/${deptId}/telegram_subscribers/${chatId}`);
        await subscriberRef.set({
            subscribed: true,
            status: "active",
            subscribedAt: new Date().toISOString()
        });
        logger.info(`Chat ID ${chatId} added to department ${deptId} successfully.`);
        return true;
    } catch (error) {
        logger.error(`Error adding chat ID ${chatId} to department ${deptId}:`, error);
        return false;
    }
};

/**
 * Retrieves all chat IDs for a specific department.
 * @param {string} deptId - The department ID.
 * @returns {Promise<string[]>} - An array of chat IDs for the department.
 */
const getDepartmentSubscribers = async (deptId) => {
    try {
        const subscribersRef = db.ref(`/departments/${deptId}/telegram_subscribers`);
        const snapshot = await subscribersRef.get();
        const chatIds = [];

        if (snapshot.exists()) {
            snapshot.forEach(childSnapshot => {
                const subscriberData = childSnapshot.val();
                if (subscriberData.subscribed && subscriberData.status === 'active') {
                    chatIds.push(childSnapshot.key);
                }
            });
        }

        logger.info(`Retrieved ${chatIds.length} active subscribers for department ${deptId}: ${chatIds.join(', ')}`);
        return chatIds;
    } catch (error) {
        logger.error(`Error retrieving subscribers for department ${deptId}:`, error);
        return [];
    }
};

/**
 * Extracts department ID from a database path.
 * @param {string} path - The database path (e.g., "/departments/dept_001/rosters/2024/12").
 * @returns {string|null} - The department ID or null if not found.
 */
const extractDepartmentFromPath = (path) => {
    const match = path.match(/\/departments\/([^\/]+)/);
    return match ? match[1] : null;
};

/**
 * Sends a Telegram message to a single chat ID.  Handles errors.
 * @param {string} chatId - The Telegram chat ID.
 * @param {string} message - The message to send.
 * @returns {Promise<void>}
 */
const sendTelegramMessage = async (chatId, message) => {
    try {
        await bot.telegram.sendMessage(chatId, message);
        logger.info(`Message sent successfully to chat ID ${chatId}.`);
    } catch (error) {
        logger.error(`Error sending message to chat ID ${chatId}:`, error);
        //  IMPORTANT:  Decide how to handle this error.
        //  Should you retry?  Remove the chat ID from the database?
        //  For now, we'll just log and continue.  Acknowledge that the message *failed* for this chat.
    }
};

// --- Cloud Functions ---

/**
 * Cloud Function to handle the Telegram Bot webhook.
 * This function receives updates from Telegram (messages, commands, etc.).
 */
exports.telegramWebhook = onRequest(async (req, res) => {
  if (!bot) {
      logger.warn("Telegram Bot is not initialized.  This function will not work.");
      res.status(500).send("Telegram Bot not configured.");
      return;
  }

  try {
      const update = req.body;
      logger.info("Received Telegram update:", update); // Log the entire update

      // Handle /start command
      if (update.message && update.message.text === '/start') {
          const chatId = String(update.message.chat.id);
          try {
              await bot.telegram.sendMessage(chatId, 'Welcome! Please send your department ID in format: /dept_001');
              res.status(200).send('OK');
              return;
          } catch (error) {
              logger.error('Error sending start message:', error);
              res.status(500).send('Error sending message');
              return;
          }
      }

      // Handle department subscription commands (e.g., /dept_001)
      if (update.message && update.message.text && update.message.text.startsWith('/dept_')) {
          const chatId = String(update.message.chat.id);
          const deptId = update.message.text.substring(1); // Remove '/' to get 'dept_001'

          try {
              // Check if department exists
              const departmentExists = await checkDepartmentExists(deptId);

              if (departmentExists) {
                  // Add user to department subscribers
                  const success = await addUserToDepartment(chatId, deptId);

                  if (success) {
                      await bot.telegram.sendMessage(chatId, `✅ Successfully subscribed to ${deptId} roster updates!\n\nYou will receive notifications when:\n📅 Final rosters are updated\n\nUse /status to check your subscriptions.`);
                  } else {
                      await bot.telegram.sendMessage(chatId, '❌ Failed to subscribe. Please try again.');
                  }
              } else {
                  await bot.telegram.sendMessage(chatId, `❌ Department ${deptId} not found. Please send your department ID in format: /dept_001`);
              }

              res.status(200).send('OK');
              return;
          } catch (error) {
              logger.error('Error handling department subscription:', error);
              await bot.telegram.sendMessage(chatId, '❌ Error processing your request. Please try again.');
              res.status(200).send('OK');
              return;
          }
      }

      // Handle /status command to show user's subscriptions
      if (update.message && update.message.text === '/status') {
          const chatId = String(update.message.chat.id);

          try {
              // Find all departments this user is subscribed to
              const departmentsRef = db.ref('/departments');
              const snapshot = await departmentsRef.get();
              const subscribedDepartments = [];

              if (snapshot.exists()) {
                  snapshot.forEach(deptSnapshot => {
                      const deptId = deptSnapshot.key;
                      const deptData = deptSnapshot.val();

                      if (deptData.telegram_subscribers && deptData.telegram_subscribers[chatId]) {
                          const subscription = deptData.telegram_subscribers[chatId];
                          if (subscription.subscribed && subscription.status === 'active') {
                              subscribedDepartments.push(deptId);
                          }
                      }
                  });
              }

              let statusMessage;
              if (subscribedDepartments.length === 0) {
                  statusMessage = '📋 You are not subscribed to any department roster updates.\n\nTo subscribe, send your department ID in format: /dept_001';
              } else {
                  statusMessage = `📋 Your active subscriptions:\n\n${subscribedDepartments.map(dept => `✅ ${dept}`).join('\n')}\n\nYou will receive notifications for roster updates in these departments.`;
              }

              await bot.telegram.sendMessage(chatId, statusMessage);
              res.status(200).send('OK');
              return;
          } catch (error) {
              logger.error('Error checking user status:', error);
              await bot.telegram.sendMessage(chatId, '❌ Error checking your status. Please try again.');
              res.status(200).send('OK');
              return;
          }
      }

      // Handle /help command
      if (update.message && update.message.text === '/help') {
          const chatId = String(update.message.chat.id);
          const helpMessage = `🤖 Roster Update Bot Commands:

/start - Start using the bot
/dept_001 - Subscribe to dept_001 roster updates
/status - Check your current subscriptions
/help - Show this help message

📝 How to use:
1. Send /start to begin
2. Send your department ID (e.g., /dept_001)
3. You'll receive notifications when final rosters are updated

💡 You can subscribe to multiple departments by sending multiple department commands.`;

          try {
              await bot.telegram.sendMessage(chatId, helpMessage);
              res.status(200).send('OK');
              return;
          } catch (error) {
              logger.error('Error sending help message:', error);
              res.status(200).send('OK');
              return;
          }
      }
      //  IMPORTANT:  If you're only handling /start, and *not* calling bot.handleUpdate,
      //  you MUST send a 200 response here for *every* other kind of message,
      //  or Telegram will keep retrying.
      res.status(200).send('OK');

  } catch (error) {
      logger.error("Error handling Telegram webhook:", error);
      res.status(500).send('Internal Server Error');
  }
});



/**
 * Firebase Realtime Database trigger that sends department-specific Telegram notifications
 * when roster data changes for a specific department.
 */
exports.rosterUpdateNotification = onValueUpdated(
    {
        ref: '/departments/{deptId}/rosters/{year}/{month}',
        region: "us-central1",
    },
    async (event) => {
        if (!bot) {
            logger.warn("Telegram Bot not initialized. Skipping notification.");
            return;
        }

        // Extract department ID from the event path
        const deptId = event.params.deptId;
        const year = event.params.year;
        const month = event.params.month;

        logger.info(`Roster data changed for department ${deptId}, ${year}/${month}:`, event.ref);

        const afterData = event.data.after.val();
        const beforeData = event.data.before.val();

        if (JSON.stringify(afterData) === JSON.stringify(beforeData)) {
            logger.info("No significant data change, skipping notification.");
            return;
        }

        const message = `📅 New roster update for ${deptId} - ${year}/${month}!`;

        try {
            // Get department-specific subscribers
            const chatIds = await getDepartmentSubscribers(deptId);

            if (chatIds.length === 0) {
                logger.info(`No active subscribers for department ${deptId}, skipping Telegram notifications.`);
            } else {
                await Promise.all(chatIds.map(chatId => sendTelegramMessage(chatId, message)));
                logger.info(`Telegram notifications sent successfully to ${chatIds.length} subscribers for department ${deptId}:`, chatIds);
            }

        } catch (error) {
            logger.error(`Error in rosterUpdateNotification for department ${deptId}:`, error);
        }

        // Send WhatsApp notification (department-specific)
        try {
            logger.info(`Attempting to send WhatsApp notification for department ${deptId}...`);
            const contentVariables = JSON.stringify({
                "1": `${deptId}`,
                "2": `${year}/${month}`
            });
            logger.info(`WhatsApp contentVariables: ${contentVariables}`);

            const whatsappMessage = await client.messages
                .create({
                    from: whatsappFrom,
                    contentSid: 'HXb5b62575e6e4ff6129ad7c8efe1f983e',
                    contentVariables: contentVariables,
                    to: whatsappTo,
                });
            logger.info(`WhatsApp notification sent successfully for department ${deptId}:`, whatsappMessage.sid);
        } catch (error) {
            logger.error(`Error sending WhatsApp notification for department ${deptId}:`, error);
        }
    }
);




// You can also trigger on creation or deletion if needed:




/**
 * Firebase Function to add a user (HTTPS POST Trigger).
 */
exports.addUser = onRequest(
  {
    enforceAppCheck: false, // Set to true in production
    region: "us-central1",  // Adjust to your region
  },
  async (req, res) => {
    // Set CORS headers for all responses
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    res.set('Access-Control-Max-Age', '3600');

    logger.info("Received addUser request:", {
      method: req.method,
      url: req.url,
      headers: req.headers,
    });

    try {
      // Handle OPTIONS preflight request
      if (req.method === 'OPTIONS') {
        logger.info("Handling OPTIONS preflight request for addUser");
        res.status(204).send('');
        return;
      }

      // Check for POST request
      if (req.method !== 'POST') {
        logger.error("Invalid method for addUser:", req.method);
        res.status(405).send({ error: 'Method Not Allowed. Use POST.' });
        return;
      }

      // Verify Bearer token
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        logger.error("Missing or invalid Authorization header for addUser");
        res.status(403).send({ error: "Unauthorized: Missing or invalid token." });
        return;
      }

      const idToken = authHeader.split('Bearer ')[1];
      const decodedToken = await getAuth().verifyIdToken(idToken);
      logger.info("Token verified for user:", decodedToken.uid);

      // TODO: Enable admin check in production
      // const userRecord = await getAuth().getUser(decodedToken.uid);
      // if (!userRecord.customClaims?.admin) {
      //   logger.error("Non-admin user attempted to add user:", decodedToken.uid);
      //   res.status(403).send({ error: "Must be an admin to add users." });
      //   return;
      // }

      // Extract email and displayName
      const { email, displayName } = req.body;
      if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        logger.error("Invalid or missing email");
        res.status(400).send({ error: 'Valid email is required.' });
        return;
      }
      if (!displayName) {
        logger.error("Missing displayName");
        res.status(400).send({ error: 'Display name is required.' });
        return;
      }

      logger.info(`Attempting to create user with email: ${email}`);

      // Create user in Firebase Auth
      const userRecord = await getAuth().createUser({
        email,
        displayName,
        emailVerified: false,
        disabled: false,
      });
      logger.info(`Successfully created user with UID: ${userRecord.uid}`);

      // Set user data in Realtime Database
      const database = getDatabase();
      const userRef = database.ref(`users/${userRecord.uid}`);
      await userRef.set({
        email,
        role: 'user',
        uid: userRecord.uid,
        displayName,
      });
      logger.info(`Successfully set user data for UID: ${userRecord.uid} in Realtime Database`);

      res.status(200).send({
        message: `User with email ${email} created successfully.`,
        uid: userRecord.uid,
      });
    } catch (error) {
      logger.error("Error adding user:", {
        message: error.message,
        code: error.code,
        email: req.body.email,
      });
      if (error.code === 'auth/email-already-in-use') {
        res.status(400).send({ error: 'Email is already in use.' });
      } else if (error.code === 'auth/invalid-email') {
        res.status(400).send({ error: 'Invalid email format.' });
      } else {
        res.status(500).send({ error: `Failed to add user: ${error.message}` });
      }
    }
  }
);

/**
 * Firebase Function to delete a user (HTTPS DELETE Trigger).
 */
exports.deleteUser = onRequest(
  {
    enforceAppCheck: false, // Set to true in production
    region: "us-central1",  // Adjust to your region
  },
  async (req, res) => {
    // Set CORS headers for all responses
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'DELETE, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Authorization, Content-Type');
    res.set('Access-Control-Max-Age', '3600');

    logger.info("Received deleteUser request:", {
      method: req.method,
      url: req.url,
      headers: req.headers,
    });

    try {
      // Handle OPTIONS preflight request
      if (req.method === 'OPTIONS') {
        logger.info("Handling OPTIONS preflight request for deleteUser");
        res.status(204).send('');
        return;
      }

      // Check for DELETE request
      if (req.method !== 'DELETE') {
        logger.error("Invalid method for deleteUser:", req.method);
        res.status(405).send({ error: 'Method Not Allowed. Use DELETE.' });
        return;
      }

      // Verify Bearer token
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        logger.error("Missing or invalid Authorization header for deleteUser");
        res.status(403).send({ error: "Unauthorized: Missing or invalid token." });
        return;
      }

      const idToken = authHeader.split('Bearer ')[1];
      const decodedToken = await getAuth().verifyIdToken(idToken);
      logger.info("Token verified for user:", decodedToken.uid);

      // TODO: Enable admin check in production
      // if (!decodedToken.admin) {
      //   logger.error("Non-admin user attempted deletion:", decodedToken.uid);
      //   res.status(403).send({ error: "Must be an admin to delete users." });
      //   return;
      // }

      // Extract uid
      const uid = req.query.uid || (req.body && req.body.uid);
      if (!uid) {
        logger.error("User ID is missing");
        res.status(400).send({ error: 'User ID is required.' });
        return;
      }

      logger.info(`Attempting to delete user with UID: ${uid}`);

      // Delete from Firebase Auth
      await getAuth().deleteUser(uid);
      logger.info(`Successfully deleted user with UID: ${uid} from Firebase Auth`);

      // Delete from Realtime Database
      const database = getDatabase();
      const userRef = database.ref(`users/${uid}`);
      await userRef.remove();
      logger.info(`Successfully deleted user data with UID: ${uid} from Realtime Database`);

      res.status(200).send({ message: `User with UID ${uid} deleted successfully.` });
    } catch (error) {
      logger.error("Error deleting user:", {
        message: error.message,
        code: error.code,
        uid: req.query.uid || req.body?.uid,
      });
      if (error.code === 'auth/user-not-found') {
        res.status(404).send({ error: 'User not found.' });
      } else if (error.code === 'auth/invalid-id-token') {
        res.status(401).send({ error: 'Invalid or expired token.' });
      } else {
        res.status(500).send({ error: `Failed to delete user: ${error.message}` });
      }
    }
  }
);