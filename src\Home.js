//src/Home.js
import React, { useState, useEffect, useMemo, useCallback } from "react";
import {
  getDaysInMonth,
  getDaysOfWeek,
  formatHoursMinutes,
} from "./utils/dateUtils";
import { orderContactInfo } from "./utils/contactUtils";
import RosterHeader from "./components/RosterHeader";
import RosterNavBar from "./components/RosterNavBar";
import RosterControls from "./components/RosterControls";
import RosterContent from "./components/RosterContent";
import AddEmployeeModal from "./components/modals/AddEmployeeModal";
import AddRosterModal from "./components/modals/AddRosterModal";
import DeleteRowModal from "./components/modals/DeleteRowModal";
import PublishRosterModal from "./components/modals/PublishRosterModal";
import useAddEmployee from "./useAddEmployee";
import useAddRoster from "./hooks/useAddRoster";

// Import custom hooks for refactoring
import useEmployeeManagement from "./hooks/useEmployeeManagement";
import useRosterManagement from "./hooks/useRosterManagement";
import usePublishRoster from "./hooks/usePublishRoster";
import useShiftSummary from "./hooks/useShiftSummary";

// Import data provider hooks
import {
  useRosterData,
  useShiftData,
  useContactInfo,
  useCalendarMarkers
} from "./contexts/DataContext.jsx";
import useDataProvider from "./contexts/useDataProvider";

// Import performance monitoring utilities
import performanceMonitor from "./utils/performanceMonitor";

// Module-level storage for cross-month drag data (survives React Strict Mode double rendering)
let crossMonthDragStorage = null;

function Home() {
  // Use data from the DataProvider
  const {
    year,
    month,
    isDraftRoster: isRosterDraft,
    data: rosterData,
    availableYears,
    availableMonths,
    loading: rosterLoading,
    error: rosterError
  } = useRosterData();

  const {
    types: shiftTypes,
    hours: shiftHours,
    colors: shiftColors,
    startTimes: shiftStartTimes,
    endTimes: shiftEndTimes,
    loading: shiftLoading,
    error: shiftError
  } = useShiftData();

  const {
    data: contactInfo,
    loading: contactLoading,
    error: contactError
  } = useContactInfo();

  const {
    weekdayMarkers,
    holidayMarkers,
    specialDayMarkers,
    loading: markersLoading,
    error: markersError
  } = useCalendarMarkers();

  // Debug calendar markers
  useEffect(() => {
    console.log('Calendar Markers:', {
      weekdayMarkers,
      holidayMarkers,
      specialDayMarkers,
      loading: markersLoading,
      error: markersError
    });
  }, [weekdayMarkers, holidayMarkers, specialDayMarkers, markersLoading, markersError]);

  // Use data provider actions
  const {
    changeYear: setYear,
    changeMonth: setMonth,
    toggleDraftMode: toggleRosterView,
    refetchRosterData: refetch
  } = useDataProvider();

  const [localRosterData, setLocalRosterData] = useState([]);

  // Cross-month drag state
  const [crossMonthDragData, setCrossMonthDragData] = useState(null);
  const [isDestinationSelectionMode, setIsDestinationSelectionMode] = useState(false);

  // Legacy hooks that will be migrated in future phases
  const {
    addEmployee,
    loading: addEmployeeLoading,
    error: addEmployeeError,
  } = useAddEmployee(year, month);

  const {
    addRoster,
    loading: addRosterLoading,
    error: addRosterError,
  } = useAddRoster();

    // Use employee management hook
    const {
      availableEmployees,
      isAddEmployeeModalOpen,
      selectedEmployee,
      addEmployeeResult,
      affectedMonths,
      fetchAvailableEmployees,
      handleAddEmployee,
      confirmAddEmployee,
      closeAddEmployeeModal,
    } = useEmployeeManagement(year, month, addEmployee, refetch);

    // Use roster management hook - Note: refetchRosters is now handled by DataProvider
    const {
      isAddRosterModalOpen,
      addRosterResult,
      nextRoster,
      handleAddRoster,
      confirmAddRoster,
      closeAddRosterModal,
      isDeleteModalOpen,
      deleteResult,
      isDeleting,
      affectedDeleteMonths,
      isCurrentOrFutureMonth,
      handleDeleteLastRow,
      confirmDeleteLastRow,
      closeDeleteModal,
    } = useRosterManagement(
      year,
      month,
      localRosterData,
      setLocalRosterData,
      addRoster,
      refetch,
      () => {}, // refetchRosters is now handled by DataProvider
      fetchAvailableEmployees
    );

    // Use publish roster hook
    const {
      isPublishModalOpen,
      publishResult,
      isPublishing,
      handlePublishChanges,
      confirmPublishChanges,
      closePublishModal,
    } = usePublishRoster(year, month, localRosterData);

    // Memoize expensive calculations
    const daysInMonth = useMemo(() => {
      performanceMonitor.startMeasure('getDaysInMonth');
      const result = getDaysInMonth(year, month);
      performanceMonitor.endMeasure('getDaysInMonth');
      return result;
    }, [year, month]);

    const daysOfWeek = useMemo(() => {
      performanceMonitor.startMeasure('getDaysOfWeek');
      const result = getDaysOfWeek(year, month, daysInMonth);
      performanceMonitor.endMeasure('getDaysOfWeek');
      return result;
    }, [year, month, daysInMonth]);

    // Memoize event handlers
    const handleToggleRosterView = useCallback(() => {
      toggleRosterView();
    }, [toggleRosterView]);

    // Cross-month drag handlers
    const handleCrossMonthDragOver = useCallback((e, targetYear, targetMonth) => {
      // Reduced logging to prevent console spam
      // The actual month switching logic will be handled by the RosterTableContainer
    }, []);

    const handleCrossMonthDragLeave = useCallback((e) => {
      // Reduced logging
    }, []);

    const handleCrossMonthDrop = useCallback((e, targetYear, targetMonth) => {
      console.log('Cross-month drop:', { targetYear, targetMonth });

      try {
        // Get the drag data
        const dragDataString = e.dataTransfer.getData('text/plain');
        if (!dragDataString) {
          console.log('No drag data found');
          return;
        }

        const dragData = JSON.parse(dragDataString);
        console.log('Cross-month drop data:', dragData);

        // Switch to target month
        if (targetYear !== year) {
          setYear(targetYear);
        }
        if (targetMonth !== month) {
          setMonth(targetMonth);
        }

        // Extract source cell data using the snapshot from crossMonthDragData or module storage
        const sourceRosterData = crossMonthDragData?.sourceRosterData || crossMonthDragStorage?.sourceRosterData || [];
        console.log('Using sourceRosterData from crossMonthDragData:', crossMonthDragData?.sourceRosterData?.length || 0);
        console.log('Using sourceRosterData from module storage:', crossMonthDragStorage?.sourceRosterData?.length || 0);
        console.log('crossMonthDragData:', crossMonthDragData);
        console.log('crossMonthDragStorage:', crossMonthDragStorage);
        console.log('Final roster data for extraction:', sourceRosterData);
        const sourceCells = extractSourceCellData(dragData, sourceRosterData);

        // Store the drag data with actual source cell values
        const enhancedDropData = {
          ...dragData,
          sourceYear: year,
          sourceMonth: month,
          targetYear: targetYear,
          targetMonth: targetMonth,
          sourceCells: sourceCells
        };
        setCrossMonthDragData(enhancedDropData);

        // Also update module-level storage with target month info
        if (crossMonthDragStorage) {
          crossMonthDragStorage = {
            ...crossMonthDragStorage,
            targetYear: targetYear,
            targetMonth: targetMonth,
            sourceCells: sourceCells
          };
          console.log('Updated module storage with target info:', crossMonthDragStorage);
        }

        setIsDestinationSelectionMode(true);
        console.log('Switched to target month, entering destination selection mode with source cells:', sourceCells);

      } catch (error) {
        console.error('Error processing cross-month drop:', error);
      }
    }, [year, month, setYear, setMonth]);

    // Handle cross-month drag start - capture source roster data
    const handleCrossMonthDragStart = useCallback((dragData) => {
      console.log('Cross-month drag start - capturing source roster data');
      console.log('Current localRosterData:', localRosterData);
      console.log('Drag data:', dragData);

      // Store a snapshot of the current roster data directly in the drag data
      const snapshot = [...localRosterData];
      console.log('Creating snapshot:', snapshot);

      // Enhance the drag data with the roster snapshot and source month info
      const enhancedDragData = {
        ...dragData,
        sourceRosterData: snapshot,
        sourceMonth: month,
        sourceYear: year
      };

      console.log('Enhanced drag data with snapshot:', enhancedDragData);

      // Store in both React state AND module-level storage (survives Strict Mode)
      setCrossMonthDragData(enhancedDragData);
      crossMonthDragStorage = enhancedDragData;
      console.log('Stored in module-level storage:', crossMonthDragStorage);
    }, [localRosterData]);

    // Extract source cell data from drag data and roster data
    const extractSourceCellData = useCallback((dragData, rosterData) => {
      const sourceCells = [];

      console.log('=== EXTRACTING SOURCE CELL DATA ===');
      console.log('Drag data:', dragData);
      console.log('Roster data:', rosterData);

      try {
        if (dragData.selection && dragData.selection.start && dragData.selection.end) {
          // Rectangular selection
          const { start, end } = dragData.selection;

          console.log('Processing rectangular selection:', { start, end });
          console.log('Available employees:', rosterData.map(emp => ({ id: emp.employeeId, name: emp.employeeName })));

          // Find employee indices
          const startEmployeeIndex = rosterData.findIndex(emp => emp.employeeId === start.techId);
          const endEmployeeIndex = rosterData.findIndex(emp => emp.employeeId === end.techId);

          console.log('Employee indices:', { startEmployeeIndex, endEmployeeIndex });

          if (startEmployeeIndex === -1 || endEmployeeIndex === -1) {
            console.error('Could not find employee indices for cross-month drag');
            console.error('Looking for employees:', { startTechId: start.techId, endTechId: end.techId });
            console.error('Available employee IDs:', rosterData.map(emp => emp.employeeId));
            return [];
          }

          // Extract cells from rectangular selection
          const minRow = Math.min(startEmployeeIndex, endEmployeeIndex);
          const maxRow = Math.max(startEmployeeIndex, endEmployeeIndex);
          const minDay = Math.min(start.day, end.day);
          const maxDay = Math.max(start.day, end.day);

          console.log('Selection bounds:', { minRow, maxRow, minDay, maxDay });

          for (let rowIndex = minRow; rowIndex <= maxRow; rowIndex++) {
            for (let day = minDay; day <= maxDay; day++) {
              const employee = rosterData[rowIndex];
              if (employee) {
                // Use numeric key directly (same as paste operation)
                const dayKey = day;
                const shift = employee[dayKey] || '';
                console.log(`Extracting cell: ${employee.employeeId} day ${day} = "${shift}"`);
                console.log(`Source employee keys:`, Object.keys(employee).filter(k => !isNaN(k)).slice(0, 10));
                console.log(`Day key: ${dayKey}, shift value: "${shift}"`);
                sourceCells.push({
                  sourceEmployeeId: employee.employeeId,
                  sourceName: employee.employeeName,
                  day: day,
                  shift: shift,
                  rowOffset: rowIndex - minRow,
                  dayOffset: day - minDay
                });
              }
            }
          }

          console.log('Extracted source cells:', sourceCells);
        } else if (dragData.multiSelection) {
          // Multi-selection
          dragData.multiSelection.forEach(cell => {
            const employee = rosterData.find(emp => emp.employeeId === cell.techId);
            if (employee) {
              sourceCells.push({
                sourceEmployeeId: cell.techId,
                sourceName: employee.employeeName,
                day: cell.day,
                shift: employee[`day${cell.day}`] || '',
                rowOffset: 0,
                dayOffset: 0
              });
            }
          });
        }
      } catch (error) {
        console.error('Error extracting source cell data:', error);
      }

      return sourceCells;
    }, []);

    // Handle destination selection
    const handleDestinationSelection = useCallback((destinationSelection) => {
      console.log('Destination selected:', destinationSelection);

      if (!crossMonthDragData) {
        console.error('No cross-month drag data available');
        return;
      }

      // Process the cross-month paste operation
      try {
        const sourceCells = crossMonthDragData.sourceCells || [];

        if (sourceCells.length === 0) {
          console.error('No source cells available for cross-month paste');
          setIsDestinationSelectionMode(false);
          setCrossMonthDragData(null);
          return;
        }

        console.log('Processing cross-month paste with', sourceCells.length, 'source cells');
        console.log('Source cells data:', sourceCells);

        // Get destination starting position
        const destStartEmployeeId = destinationSelection.start.techId;
        const destStartDay = destinationSelection.start.day;

        console.log('Destination starting position:', { destStartEmployeeId, destStartDay });
        console.log('Current localRosterData:', localRosterData);
        console.log('Current month/year:', { month, year });
        console.log('Source month/year:', { sourceMonth: crossMonthDragStorage?.sourceMonth, sourceYear: crossMonthDragStorage?.sourceYear });

        // Get target month/year from the cross-month drop data (from the drop handler)
        const targetMonth = crossMonthDragData?.targetMonth || crossMonthDragStorage?.targetMonth;
        const targetYear = crossMonthDragData?.targetYear || crossMonthDragStorage?.targetYear;
        console.log('Target month/year:', { targetMonth, targetYear });

        // Safety check: Ensure we're in the correct destination month
        if (targetMonth && targetYear && (month !== targetMonth || year !== targetYear)) {
          console.error('❌ Month/year mismatch! Current:', { month, year }, 'Expected:', { targetMonth, targetYear });
          console.error('❌ This indicates the destination month data has not loaded yet. Aborting paste to prevent data corruption.');
          alert('Error: Destination month data not ready. Please try again.');
          setIsDestinationSelectionMode(false);
          setCrossMonthDragData(null);
          crossMonthDragStorage = null;
          return;
        }

        console.log('✅ Month/year verification passed. Proceeding with paste.');

        // Debug: Let's examine the actual data to understand what's happening
        const sourceDataSnapshot = crossMonthDragStorage?.sourceRosterData || [];
        console.log('🔍 DEBUGGING DATA COMPARISON:');
        console.log('Source data length:', sourceDataSnapshot.length);
        console.log('Destination data length:', localRosterData.length);

        if (sourceDataSnapshot.length > 0 && localRosterData.length > 0) {
          const sourceFirstEmp = sourceDataSnapshot[0];
          const destFirstEmp = localRosterData[0];
          console.log('Source first employee sample days:', {
            employeeId: sourceFirstEmp?.employeeId,
            day1: sourceFirstEmp?.[1],
            day8: sourceFirstEmp?.[8],
            day15: sourceFirstEmp?.[15]
          });
          console.log('Dest first employee sample days:', {
            employeeId: destFirstEmp?.employeeId,
            day1: destFirstEmp?.[1],
            day8: destFirstEmp?.[8],
            day15: destFirstEmp?.[15]
          });
        }

        // Temporarily disable the strict data integrity check to allow paste
        // TODO: Re-enable with better logic once we understand the data patterns
        console.log('⚠️ Data integrity check temporarily disabled for debugging');

        // Find destination employee index
        const destEmployeeIndex = localRosterData.findIndex(emp => emp.employeeId === destStartEmployeeId);
        if (destEmployeeIndex === -1) {
          console.error('Could not find destination employee:', destStartEmployeeId);
          console.error('Available employees:', localRosterData.map(emp => emp.employeeId));
          setIsDestinationSelectionMode(false);
          setCrossMonthDragData(null);
          return;
        }

        console.log('Found destination employee at index:', destEmployeeIndex);

        // Create updated roster data with deep copy to ensure re-render
        const updatedRosterData = localRosterData.map(employee => ({ ...employee }));
        let pastedCells = 0;

        // Paste each source cell to its corresponding destination
        sourceCells.forEach((sourceCell, index) => {
          const destEmployeeIdx = destEmployeeIndex + sourceCell.rowOffset;
          const destDay = destStartDay + sourceCell.dayOffset;

          console.log(`Processing source cell ${index + 1}:`, {
            sourceCell,
            destEmployeeIdx,
            destDay,
            destEmployeeIndex,
            destStartDay
          });

          // Check if destination is valid
          if (destEmployeeIdx >= 0 && destEmployeeIdx < updatedRosterData.length && destDay >= 1 && destDay <= 31) {
            const destEmployee = updatedRosterData[destEmployeeIdx];
            // Employee objects use numeric keys (1, 2, 3, ..., 31) for days, not "day1", "day2", etc.
            const dayKey = destDay; // Use numeric key directly

            console.log(`Destination employee:`, destEmployee);
            console.log(`Day key: ${dayKey}, has property: ${destEmployee?.hasOwnProperty(dayKey)}`);

            if (destEmployee && destEmployee.hasOwnProperty(dayKey)) {
              const oldValue = destEmployee[dayKey];
              destEmployee[dayKey] = sourceCell.shift;
              console.log(`✅ Pasted "${sourceCell.shift}" to ${destEmployee.employeeId} day ${destDay} (was: "${oldValue}")`);
              pastedCells++;
            } else {
              console.log(`❌ Cannot paste to ${destEmployee?.employeeId || 'unknown'} day ${destDay} - invalid destination`);
            }
          } else {
            console.log(`❌ Invalid destination position: employee index ${destEmployeeIdx}, day ${destDay}`);
          }
        });

        // Update the local roster data
        console.log('Updated roster data before setState:', updatedRosterData);
        setLocalRosterData(updatedRosterData);
        console.log('setLocalRosterData called with updated data');

        console.log(`Cross-month paste completed: ${pastedCells} cells pasted`);

        // Show a simple alert for now to confirm the operation
        if (pastedCells > 0) {
          alert(`Cross-month paste completed! ${pastedCells} cells pasted successfully.`);
        } else {
          alert('Cross-month paste completed, but no cells were pasted. Check console for details.');
        }

        // Clear the destination selection mode
        setIsDestinationSelectionMode(false);
        setCrossMonthDragData(null);
        crossMonthDragStorage = null; // Clear module storage

      } catch (error) {
        console.error('Error processing destination selection:', error);
        setIsDestinationSelectionMode(false);
        setCrossMonthDragData(null);
      }
    }, [crossMonthDragData]);

    // Cancel destination selection
    const handleCancelDestinationSelection = useCallback(() => {
      console.log('Cancelling destination selection');
      setIsDestinationSelectionMode(false);
      setCrossMonthDragData(null);
      crossMonthDragStorage = null; // Clear module storage
    }, []);

    // Log data changes for debugging (only in development)
    useEffect(() => {
      if (process.env.NODE_ENV !== 'production') {
        console.log("Available months:", availableMonths);
      }
    }, [availableMonths]);

    useEffect(() => {
      if (process.env.NODE_ENV !== 'production') {
        console.log("Available years:", availableYears);
      }
    }, [availableYears]);

    // Update local roster data when roster data changes
    useEffect(() => {
      performanceMonitor.startMeasure('updateLocalRosterData');

      if (rosterData && rosterData.length > 0) {
        if (process.env.NODE_ENV !== 'production') {
          console.log("Home.js setting localRosterData:", rosterData);
        }
        setLocalRosterData(rosterData);
      } else {
        setLocalRosterData([]);
      }

      performanceMonitor.endMeasure('updateLocalRosterData');
    }, [rosterData]);

    // Memoize loading and error states
    const isLoading = useMemo(() =>
      rosterLoading || shiftLoading || contactLoading || markersLoading,
      [rosterLoading, shiftLoading, contactLoading, markersLoading]
    );

    const error = useMemo(() =>
      rosterError || shiftError || contactError || markersError,
      [rosterError, shiftError, contactError, markersError]
    );
    // Get shift summary data (hooks must be called at the top level)
    const { shiftSummary, shiftTotals } = useShiftSummary(localRosterData, daysInMonth);

    // Performance monitoring for shift summary calculation
    useEffect(() => {
      performanceMonitor.startMeasure('processShiftSummary');
      performanceMonitor.endMeasure('processShiftSummary');
    }, [shiftSummary, shiftTotals]);

    // Memoize contact info ordering
    const orderedContactInfo = useMemo(() => {
      performanceMonitor.startMeasure('orderContactInfo');
      const result = orderContactInfo(contactInfo);
      performanceMonitor.endMeasure('orderContactInfo');
      return result;
    }, [contactInfo]);

    return (
      <div style={{ display: "flex", minHeight: "100vh", position: "relative", margin: "0 auto" }}>
        {!isLoading && (
          <RosterNavBar
            availableYears={availableYears}
            availableMonths={availableMonths[year] || []}
            year={year}
            setYear={setYear}
            selectedMonth={month}
            onMonthSelect={setMonth}
            setMonth={setMonth}
            onCrossMonthDragOver={handleCrossMonthDragOver}
            onCrossMonthDragLeave={handleCrossMonthDragLeave}
            onCrossMonthDrop={handleCrossMonthDrop}
          />
        )}
        <div
          style={{
            paddingLeft: isLoading ? "0" : "120px",
            paddingTop: "100px",
            flex: 1,
            minHeight: "100vh",
            backgroundColor: "#f9fafb",
            paddingBottom: "1rem",
            boxSizing: "border-box",
          }}
        >
          {isLoading ? (
            <div
              style={{
                minHeight: "calc(100vh - 60px)",
                display: "flex",
                width: "100%",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <div style={{ textAlign: "center", fontSize: "1.25rem" }}>
                Loading roster, shift, and contact data...
              </div>
              <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-[1000]">
                <div className="animate-spin rounded-full h-20 w-20 border-t-4 border-blue-600"></div>
              </div>
            </div>
          ) : error ? (
            <div
              style={{
                minHeight: "calc(100vh - 60px)",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <div
                style={{
                  backgroundColor: "#fee2e2",
                  border: "1px solid #f87171",
                  color: "#b91c1c",
                  padding: "0.75rem 1rem",
                  borderRadius: "0.375rem",
                  maxWidth: "28rem",
                  textAlign: "center",
                }}
              >
                <strong style={{ fontWeight: "bold" }}>Error!</strong>
                <span> {error.message}</span>
                <p style={{ marginTop: "0.5rem", fontSize: "0.875rem" }}>
                  Please try again later.
                </p>
              </div>
            </div>
          ) : (
            <div style={{ minHeight: "calc(100vh - 200px)" }}>
              {localRosterData.length === 0 ? (
                <div
                  style={{
                    minHeight: "calc(100vh - 60px)",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <div style={{ textAlign: "center", fontSize: "1.25rem" }}>
                    No roster data available for{" "}
                    {new Date(year, month - 1).toLocaleString("default", { month: "long" })}{" "}
                    {year}.
                  </div>
                </div>
              ) : (
                <>
                  <RosterHeader year={year} month={month} />
                  <div className="mb-6">
                  <div className="mb-6 ml-[100px]">
  <span
    className={`text-lg font-semibold ${
      isRosterDraft ? 'text-gray-700' : 'bg-gradient-to-r from-yellow-400 to-orange-500 text-transparent bg-clip-text'
    }`}
  >
    {isRosterDraft ? 'Draft Roster' : '⭐ Live Roster ⭐'}
  </span>
</div>
</div>

                  <RosterControls
                    availableEmployees={availableEmployees}
                    handleAddEmployee={handleAddEmployee}
                    handleAddRoster={handleAddRoster}
                    handleDeleteLastRow={handleDeleteLastRow}
                    handlePublishChanges={handlePublishChanges}
                    handleToggleRosterView={handleToggleRosterView}
                    addEmployeeLoading={addEmployeeLoading}
                    addEmployeeError={addEmployeeError}
                    addRosterLoading={addRosterLoading}
                    isCurrentOrFutureMonth={isCurrentOrFutureMonth}
                    localRosterData={localRosterData}
                    isDeleting={isDeleting}
                    isPublishing={isPublishing}
                    isRosterDraft={isRosterDraft}
                  />
                  <RosterContent
                    localRosterData={localRosterData}
                    setLocalRosterData={setLocalRosterData}
                    shiftTypes={shiftTypes}
                    shiftHours={shiftHours}
                    shiftColors={shiftColors}
                    daysOfWeek={daysOfWeek}
                    formatHoursMinutes={formatHoursMinutes}
                    year={year}
                    month={month}
                    weekdayMarkers={weekdayMarkers}
                    holidayMarkers={holidayMarkers}
                    specialDayMarkers={specialDayMarkers}
                    shiftSummary={shiftSummary}
                    shiftTotals={shiftTotals}
                    daysInMonth={daysInMonth}
                    shiftStartTimes={shiftStartTimes}
                    shiftEndTimes={shiftEndTimes}
                    orderedContactInfo={orderedContactInfo}
                    isRosterDraft={isRosterDraft}
                    changeYear={setYear}
                    changeMonth={setMonth}
                    crossMonthDragData={crossMonthDragData}
                    isDestinationSelectionMode={isDestinationSelectionMode}
                    onDestinationSelection={handleDestinationSelection}
                    onCancelDestinationSelection={handleCancelDestinationSelection}
                    onCrossMonthDragStart={handleCrossMonthDragStart}
                  />
                </>
              )}
            </div>
          )}
        </div>

        {/* Add Employee Modal */}
        <AddEmployeeModal
          isOpen={isAddEmployeeModalOpen}
          selectedEmployee={selectedEmployee}
          addEmployeeResult={addEmployeeResult}
          affectedMonths={affectedMonths}
          addEmployeeLoading={addEmployeeLoading}
          closeModal={closeAddEmployeeModal}
          confirmAddEmployee={confirmAddEmployee}
          year={year}
          month={month}
        />

        {/* Add Roster Modal */}
        <AddRosterModal
          isOpen={isAddRosterModalOpen}
          nextRoster={nextRoster}
          addRosterResult={addRosterResult}
          addRosterLoading={addRosterLoading}
          closeModal={closeAddRosterModal}
          confirmAddRoster={confirmAddRoster}
        />

        {/* Delete Last Row Modal */}
        <DeleteRowModal
          isOpen={isDeleteModalOpen}
          localRosterData={localRosterData}
          deleteResult={deleteResult}
          affectedDeleteMonths={affectedDeleteMonths}
          isDeleting={isDeleting}
          closeModal={closeDeleteModal}
          confirmDeleteLastRow={confirmDeleteLastRow}
          year={year}
          month={month}
        />

        {/* Publish Changes Modal */}
        <PublishRosterModal
          isOpen={isPublishModalOpen}
          publishResult={publishResult}
          isPublishing={isPublishing}
          closeModal={closePublishModal}
          confirmPublishChanges={confirmPublishChanges}
          year={year}
          month={month}
        />
      </div>
    );
  }

export default Home;
