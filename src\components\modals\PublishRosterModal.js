// src/components/modals/PublishRosterModal.js
import React from 'react';

/**
 * PublishRosterModal component - Modal for publishing roster changes
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {Object} props.publishResult - Result of publishing changes
 * @param {boolean} props.isPublishing - Whether publishing changes is in progress
 * @param {Function} props.closeModal - Function to close the modal
 * @param {Function} props.confirmPublishChanges - Function to confirm publishing changes
 * @param {number} props.year - Current year
 * @param {number} props.month - Current month
 * @returns {JSX.Element|null} PublishRosterModal component or null if not open
 */
const PublishRosterModal = ({
  isOpen,
  publishResult,
  isPublishing,
  closeModal,
  confirmPublishChanges,
  year,
  month,
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h2 className="text-xl font-bold mb-4">Publish Roster</h2>
        {!publishResult ? (
          <>
            <p className="text-sm text-gray-700 mb-4">
              You are about to publish the roster for{" "}
              <strong>
                {new Date(year, month - 1).toLocaleString('default', { month: 'long' })} {year}
              </strong>
              . This will move the draft roster to the live roster.
            </p>
            <p className="text-sm text-gray-700 mb-4">
              This action cannot be undone. Do you want to proceed?
            </p>
            {isPublishing && (
              <div className="flex justify-center mb-4">
                <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-600"></div>
              </div>
            )}
            <div className="flex justify-end space-x-2">
              <button
                onClick={closeModal}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                disabled={isPublishing}
              >
                Cancel
              </button>
              <button
                onClick={confirmPublishChanges}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
                disabled={isPublishing}
              >
                Publish
              </button>
            </div>
          </>
        ) : publishResult.success ? (
          <>
            <p className="text-sm text-green-600 mb-4">
              Roster for{" "}
              <strong>
                {new Date(year, month - 1).toLocaleString('default', { month: 'long' })} {year}
              </strong>{" "}
              was successfully published.
            </p>
            <div className="flex justify-end">
              <button
                onClick={closeModal}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Close
              </button>
            </div>
          </>
        ) : (
          <>
            <p className="text-sm text-red-600 mb-4">
              Failed to publish roster: {publishResult.error}
            </p>
            <div className="flex justify-end space-x-2">
              <button
                onClick={closeModal}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
              >
                Cancel
              </button>
              <button
                onClick={confirmPublishChanges}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Retry
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default PublishRosterModal;
