// src/contexts/DataContext.test.js
import React from 'react';
import { render, screen, act, waitFor } from '@testing-library/react';
import { DataProvider, useRosterData, useEmployeeData, useDataDispatch } from './DataContext';
import * as firebaseDatabase from 'firebase/database';

// Mock Firebase
jest.mock('firebase/database', () => ({
  getDatabase: jest.fn(),
  ref: jest.fn(),
  onValue: jest.fn(),
  get: jest.fn(),
  set: jest.fn(),
  remove: jest.fn(),
  update: jest.fn(),
  off: jest.fn()
}));

// Test component that uses the DataProvider
const TestComponent = () => {
  const rosterData = useRosterData();
  const employeeData = useEmployeeData();
  const dispatch = useDataDispatch();
  
  return (
    <div>
      <div data-testid="year">{rosterData.year}</div>
      <div data-testid="month">{rosterData.month}</div>
      <div data-testid="is-draft">{rosterData.isDraftRoster ? 'true' : 'false'}</div>
      <div data-testid="roster-loading">{rosterData.loading ? 'true' : 'false'}</div>
      <div data-testid="employee-loading">{employeeData.loading ? 'true' : 'false'}</div>
      <button 
        data-testid="toggle-draft"
        onClick={() => dispatch({ type: 'TOGGLE_ROSTER_DRAFT' })}
      >
        Toggle Draft
      </button>
      <button 
        data-testid="set-year"
        onClick={() => dispatch({ type: 'SET_ROSTER_YEAR', payload: 2025 })}
      >
        Set Year
      </button>
      <button 
        data-testid="set-month"
        onClick={() => dispatch({ type: 'SET_ROSTER_MONTH', payload: 6 })}
      >
        Set Month
      </button>
    </div>
  );
};

describe('DataProvider', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Mock Firebase onValue to simulate data loading
    firebaseDatabase.onValue.mockImplementation((ref, callback) => {
      // Simulate async data loading
      setTimeout(() => {
        callback({
          exists: () => true,
          val: () => ({ 2023: { 1: {}, 2: {} }, 2024: { 1: {} } })
        });
      }, 0);
      
      // Return a mock unsubscribe function
      return jest.fn();
    });
    
    // Mock Firebase get to simulate data loading
    firebaseDatabase.get.mockImplementation(() => {
      return Promise.resolve({
        exists: () => true,
        val: () => ({ employee1: { name: 'John Doe' } })
      });
    });
  });
  
  test('provides initial state values', async () => {
    render(
      <DataProvider>
        <TestComponent />
      </DataProvider>
    );
    
    // Check initial values
    expect(screen.getByTestId('year').textContent).toBe(new Date().getFullYear().toString());
    expect(screen.getByTestId('month').textContent).toBe((new Date().getMonth() + 1).toString());
    expect(screen.getByTestId('is-draft').textContent).toBe('true');
    expect(screen.getByTestId('roster-loading').textContent).toBe('false');
    expect(screen.getByTestId('employee-loading').textContent).toBe('false');
  });
  
  test('updates state when dispatching actions', async () => {
    render(
      <DataProvider>
        <TestComponent />
      </DataProvider>
    );
    
    // Toggle draft mode
    act(() => {
      screen.getByTestId('toggle-draft').click();
    });
    
    // Check that draft mode was toggled
    expect(screen.getByTestId('is-draft').textContent).toBe('false');
    
    // Set year
    act(() => {
      screen.getByTestId('set-year').click();
    });
    
    // Check that year was updated
    expect(screen.getByTestId('year').textContent).toBe('2024');
    
    // Set month
    act(() => {
      screen.getByTestId('set-month').click();
    });
    
    // Check that month was updated
    expect(screen.getByTestId('month').textContent).toBe('6');
  });
  
  test('fetches data from Firebase', async () => {
    render(
      <DataProvider>
        <TestComponent />
      </DataProvider>
    );
    
    // Wait for Firebase calls
    await waitFor(() => {
      expect(firebaseDatabase.getDatabase).toHaveBeenCalled();
      expect(firebaseDatabase.ref).toHaveBeenCalled();
      expect(firebaseDatabase.onValue).toHaveBeenCalled();
    });
  });
});
