// src/hooks/RosterTable/useCrossMonthDrag.js
import { useState, useCallback, useRef } from 'react';

/**
 * Custom hook to manage cross-month drag and drop functionality
 * Provides Excel-like sheet-to-sheet drag and drop experience
 *
 * @param {number} currentYear - Current viewing year
 * @param {number} currentMonth - Current viewing month
 * @param {Function} changeMonth - Function to change month
 * @param {Function} changeYear - Function to change year
 * @param {Array} rosterData - Current roster data
 * @returns {Object} Cross-month drag handlers and state
 */
const useCrossMonthDrag = (currentYear, currentMonth, changeMonth, changeYear, rosterData) => {
  // State for cross-month drag operation
  const [isDragActive, setIsDragActive] = useState(false);
  const [dragData, setDragData] = useState(null);
  const [targetMonth, setTargetMonth] = useState(null);
  const [targetYear, setTargetYear] = useState(null);
  const [isMonthSwitching, setIsMonthSwitching] = useState(false);

  // Refs for drag operation
  const dragTimeoutRef = useRef(null);
  const originalMonthRef = useRef({ year: currentYear, month: currentMonth });

  // Handle drag start for cross-month operations
  const handleCrossMonthDragStart = useCallback((dragInfo) => {
    console.log('Cross-month drag started:', dragInfo);
    setIsDragActive(true);
    setDragData(dragInfo);
    originalMonthRef.current = { year: currentYear, month: currentMonth };
  }, [currentYear, currentMonth]);

  // Handle drag over month navigation elements
  const handleMonthNavigationDragOver = useCallback((e, targetYear, targetMonth) => {
    if (!isDragActive || !dragData) return;
    
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
    
    // Clear any existing timeout
    if (dragTimeoutRef.current) {
      clearTimeout(dragTimeoutRef.current);
    }

    // Set a timeout to switch months if hovering for more than 800ms
    dragTimeoutRef.current = setTimeout(() => {
      if (targetYear !== currentYear || targetMonth !== currentMonth) {
        console.log(`Switching to month ${targetMonth}/${targetYear} for cross-month drag`);
        setIsMonthSwitching(true);
        setTargetMonth(targetMonth);
        setTargetYear(targetYear);
        
        // Switch to target month
        if (targetYear !== currentYear) {
          changeYear(targetYear);
        }
        changeMonth(targetMonth);
        
        // Reset switching state after a brief delay
        setTimeout(() => setIsMonthSwitching(false), 500);
      }
    }, 800);
  }, [isDragActive, dragData, currentYear, currentMonth, changeMonth, changeYear]);

  // Handle drag leave from month navigation
  const handleMonthNavigationDragLeave = useCallback(() => {
    if (dragTimeoutRef.current) {
      clearTimeout(dragTimeoutRef.current);
      dragTimeoutRef.current = null;
    }
  }, []);

  // Handle drop in target month
  const handleCrossMonthDrop = useCallback((e, targetTechId, targetDay) => {
    if (!isDragActive || !dragData) return null;

    console.log('Cross-month drop:', { targetTechId, targetDay, dragData });
    
    try {
      // Parse the drag data if it's a string
      const parsedDragData = typeof dragData === 'string' ? JSON.parse(dragData) : dragData;
      
      // Extract source cells from the drag data
      let sourceCells = [];
      
      if (parsedDragData.selection && parsedDragData.selection.start && parsedDragData.selection.end) {
        // Rectangular selection
        const { start, end } = parsedDragData.selection;
        
        // Find employee indices
        const startEmployeeIndex = rosterData.findIndex(emp => emp.employeeId === start.techId);
        const endEmployeeIndex = rosterData.findIndex(emp => emp.employeeId === end.techId);
        
        if (startEmployeeIndex === -1 || endEmployeeIndex === -1) {
          console.error('Could not find employee indices for cross-month drag');
          return null;
        }

        // Extract cells from rectangular selection
        const minRow = Math.min(startEmployeeIndex, endEmployeeIndex);
        const maxRow = Math.max(startEmployeeIndex, endEmployeeIndex);
        const minDay = Math.min(start.day, end.day);
        const maxDay = Math.max(start.day, end.day);

        for (let rowIndex = minRow; rowIndex <= maxRow; rowIndex++) {
          for (let day = minDay; day <= maxDay; day++) {
            const employee = rosterData[rowIndex];
            if (employee) {
              const shift = employee[`day${day}`] || '';
              sourceCells.push({
                sourceEmployeeId: employee.employeeId,
                sourceName: employee.employeeName,
                day: day,
                shift: shift,
                rowOffset: rowIndex - minRow,
                dayOffset: day - minDay
              });
            }
          }
        }
      } else if (parsedDragData.multiSelection) {
        // Multi-selection
        parsedDragData.multiSelection.forEach(cell => {
          const employee = rosterData.find(emp => emp.employeeId === cell.techId);
          if (employee) {
            sourceCells.push({
              sourceEmployeeId: cell.techId,
              sourceName: employee.employeeName,
              day: cell.day,
              shift: employee[`day${cell.day}`] || '',
              rowOffset: 0,
              dayOffset: 0
            });
          }
        });
      }

      console.log('Extracted source cells for cross-month operation:', sourceCells);
      
      // Return the cross-month operation data
      return {
        type: 'crossMonth',
        sourceCells,
        targetTechId,
        targetDay,
        sourceMonth: originalMonthRef.current.month,
        sourceYear: originalMonthRef.current.year,
        targetMonth: currentMonth,
        targetYear: currentYear,
        dragMode: parsedDragData.type || 'rectangular'
      };

    } catch (error) {
      console.error('Error processing cross-month drop:', error);
      return null;
    }
  }, [isDragActive, dragData, rosterData, currentMonth, currentYear]);

  // Cancel cross-month drag operation
  const cancelCrossMonthDrag = useCallback(() => {
    console.log('Cancelling cross-month drag operation');
    setIsDragActive(false);
    setDragData(null);
    setTargetMonth(null);
    setTargetYear(null);
    setIsMonthSwitching(false);
    
    if (dragTimeoutRef.current) {
      clearTimeout(dragTimeoutRef.current);
      dragTimeoutRef.current = null;
    }
  }, []);

  // Reset to original month
  const resetToOriginalMonth = useCallback(() => {
    const { year, month } = originalMonthRef.current;
    if (year !== currentYear) {
      changeYear(year);
    }
    if (month !== currentMonth) {
      changeMonth(month);
    }
    cancelCrossMonthDrag();
  }, [currentYear, currentMonth, changeYear, changeMonth, cancelCrossMonthDrag]);

  return {
    // State
    isDragActive,
    dragData,
    targetMonth,
    targetYear,
    isMonthSwitching,
    
    // Handlers
    handleCrossMonthDragStart,
    handleMonthNavigationDragOver,
    handleMonthNavigationDragLeave,
    handleCrossMonthDrop,
    cancelCrossMonthDrag,
    resetToOriginalMonth
  };
};

export default useCrossMonthDrag;
