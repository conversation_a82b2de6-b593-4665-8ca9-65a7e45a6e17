// src\contexts\actions\employeeActions.js
import { getDatabase, ref, set, get, update, remove } from 'firebase/database';
import getDepartmentPath from '../../utils/getDepartmentPath';
import dataCache from '../../utils/cacheUtils';
import {
  FETCH_EMPLOYEES_START,
  FETCH_EMPLOYEES_SUCCESS,
  FETCH_EMPLOYEES_ERROR,
  ADD_EMPLOYEE,
  UPDATE_EMPLOYEE,
  DELETE_EMPLOYEE
} from '../DataContext';

// ===== EMPLOYEE ACTIONS =====

// Fetch employees data
export const fetchEmployeesData = async (dispatch, forceRefresh = false, selectedDepartment = null) => {
  dispatch({ type: FETCH_EMPLOYEES_START });

  const cacheKey = `employees_data_${selectedDepartment || 'default'}`;

  // Check cache first unless forced refresh
  if (!forceRefresh) {
    const cachedData = dataCache.get(cacheKey);
    if (cachedData) {
      dispatch({ type: <PERSON>ET<PERSON>_EMPLOYEES_SUCCESS, payload: cachedData });
      return;
    }
  }

  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);

    const db = getDatabase();
    const employeesRef = ref(db, `${departmentPath}employees`);

    get(employeesRef)
      .then((snapshot) => {
        if (snapshot.exists()) {
          let employeeData = Object.values(snapshot.val());
          // Sort by rowIndex to maintain correct order
          employeeData = employeeData.sort((a, b) => a.rowIndex - b.rowIndex);

          // Cache the data for future use (30 minutes TTL)
          dataCache.set(cacheKey, employeeData, 30 * 60 * 1000);

          dispatch({ type: FETCH_EMPLOYEES_SUCCESS, payload: employeeData });
        } else {
          dispatch({ type: FETCH_EMPLOYEES_SUCCESS, payload: [] });
        }
      })
      .catch((err) => {
        dispatch({
          type: FETCH_EMPLOYEES_ERROR,
          payload: new Error('Failed to fetch employees data: ' + err.message)
        });
      });
  } catch (error) {
    dispatch({
      type: FETCH_EMPLOYEES_ERROR,
      payload: new Error('Failed to fetch employees data: ' + error.message)
    });
  }
};

// Add employee with optimistic update
export const addEmployee = async (dispatch, employeeData, selectedDepartment = null) => {
  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);
    const cacheKey = `employees_data_${selectedDepartment || 'default'}`;

    // Optimistically update the UI immediately
    dispatch({ type: ADD_EMPLOYEE, payload: employeeData });

    // Invalidate the cache
    dataCache.delete(cacheKey);

    // Perform the actual database update
    const db = getDatabase();
    await set(ref(db, `${departmentPath}employees/${employeeData.employeeId}`), employeeData);

    return true;
  } catch (err) {
    console.error('Error adding employee:', err);

    // Fetch fresh data to revert the optimistic update
    fetchEmployeesData(dispatch, true, selectedDepartment);

    return false;
  }
};

// Update employee with optimistic update
export const updateEmployee = async (dispatch, employeeId, employeeData, selectedDepartment = null) => {
  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);
    const cacheKey = `employees_data_${selectedDepartment || 'default'}`;
    const db = getDatabase();

    // 1. Get current employee data
    const employeeRef = ref(db, `${departmentPath}employees/${employeeId}`);
    const employeeSnapshot = await get(employeeRef);

    if (!employeeSnapshot.exists()) {
      console.warn(`Employee with ID ${employeeId} not found.`);
      return false;
    }

    const currentEmployee = employeeSnapshot.val();
    const oldRowIndex = currentEmployee.rowIndex;

    // Create a copy of updated data for processing
    const updatedData = { ...employeeData };

    // 2. Handle rowIndex change
    if (updatedData.rowIndex !== undefined && updatedData.rowIndex !== oldRowIndex) {
      // Check if the employee has entries in future rosters before adjusting row indices
      const hasRosterEntries = await checkFutureRosterEntries(employeeId, departmentPath);
      if (hasRosterEntries) {
        await adjustRowIndices(employeeId, updatedData.rowIndex, dispatch, selectedDepartment);

        // 3. Update the employee data. Use the employeeRef we already have
        await update(employeeRef, updatedData);

        // 4. Update the rosters
        await updateEmployeeAndRoster(employeeId, updatedData, departmentPath);

        // 5. Invalidate the cache and fetch fresh data to ensure UI is in sync
        dataCache.delete(cacheKey);
        await fetchEmployeesData(dispatch, true, selectedDepartment);

        return true;
      } else {
        console.warn(`Employee ${employeeId} has no future roster entries. Keeping original rowIndex: ${oldRowIndex}.`);
        // Keep the original rowIndex instead of the new one
        updatedData.rowIndex = oldRowIndex;
      }
    }

    // 3. Update the employee data. Use the employeeRef we already have
    await update(employeeRef, updatedData);

    // 4. Update the rosters
    await updateEmployeeAndRoster(employeeId, updatedData, departmentPath);

    // 5. Update local state (optimistic update after successful database update)
    dispatch({ type: UPDATE_EMPLOYEE, payload: { employeeId, ...updatedData } });

    // Invalidate the cache
    dataCache.delete(cacheKey);

    return true;
  } catch (err) {
    console.error('Error updating employee:', err);

    // Fetch fresh data to revert any changes
    fetchEmployeesData(dispatch, true, selectedDepartment);

    return false;
  }
};

// Delete employee with optimistic update
export const deleteEmployee = async (dispatch, employeeId, selectedDepartment = null) => {
  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);
    const cacheKey = `employees_data_${selectedDepartment || 'default'}`;
    const db = getDatabase();

    // Get current employee data before deletion
    const employeeRef = ref(db, `${departmentPath}employees/${employeeId}`);
    const employeeSnapshot = await get(employeeRef);

    if (!employeeSnapshot.exists()) {
      console.warn(`Employee with ID ${employeeId} not found.`);
      return false;
    }

    const deletedEmployee = employeeSnapshot.val();
    const deletedRowIndex = deletedEmployee.rowIndex;

    // Get all employees to determine which ones need row index adjustment
    const employeesSnapshot = await get(ref(db, `${departmentPath}employees`));
    if (!employeesSnapshot.exists()) {
      console.warn('No employees found.');
      return false;
    }

    const allEmployees = Object.values(employeesSnapshot.val());
    const remainingEmployees = allEmployees.filter(emp => emp.employeeId !== employeeId);

    // Optimistically update the UI immediately
    dispatch({ type: DELETE_EMPLOYEE, payload: employeeId });

    // Invalidate the cache
    dataCache.delete(cacheKey);

    // Remove the employee from database
    await remove(employeeRef);

    // Remove employee from all future rosters
    await removeEmployeeFromRosters(employeeId, departmentPath);

    // Adjust row indices for employees below the deleted one
    const updates = {};
    const affectedEmployees = remainingEmployees
      .filter(emp => emp.rowIndex > deletedRowIndex)
      .map(emp => ({
        ...emp,
        newRowIndex: emp.rowIndex - 1
      }));

    // Prepare updates for affected employees
    affectedEmployees.forEach(emp => {
      updates[`${departmentPath}employees/${emp.employeeId}/rowIndex`] = emp.newRowIndex;
    });

    // Apply row index updates if there are any
    if (Object.keys(updates).length > 0) {
      await update(ref(db), updates);
    }

    // Update rosters for affected employees
    const futurePaths = await getFutureRosterPaths(db, departmentPath);
    const rosterUpdates = {};

    for (const emp of affectedEmployees) {
      for (const path of futurePaths) {
        const rosterRef = `${departmentPath}rostersDraft/${path}/employeeShifts/${emp.employeeId}`;
        const snapshot = await get(ref(db, rosterRef));
        if (snapshot.exists()) {
          rosterUpdates[rosterRef] = {
            ...snapshot.val(),
            rowIndex: emp.newRowIndex
          };
        }
      }
    }

    // Apply roster updates if there are any
    if (Object.keys(rosterUpdates).length > 0) {
      await update(ref(db), rosterUpdates);
    }

    // If there were affected employees (row index adjustments), fetch fresh data to ensure UI is in sync
    if (affectedEmployees.length > 0) {
      console.log(`Deleted employee affected ${affectedEmployees.length} other employees. Fetching fresh data.`);
      await fetchEmployeesData(dispatch, true, selectedDepartment);
    } else {
      // No row adjustments needed, just update local state normally
      // (The DELETE_EMPLOYEE dispatch was already done at the beginning)
    }

    return true;
  } catch (err) {
    console.error('Error deleting employee:', err);

    // Fetch fresh data to revert the optimistic update
    fetchEmployeesData(dispatch, true, selectedDepartment);

    return false;
  }
};
// ... existing imports ...

// === EMPLOYEE LOGIC FROM useEmployeeData.js ===

/**
 * Adjusts row indices of affected employees when one moves.
 */
export const adjustRowIndices = async (employeeId, newRowIndex, dispatch, selectedDepartment = null) => {
  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);
    const db = getDatabase();
    const employeesSnapshot = await get(ref(db, `${departmentPath}employees`));
    if (!employeesSnapshot.exists()) return;

    const allEmployees = Object.values(employeesSnapshot.val());
    const currentEmployee = allEmployees.find(emp => emp.employeeId === employeeId);
    if (!currentEmployee) return;

    const oldRowIndex = currentEmployee.rowIndex;
    if (oldRowIndex === newRowIndex) return;

    const sortedEmployees = [...allEmployees].sort((a, b) => a.rowIndex - b.rowIndex);
    const affectedEmployees = [];
    const updates = {};

    if (newRowIndex < oldRowIndex) {
      // Moving up
      for (let i = newRowIndex; i < oldRowIndex; i++) {
        const emp = sortedEmployees.find(e => e.rowIndex === i);
        if (emp && emp.employeeId !== employeeId) {
          affectedEmployees.push({ ...emp, newRowIndex: i + 1 });
        }
      }
    } else {
      // Moving down
      for (let i = oldRowIndex + 1; i <= newRowIndex; i++) {
        const emp = sortedEmployees.find(e => e.rowIndex === i);
        if (emp && emp.employeeId !== employeeId) {
          affectedEmployees.push({ ...emp, newRowIndex: i - 1 });
        }
      }
    }

    affectedEmployees.forEach(emp => {
      updates[`${departmentPath}employees/${emp.employeeId}/rowIndex`] = emp.newRowIndex;
    });

    updates[`${departmentPath}employees/${employeeId}/rowIndex`] = newRowIndex;

    await update(ref(db), updates);

    // Now update future rosters
    const rosterPaths = await getFutureRosterPaths(db, departmentPath);
    const rosterUpdates = {};
    affectedEmployees.push({ employeeId, newRowIndex });

    for (const emp of affectedEmployees) {
      for (const path of rosterPaths) {
        const rosterRef = `${departmentPath}rostersDraft/${path}/employeeShifts/${emp.employeeId}`;
        const snapshot = await get(ref(db, rosterRef));
        if (snapshot.exists()) {
          rosterUpdates[rosterRef] = {
            ...snapshot.val(),
            rowIndex: emp.newRowIndex
          };
        }
      }
    }

    if (Object.keys(rosterUpdates).length > 0) {
      await update(ref(db), rosterUpdates);
    }

    // Dispatch local state update
    dispatch({ type: UPDATE_EMPLOYEE, payload: { employeeId, rowIndex: newRowIndex } });
    affectedEmployees.forEach(emp =>
      dispatch({ type: UPDATE_EMPLOYEE, payload: { employeeId: emp.employeeId, rowIndex: emp.newRowIndex } })
    );
  } catch (err) {
    console.error("Error adjusting row indices:", err);
  }
};

async function getFutureRosterPaths(db, departmentPath) {
  const snapshot = await get(ref(db, `${departmentPath}rostersDraft`));
  if (!snapshot.exists()) return [];

  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth() + 1;
  const paths = [];

  Object.entries(snapshot.val()).forEach(([year, months]) => {
    Object.keys(months).forEach(month => {
      const [rosterYear, rosterMonth] = [parseInt(year), parseInt(month)];
      if (
        rosterYear > currentYear ||
        (rosterYear === currentYear && rosterMonth >= currentMonth)
      ) {
        paths.push(`${year}/${month}`);
      }
    });
  });

  return paths;
}

// Get future roster paths starting from a specific month
async function getFutureRosterPathsFromMonth(db, departmentPath, startYear, startMonth) {
  const snapshot = await get(ref(db, `${departmentPath}rostersDraft`));
  if (!snapshot.exists()) return [];

  const paths = [];

  Object.entries(snapshot.val()).forEach(([year, months]) => {
    Object.keys(months).forEach(month => {
      const [rosterYear, rosterMonth] = [parseInt(year), parseInt(month)];
      if (
        rosterYear > startYear ||
        (rosterYear === startYear && rosterMonth >= startMonth)
      ) {
        paths.push(`${year}/${month}`);
      }
    });
  });

  return paths;
}

/**
 * Checks if an employee has entries in any future rosters.
 */
export const checkFutureRosterEntries = async (employeeId, departmentPath) => {
  const db = getDatabase();
  const snapshot = await get(ref(db, `${departmentPath}rostersDraft`));
  if (!snapshot.exists()) return false;

  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth() + 1;

  for (const year of Object.keys(snapshot.val())) {
    for (const month of Object.keys(snapshot.val()[year])) {
      const rosterYear = parseInt(year);
      const rosterMonth = parseInt(month);
      if (
        rosterYear > currentYear ||
        (rosterYear === currentYear && rosterMonth >= currentMonth)
      ) {
        const rosterRef = ref(db, `${departmentPath}rostersDraft/${year}/${month}/employeeShifts/${employeeId}`);
        const snap = await get(rosterRef);
        if (snap.exists()) return true;
      }
    }
  }

  return false;
};

/**
 * Updates employee and related roster fields.
 */
export const updateEmployeeAndRoster = async (employeeId, updatedData, departmentPath) => {
  try {
    const db = getDatabase();
    const employeeRef = ref(db, `${departmentPath}employees/${employeeId}`);
    const employeeSnapshot = await get(employeeRef);

    if (!employeeSnapshot.exists()) {
      console.warn(`Employee with ID ${employeeId} not found.`);
      return;
    }

    // Get paths to future rosters
    const rosterPaths = await getFutureRosterPaths(db, departmentPath);
    if (rosterPaths.length === 0) {
      console.warn("No future rosters found to update.");
      return;
    }

    const rosterUpdates = {};
    let hasRosterUpdates = false; // Track if any roster updates were actually made

    // Update relevant fields in future rosters.
    for (const path of rosterPaths) {
      const rosterRef = ref(db, `${departmentPath}rostersDraft/${path}/employeeShifts/${employeeId}`);
      const rosterSnapshot = await get(rosterRef);

      if (rosterSnapshot.exists()) {
        const rosterEntry = rosterSnapshot.val();
        const rosterUpdateFields = {};

        // Iterate through the updated data to find fields that exist in the *roster entry*
        for (const key in updatedData) {
          if (rosterEntry.hasOwnProperty(key)) {
            // Important: Only update if the new data is different from the *roster* data.
            if (updatedData[key] !== rosterEntry[key]) {
              rosterUpdateFields[key] = updatedData[key];
            }
          }
        }
        if (Object.keys(rosterUpdateFields).length > 0) {
          rosterUpdates[`${path}/employeeShifts/${employeeId}`] = {
            ...rosterEntry,
            ...rosterUpdateFields,
          };
          hasRosterUpdates = true; // Set the flag
          console.log(`Updated roster at ${path} for employee ${employeeId} with:`, rosterUpdateFields);
        }
      } else {
        console.warn(`Employee ${employeeId} not found in roster at ${path}`); //keep going
      }
    }

    if (hasRosterUpdates) {
      await update(ref(db, `${departmentPath}rostersDraft`), rosterUpdates);
    } else {
      console.warn("No roster updates were needed.");
    }

    console.log("Employee and roster update process completed.");

  } catch (error) {
    console.error("Error updating employee and roster:", error);
    throw new Error("Failed to update employee and roster: " + error.message); // Re-throw for caller to handle
  }
};

/**
 * Removes employee from all future rosters.
 */
export const removeEmployeeFromRosters = async (employeeId, departmentPath) => {
  const db = getDatabase();
  const futurePaths = await getFutureRosterPaths(db, departmentPath);
  if (futurePaths.length === 0) return;

  const updates = {};
  futurePaths.forEach(path => {
    updates[`${departmentPath}rostersDraft/${path}/employeeShifts/${employeeId}`] = null;
  });

  if (Object.keys(updates).length > 0) {
    await update(ref(db), updates);
  }
};

// ===== PREVIEW FUNCTIONS FOR MODALS =====

/**
 * Preview what will happen when updating an employee from a specific month
 */
export const previewEmployeeUpdateFromMonth = async (employeeId, updateData, startYear, startMonth, selectedDepartment = null) => {
  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);
    const db = getDatabase();

    // Get current employee data
    const employeeRef = ref(db, `${departmentPath}employees/${employeeId}`);
    const employeeSnapshot = await get(employeeRef);

    if (!employeeSnapshot.exists()) {
      throw new Error(`Employee with ID ${employeeId} not found.`);
    }

    const currentEmployee = employeeSnapshot.val();
    const oldRowIndex = currentEmployee.rowIndex;

    // Determine what type of changes are being made
    const changedFields = [];
    const isRowIndexChange = updateData.rowIndex !== undefined && updateData.rowIndex !== oldRowIndex;

    // Identify changed fields
    Object.keys(updateData).forEach(key => {
      if (key !== 'rowIndex' && updateData[key] !== currentEmployee[key]) {
        changedFields.push(key);
      }
    });

    let affectedEmployees = [];
    let affectedMonths = [];
    let hasRosterEntries = false;
    let changeType = 'regular'; // 'regular', 'rowIndex', 'rowIndexSkipped'

    if (isRowIndexChange) {
      // Check if employee has future roster entries
      hasRosterEntries = await checkFutureRosterEntries(employeeId, departmentPath);

      if (hasRosterEntries) {
        changeType = 'rowIndex';

        // Get affected employees (those whose row indices will change)
        const employeesSnapshot = await get(ref(db, `${departmentPath}employees`));
        if (employeesSnapshot.exists()) {
          const allEmployees = Object.values(employeesSnapshot.val());
          const sortedEmployees = [...allEmployees].sort((a, b) => a.rowIndex - b.rowIndex);

          if (updateData.rowIndex < oldRowIndex) {
            // Moving up
            for (let i = updateData.rowIndex; i < oldRowIndex; i++) {
              const emp = sortedEmployees.find(e => e.rowIndex === i);
              if (emp && emp.employeeId !== employeeId) {
                affectedEmployees.push({ ...emp, newRowIndex: i + 1 });
              }
            }
          } else {
            // Moving down
            for (let i = oldRowIndex + 1; i <= updateData.rowIndex; i++) {
              const emp = sortedEmployees.find(e => e.rowIndex === i);
              if (emp && emp.employeeId !== employeeId) {
                affectedEmployees.push({ ...emp, newRowIndex: i - 1 });
              }
            }
          }
        }

        // Get affected months - use the custom function for specific start month
        const futurePaths = await getFutureRosterPathsFromMonth(db, departmentPath, startYear, startMonth);
        affectedMonths = futurePaths.map(path => {
          const [year, month] = path.split('/');
          return { year: parseInt(year), month: parseInt(month) };
        });
      } else {
        changeType = 'rowIndexSkipped';
      }
    }

    // If no row index change but other changes, still get affected months
    if (!isRowIndexChange && changedFields.length > 0) {
      const futurePaths = await getFutureRosterPathsFromMonth(db, departmentPath, startYear, startMonth);
      affectedMonths = futurePaths.map(path => {
        const [year, month] = path.split('/');
        return { year: parseInt(year), month: parseInt(month) };
      });
    }

    return {
      currentEmployee,
      changedFields,
      isRowIndexChange,
      oldRowIndex,
      newRowIndex: updateData.rowIndex,
      hasRosterEntries,
      affectedEmployees,
      affectedMonths,
      changeType
    };
  } catch (error) {
    console.error('Error previewing employee update from month:', error);
    throw error;
  }
};

/**
 * Preview what will happen when updating an employee
 */
export const previewEmployeeUpdate = async (employeeId, updateData, selectedDepartment = null) => {
  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);
    const db = getDatabase();

    console.log('Preview employee update debug:', {
      employeeId,
      departmentPath,
      selectedDepartment,
      updateData
    });

    // Get current employee data
    const employeeRef = ref(db, `${departmentPath}employees/${employeeId}`);
    const employeeSnapshot = await get(employeeRef);

    console.log('Employee lookup result:', {
      path: `${departmentPath}employees/${employeeId}`,
      exists: employeeSnapshot.exists(),
      data: employeeSnapshot.exists() ? employeeSnapshot.val() : null
    });

    if (!employeeSnapshot.exists()) {
      // Let's also check if the employee exists in other departments
      const allDepartmentsRef = ref(db, 'departments');
      const allDepartmentsSnapshot = await get(allDepartmentsRef);

      if (allDepartmentsSnapshot.exists()) {
        const departments = allDepartmentsSnapshot.val();
        const foundInDepartments = [];

        Object.keys(departments).forEach(deptKey => {
          if (departments[deptKey].employees && departments[deptKey].employees[employeeId]) {
            foundInDepartments.push({
              department: deptKey,
              employee: departments[deptKey].employees[employeeId]
            });
          }
        });

        console.log('Employee found in other departments:', foundInDepartments);

        if (foundInDepartments.length > 0) {
          throw new Error(`Employee with ID ${employeeId} not found in current department (${departmentPath}), but found in: ${foundInDepartments.map(f => f.department).join(', ')}`);
        }
      }

      throw new Error(`Employee with ID ${employeeId} not found.`);
    }

    const currentEmployee = employeeSnapshot.val();
    const oldRowIndex = currentEmployee.rowIndex;

    // Determine what type of changes are being made
    const changedFields = [];
    const isRowIndexChange = updateData.rowIndex !== undefined && updateData.rowIndex !== oldRowIndex;

    // Identify changed fields
    Object.keys(updateData).forEach(key => {
      if (key !== 'rowIndex' && updateData[key] !== currentEmployee[key]) {
        changedFields.push(key);
      }
    });

    let affectedEmployees = [];
    let affectedMonths = [];
    let hasRosterEntries = false;
    let changeType = 'regular'; // 'regular', 'rowIndex', 'rowIndexSkipped'

    if (isRowIndexChange) {
      // Check if employee has future roster entries
      hasRosterEntries = await checkFutureRosterEntries(employeeId, departmentPath);

      if (hasRosterEntries) {
        changeType = 'rowIndex';

        // Get affected employees for row index change
        const employeesSnapshot = await get(ref(db, `${departmentPath}employees`));
        if (employeesSnapshot.exists()) {
          const allEmployees = Object.values(employeesSnapshot.val());
          const sortedEmployees = [...allEmployees].sort((a, b) => a.rowIndex - b.rowIndex);

          const newRowIndex = updateData.rowIndex;

          if (newRowIndex < oldRowIndex) {
            // Moving up
            for (let i = newRowIndex; i < oldRowIndex; i++) {
              const emp = sortedEmployees.find(e => e.rowIndex === i);
              if (emp && emp.employeeId !== employeeId) {
                affectedEmployees.push({
                  ...emp,
                  oldRowIndex: i,
                  newRowIndex: i + 1
                });
              }
            }
          } else {
            // Moving down
            for (let i = oldRowIndex + 1; i <= newRowIndex; i++) {
              const emp = sortedEmployees.find(e => e.rowIndex === i);
              if (emp && emp.employeeId !== employeeId) {
                affectedEmployees.push({
                  ...emp,
                  oldRowIndex: i,
                  newRowIndex: i - 1
                });
              }
            }
          }
        }

        // Get affected months
        const futurePaths = await getFutureRosterPaths(db, departmentPath);
        affectedMonths = futurePaths.map(path => {
          const [year, month] = path.split('/');
          return { year: parseInt(year), month: parseInt(month) };
        });
      } else {
        changeType = 'rowIndexSkipped';
      }
    }

    // If no row index change but other changes, still get affected months
    if (!isRowIndexChange && changedFields.length > 0) {
      const futurePaths = await getFutureRosterPaths(db, departmentPath);
      affectedMonths = futurePaths.map(path => {
        const [year, month] = path.split('/');
        return { year: parseInt(year), month: parseInt(month) };
      });
    }

    return {
      currentEmployee,
      changedFields,
      isRowIndexChange,
      oldRowIndex,
      newRowIndex: updateData.rowIndex,
      hasRosterEntries,
      affectedEmployees,
      affectedMonths,
      changeType
    };
  } catch (error) {
    console.error('Error previewing employee update:', error);
    throw error;
  }
};

/**
 * Preview what will happen when deleting an employee
 */
export const previewEmployeeDelete = async (employeeId, selectedDepartment = null) => {
  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);
    const db = getDatabase();

    console.log('Preview employee delete debug:', {
      employeeId,
      departmentPath,
      selectedDepartment
    });

    // Get current employee data
    const employeeRef = ref(db, `${departmentPath}employees/${employeeId}`);
    const employeeSnapshot = await get(employeeRef);

    console.log('Employee delete lookup result:', {
      path: `${departmentPath}employees/${employeeId}`,
      exists: employeeSnapshot.exists(),
      data: employeeSnapshot.exists() ? employeeSnapshot.val() : null
    });

    if (!employeeSnapshot.exists()) {
      // Let's also check if the employee exists in other departments
      const allDepartmentsRef = ref(db, 'departments');
      const allDepartmentsSnapshot = await get(allDepartmentsRef);

      if (allDepartmentsSnapshot.exists()) {
        const departments = allDepartmentsSnapshot.val();
        const foundInDepartments = [];

        Object.keys(departments).forEach(deptKey => {
          if (departments[deptKey].employees && departments[deptKey].employees[employeeId]) {
            foundInDepartments.push({
              department: deptKey,
              employee: departments[deptKey].employees[employeeId]
            });
          }
        });

        console.log('Employee found in other departments:', foundInDepartments);

        if (foundInDepartments.length > 0) {
          throw new Error(`Employee with ID ${employeeId} not found in current department (${departmentPath}), but found in: ${foundInDepartments.map(f => f.department).join(', ')}`);
        }
      }

      throw new Error(`Employee with ID ${employeeId} not found.`);
    }

    const employee = employeeSnapshot.val();
    const deletedRowIndex = employee.rowIndex;

    // Get all employees to determine affected ones
    const employeesSnapshot = await get(ref(db, `${departmentPath}employees`));
    let affectedEmployees = [];

    if (employeesSnapshot.exists()) {
      const allEmployees = Object.values(employeesSnapshot.val());
      const remainingEmployees = allEmployees.filter(emp => emp.employeeId !== employeeId);

      // Find employees that will have their row index adjusted
      affectedEmployees = remainingEmployees
        .filter(emp => emp.rowIndex > deletedRowIndex)
        .map(emp => ({
          ...emp,
          oldRowIndex: emp.rowIndex,
          newRowIndex: emp.rowIndex - 1
        }))
        .sort((a, b) => a.oldRowIndex - b.oldRowIndex);
    }

    // Get affected months (future rosters)
    const futurePaths = await getFutureRosterPaths(db, departmentPath);
    const affectedMonths = futurePaths.map(path => {
      const [year, month] = path.split('/');
      return { year: parseInt(year), month: parseInt(month) };
    });

    return {
      employee,
      affectedEmployees,
      affectedMonths,
      hasRowAdjustments: affectedEmployees.length > 0
    };
  } catch (error) {
    console.error('Error previewing employee delete:', error);
    throw error;
  }
};