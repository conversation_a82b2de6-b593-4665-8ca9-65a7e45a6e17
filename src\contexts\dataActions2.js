export {
  setRosterYear,
  setRoster<PERSON>onth,
  toggleRosterDraft,
  fetchRosterData,
  fetchAvailableYears,
  fetchAvailableMonths
} from './actions/rosterActions';

export {
  addEmployee,
  updateEmployee,
  deleteEmployee,
  fetchEmployeesData
} from './actions/employeeActions';

export {
  updateShift,
  deleteShift,
  fetchShiftsData
} from './actions/shiftActions';

export {
  updateContactInfo,
  deleteContactInfo,
  fetchContactInfo
} from './actions/contactInfoActions';

export {
  
  fetchCalendarMarkers,
  addCalendarMarker,
  updateCalendarMarker,
  deleteCalendarMarker,
} from './actions/calendarMarkerActions';   
// addCalendarMarker,
//   updateCalendarMarker,
//   deleteCalendarMarker,
// export{
//   setRosterYear,
//   setRosterMonth,
//   toggleRosterDraft,
//   fetchRosterData,
//   fetchAvailableYears,
//   fetchAvailableMonths,
//   addEmployee,
//   updateEmployee,
//   deleteEmployee,
//   fetchEmployeesData,
//   updateShift,
//   deleteShift,
//   fetchShiftsData,
//   updateContactInfo,
//   deleteContactInfo,
//   fetchContactInfo,
//   fetchCalendarMarkers
// }