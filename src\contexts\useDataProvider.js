// src/contexts/useDataProvider.js
import { useEffect, useCallback, useRef, useContext } from 'react';
import { AuthContext } from '../components/AuthProvider';
import { useDataDispatch, useRosterData } from './DataContext.jsx';
import {
  fetchRosterData,
  fetchAvailableYears,
  fetchAvailableMonths,
  fetchEmployeesData,
  fetchShiftsData,
  fetchContactInfo,
  fetchCalendarMarkers,
  addCalendarMarker,
  updateCalendarMarker,
  deleteCalendarMarker,
  setRosterYear,
  setRosterMonth,
  toggleRosterDraft,
  addEmployee,
  updateEmployee,
  deleteEmployee,
  updateShift,
  deleteShift,
  updateContactInfo,
  deleteContactInfo
} from './dataActions2';

/**
 * Custom hook to initialize and manage data fetching
 * @returns {Object} Data fetching and management functions
 */
export const useDataProvider = () => {
  const { selectedDepartment, isSuperAdmin, isRegionManager, isSuperUser, isRegionUser } = useContext(AuthContext);
  const dispatch = useDataDispatch();
  const { year, month, isDraftRoster } = useRosterData();
  const cleanupFunctions = useRef({});

  // Helper function to determine if we should use selectedDepartment
  const shouldUseSelectedDepartment = () => isSuperAdmin() || isRegionManager || isSuperUser || isRegionUser;

  // Initialize data fetching
  useEffect(() => {
    const initializeData = async () => {
      try {
        // Fetch initial data, passing selectedDepartment for super-admins and region-managers
        const deptToUse = shouldUseSelectedDepartment() ? selectedDepartment : null;
        await Promise.all([
          fetchAvailableYears(dispatch, isDraftRoster, null, deptToUse),
          fetchEmployeesData(dispatch, false, deptToUse),
          fetchShiftsData(dispatch, false, deptToUse),
          fetchCalendarMarkers(dispatch, false, deptToUse)
        ]);

        // Set up contact info subscription
        const cleanup = await fetchContactInfo(dispatch, false, deptToUse);
        cleanupFunctions.current.contactInfo = typeof cleanup === 'function' ? cleanup : () => {};
      } catch (err) {
        console.error('Error initializing data:', err);
        cleanupFunctions.current.contactInfo = () => {};
      }
    };

    initializeData();

    // Cleanup function
    return () => {
      Object.values(cleanupFunctions.current).forEach(cleanup => {
        if (typeof cleanup === 'function') {
          cleanup();
        }
      });
    };
  }, [dispatch, isDraftRoster, selectedDepartment, isSuperAdmin, isRegionManager, isSuperUser, isRegionUser]);

  // Refetch data when selectedDepartment changes for users who can switch departments
  useEffect(() => {
    if (shouldUseSelectedDepartment() && selectedDepartment) {
      const refetchData = async () => {
        try {
          // Clear existing subscriptions
          if (cleanupFunctions.current.rosterData) {
            cleanupFunctions.current.rosterData();
          }
          if (cleanupFunctions.current.contactInfo) {
            cleanupFunctions.current.contactInfo();
          }

          // Refetch all data with new department
          await Promise.all([
            fetchAvailableYears(dispatch, isDraftRoster, null, selectedDepartment),
            fetchAvailableMonths(dispatch, year, isDraftRoster, null, selectedDepartment),
            fetchRosterData(dispatch, year, month, isDraftRoster, null, selectedDepartment),
            fetchEmployeesData(dispatch, true, selectedDepartment),
            fetchShiftsData(dispatch, true, selectedDepartment),
            fetchCalendarMarkers(dispatch, true, selectedDepartment)
          ]);

          // Re-establish contact info subscription
          const cleanup = await fetchContactInfo(dispatch, true, selectedDepartment);
          cleanupFunctions.current.contactInfo = typeof cleanup === 'function' ? cleanup : () => {};
        } catch (err) {
          console.error('Error refetching data for new department:', err);
        }
      };

      refetchData();
    }
  }, [selectedDepartment, dispatch, isDraftRoster, year, month, isSuperAdmin, isRegionManager, isSuperUser, isRegionUser]);

  // Function to fetch available years with an override for isDraftRoster
  const fetchAvailableYearsWithOverride = useCallback((overrideIsDraftRoster = null) => {
    try {
      const deptToUse = shouldUseSelectedDepartment() ? selectedDepartment : null;
      fetchAvailableYears(dispatch, isDraftRoster, overrideIsDraftRoster, deptToUse);
    } catch (err) {
      console.error('Error fetching available years:', err);
    }
  }, [dispatch, isDraftRoster, selectedDepartment, isSuperAdmin, isRegionManager, isSuperUser, isRegionUser]);

  // Function to fetch available months with an override for isDraftRoster
  const fetchAvailableMonthsWithOverride = useCallback((year, overrideIsDraftRoster = null) => {
    try {
      const deptToUse = shouldUseSelectedDepartment() ? selectedDepartment : null;
      fetchAvailableMonths(dispatch, year, isDraftRoster, overrideIsDraftRoster, deptToUse);
    } catch (err) {
      console.error('Error fetching available months:', err);
    }
  }, [dispatch, isDraftRoster, selectedDepartment, isSuperAdmin, isRegionManager, isSuperUser, isRegionUser]);

  // Fetch available months when year changes
  useEffect(() => {
    try {
      const deptToUse = shouldUseSelectedDepartment() ? selectedDepartment : null;
      fetchAvailableMonths(dispatch, year, isDraftRoster, null, deptToUse);
    } catch (err) {
      console.error('Error fetching available months:', err);
    }
  }, [dispatch, year, isDraftRoster, selectedDepartment, isSuperAdmin, isRegionManager, isSuperUser, isRegionUser]);

  // Fetch roster data when year, month, or isDraftRoster changes
  useEffect(() => {
    // Clean up previous roster data subscription if it exists
    if (typeof cleanupFunctions.current.rosterData === 'function') {
      cleanupFunctions.current.rosterData();
    }

    // Set up new subscription
    const deptToUse = shouldUseSelectedDepartment() ? selectedDepartment : null;
    fetchRosterData(dispatch, year, month, isDraftRoster, null, deptToUse)
      .then(cleanup => {
        cleanupFunctions.current.rosterData = typeof cleanup === 'function' ? cleanup : () => {};
      })
      .catch(err => {
        console.error('Error setting up roster data subscription:', err);
        cleanupFunctions.current.rosterData = () => {};
      });

    // Cleanup function
    return () => {
      if (typeof cleanupFunctions.current.rosterData === 'function') {
        cleanupFunctions.current.rosterData();
      }
    };
  }, [dispatch, year, month, isDraftRoster, selectedDepartment, isSuperAdmin, isRegionManager, isSuperUser, isRegionUser]);

  // Function to fetch roster data with an override for isDraftRoster
  const fetchRosterDataWithOverride = useCallback((overrideIsDraftRoster = null) => {
    // Clean up previous subscription
    if (typeof cleanupFunctions.current.rosterData === 'function') {
      cleanupFunctions.current.rosterData();
    }

    // Set up new subscription with the override
    const deptToUse = shouldUseSelectedDepartment() ? selectedDepartment : null;
    fetchRosterData(
      dispatch,
      year,
      month,
      isDraftRoster,
      overrideIsDraftRoster,
      deptToUse
    )
      .then(cleanup => {
        cleanupFunctions.current.rosterData = typeof cleanup === 'function' ? cleanup : () => {};
      })
      .catch(err => {
        console.error('Error setting up roster data subscription:', err);
        cleanupFunctions.current.rosterData = () => {};
      });
  }, [dispatch, year, month, isDraftRoster, selectedDepartment, isSuperAdmin, isRegionManager, isSuperUser, isRegionUser]);

  // Roster actions
  const changeYear = useCallback((newYear) => {
    setRosterYear(dispatch, newYear);
  }, [dispatch]);

  const changeMonth = useCallback((newMonth) => {
    setRosterMonth(dispatch, newMonth);
  }, [dispatch]);

  const toggleDraftMode = useCallback(() => {
    toggleRosterDraft(dispatch);
  }, [dispatch]);

  const refetchRosterData = useCallback(() => {
    // Clean up previous subscription
    if (typeof cleanupFunctions.current.rosterData === 'function') {
      cleanupFunctions.current.rosterData();
    }

    // Set up new subscription
    const deptToUse = shouldUseSelectedDepartment() ? selectedDepartment : null;
    fetchRosterData(dispatch, year, month, isDraftRoster, null, deptToUse)
      .then(cleanup => {
        cleanupFunctions.current.rosterData = typeof cleanup === 'function' ? cleanup : () => {};
      })
      .catch(err => {
        console.error('Error setting up roster data subscription:', err);
        cleanupFunctions.current.rosterData = () => {};
      });
  }, [dispatch, year, month, isDraftRoster, selectedDepartment, isSuperAdmin, isRegionManager, isSuperUser, isRegionUser]);

  // Employee actions
  const addNewEmployee = useCallback((employeeData) => {
    const deptToUse = shouldUseSelectedDepartment() ? selectedDepartment : null;
    return addEmployee(dispatch, employeeData, deptToUse);
  }, [dispatch, selectedDepartment, isSuperAdmin, isRegionManager, isSuperUser, isRegionUser]);

  const updateExistingEmployee = useCallback((employeeId, employeeData) => {
    const deptToUse = shouldUseSelectedDepartment() ? selectedDepartment : null;
    return updateEmployee(dispatch, employeeId, employeeData, deptToUse);
  }, [dispatch, selectedDepartment, isSuperAdmin, isRegionManager, isSuperUser, isRegionUser]);

  const removeEmployee = useCallback((employeeId) => {
    const deptToUse = shouldUseSelectedDepartment() ? selectedDepartment : null;
    return deleteEmployee(dispatch, employeeId, deptToUse);
  }, [dispatch, selectedDepartment, isSuperAdmin, isRegionManager, isSuperUser, isRegionUser]);

  const refetchEmployeesData = useCallback((forceRefresh = true) => {
    const deptToUse = shouldUseSelectedDepartment() ? selectedDepartment : null;
    fetchEmployeesData(dispatch, forceRefresh, deptToUse);
  }, [dispatch, selectedDepartment, isSuperAdmin, isRegionManager, isSuperUser, isRegionUser]);

  // Shift actions
  const updateShiftData = useCallback((shiftName, shiftData) => {
    const deptToUse = shouldUseSelectedDepartment() ? selectedDepartment : null;
    return updateShift(dispatch, shiftName, shiftData, deptToUse);
  }, [dispatch, selectedDepartment, isSuperAdmin, isRegionManager, isSuperUser, isRegionUser]);

  const removeShift = useCallback((shiftName) => {
    const deptToUse = shouldUseSelectedDepartment() ? selectedDepartment : null;
    return deleteShift(dispatch, shiftName, deptToUse);
  }, [dispatch, selectedDepartment, isSuperAdmin, isRegionManager, isSuperUser, isRegionUser]);

  const refetchShiftsData = useCallback((forceRefresh = true) => {
    const deptToUse = shouldUseSelectedDepartment() ? selectedDepartment : null;
    fetchShiftsData(dispatch, forceRefresh, deptToUse);
  }, [dispatch, selectedDepartment, isSuperAdmin, isRegionManager, isSuperUser, isRegionUser]);

  // Contact info actions
  const updateContactInfoData = useCallback((id, data) => {
    const deptToUse = shouldUseSelectedDepartment() ? selectedDepartment : null;
    return updateContactInfo(dispatch, id, data, deptToUse);
  }, [dispatch, selectedDepartment, isSuperAdmin, isRegionManager, isSuperUser, isRegionUser]);

  const removeContactInfo = useCallback((id) => {
    const deptToUse = shouldUseSelectedDepartment() ? selectedDepartment : null;
    return deleteContactInfo(dispatch, id, deptToUse);
  }, [dispatch, selectedDepartment, isSuperAdmin, isRegionManager, isSuperUser, isRegionUser]);

  const refetchContactInfo = useCallback((forceRefresh = true) => {
    // Clean up previous subscription
    if (typeof cleanupFunctions.current.contactInfo === 'function') {
      cleanupFunctions.current.contactInfo();
    }

    // Set up new subscription
    const deptToUse = shouldUseSelectedDepartment() ? selectedDepartment : null;
    fetchContactInfo(dispatch, forceRefresh, deptToUse)
      .then(cleanup => {
        cleanupFunctions.current.contactInfo = typeof cleanup === 'function' ? cleanup : () => {};
      })
      .catch(err => {
        console.error('Error setting up contact info subscription:', err);
        cleanupFunctions.current.contactInfo = () => {};
      });
  }, [dispatch, selectedDepartment, isSuperAdmin, isRegionManager, isSuperUser, isRegionUser]);

  // Calendar markers actions
  const refetchCalendarMarkers = useCallback((forceRefresh = true) => {
    const deptToUse = shouldUseSelectedDepartment() ? selectedDepartment : null;
    fetchCalendarMarkers(dispatch, forceRefresh, deptToUse);
  }, [dispatch, selectedDepartment, isSuperAdmin, isRegionManager, isSuperUser, isRegionUser]);
 
   const addNewCalendarMarker = useCallback((markerType, markerData) => {
    return addCalendarMarker(dispatch, markerType, markerData, isSuperAdmin() ? selectedDepartment : null);
  }, [dispatch, selectedDepartment, isSuperAdmin, isRegionManager, isSuperUser, isRegionUser]);

  const updateExistingCalendarMarker = useCallback((markerType, markerId, markerData) => {
    return updateCalendarMarker(dispatch, markerType, markerId, markerData, isSuperAdmin() ? selectedDepartment : null);
  }, [dispatch, selectedDepartment, isSuperAdmin, isRegionManager, isSuperUser, isRegionUser]);

  const deleteExistingCalendarMarker = useCallback((markerId, markerType) => {
    return deleteCalendarMarker(dispatch, markerId, markerType, isSuperAdmin() ? selectedDepartment : null);
  }, [dispatch, selectedDepartment, isSuperAdmin, isRegionManager, isSuperUser, isRegionUser]);
  // Cache management
  const clearCache = useCallback(() => {
    // Import dynamically to avoid circular dependencies
    const dataCache = require('../utils/cacheUtils').default;
    dataCache.clear();
    console.log('Data cache cleared');
  }, []);

  return {
    // Roster actions
    changeYear,
    changeMonth,
    toggleDraftMode,
    refetchRosterData,
    fetchRosterDataWithOverride,
    fetchAvailableYearsWithOverride,
    fetchAvailableMonthsWithOverride,

    // Employee actions
    addNewEmployee,
    updateExistingEmployee,
    removeEmployee,
    refetchEmployeesData,

    // Shift actions
    updateShiftData,
    removeShift,
    refetchShiftsData,

    // Contact info actions
    updateContactInfoData,
    removeContactInfo,
    refetchContactInfo,

    // Calendar markers actions
    refetchCalendarMarkers,
    addNewCalendarMarker,
    updateExistingCalendarMarker,
    deleteExistingCalendarMarker,

    // Cache management
    clearCache
  };
};

export default useDataProvider;