// src/components/RosterControls.js
import React, { useState, useEffect, useRef } from 'react';

/**
 * RosterControls component - Control panel with buttons for roster management
 *
 * @param {Object} props - Component props
 * @param {Array} props.availableEmployees - List of available employees
 * @param {Function} props.handleAddEmployee - Function to handle adding an employee
 * @param {Function} props.handleAddRoster - Function to handle adding a roster
 * @param {Function} props.handleDeleteLastRow - Function to handle deleting the last row
 * @param {Function} props.handlePublishChanges - Function to handle publishing changes
 * @param {Function} props.handleToggleRosterView - Function to toggle between draft and live roster view
 * @param {boolean} props.addEmployeeLoading - Whether adding an employee is in progress
 * @param {string} props.addEmployeeError - Error message when adding an employee fails
 * @param {boolean} props.addRosterLoading - Whether adding a roster is in progress
 * @param {boolean} props.isCurrentOrFutureMonth - Whether the current roster is for the current or future month
 * @param {Array} props.localRosterData - Current roster data
 * @param {boolean} props.isDeleting - Whether deleting a row is in progress
 * @param {boolean} props.isPublishing - Whether publishing changes is in progress
 * @param {boolean} props.isRosterDraft - Whether the current roster is a draft
 * @returns {JSX.Element} RosterControls component
 */
const RosterControls = ({
  availableEmployees,
  handleAddEmployee,
  handleAddRoster,
  handleDeleteLastRow,
  handlePublishChanges,
  handleToggleRosterView,
  addEmployeeLoading,
  addEmployeeError,
  addRosterLoading,
  isCurrentOrFutureMonth,
  localRosterData,
  isDeleting,
  isPublishing,
  isRosterDraft,
}) => {
  // Use a ref to track the select element
  const selectRef = useRef(null);

  // State to track the currently selected employee
  const [selectedEmployeeId, setSelectedEmployeeId] = useState('');

  // Reset the select element when availableEmployees changes
  useEffect(() => {
    if (selectRef.current) {
      selectRef.current.value = '';
    }
    setSelectedEmployeeId('');
  }, [availableEmployees]);

  // Handle employee selection
  const onEmployeeSelect = (e) => {
    const employeeId = e.target.value;
    setSelectedEmployeeId(employeeId);

    if (employeeId) {
      handleAddEmployee(employeeId);
    }
  };

  // Handle direct employee selection (for single employee case)
  const handleSingleEmployeeSelect = (employeeId) => {
    if (employeeId && availableEmployees.length === 1) {
      setSelectedEmployeeId(employeeId);
      handleAddEmployee(employeeId);
    }
  };

  // If there's only one employee, provide a button to select it
  const renderSingleEmployeeOption = () => {
    if (availableEmployees.length === 1 && isRosterDraft) {
      const employee = availableEmployees[0];
      return (
        <button
          onClick={() => handleSingleEmployeeSelect(employee.employeeId)}
          disabled={addEmployeeLoading}
          className="ml-2 px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 text-sm"
        >
          Add {employee.name}
        </button>
      );
    }
    return null;
  };

  return (
    <div className="mb-6 p-4 bg-gray-100 rounded-lg shadow w-fit">
      <div className="flex items-center space-x-4">
        <label className="text-sm font-medium text-gray-700">Add Employee:</label>
        <select
          ref={selectRef}
          onChange={onEmployeeSelect}
          value={selectedEmployeeId}
          disabled={addEmployeeLoading || availableEmployees.length === 0 || !isRosterDraft}
          className="p-2 border border-gray-300 rounded-md bg-white text-sm focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
        >
          <option value="" disabled>
            Select an employee
          </option>
          {availableEmployees.map((emp) => (
            <option key={emp.employeeId} value={emp.employeeId}>
              {emp.name}
            </option>
          ))}
        </select>
        {/* {renderSingleEmployeeOption()} */}
        {addEmployeeLoading && <span className="text-sm text-gray-600">Adding...</span>}
        {addEmployeeError && <span className="text-sm text-red-600">Error: {addEmployeeError}</span>}
        <button
          onClick={handleAddRoster}
          disabled={addRosterLoading || !isRosterDraft}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
        >
          Add Next Month Roster
        </button>
        <button
          onClick={handleDeleteLastRow}
          disabled={!isCurrentOrFutureMonth || localRosterData.length === 0 || isDeleting || !isRosterDraft}
          className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
        >
          Delete Last Row
        </button>
        <button
          onClick={handlePublishChanges}
          disabled={isPublishing || localRosterData.length === 0 || !isRosterDraft}
          className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
        >
          Publish Changes
        </button>
        <button
          onClick={handleToggleRosterView}
          className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
        >
          Switch to {isRosterDraft ? 'Live' : 'Draft'} Roster
        </button>
      </div>
    </div>
  );
};

export default RosterControls;
