// src\components\RosterNavBar.js
import React, {memo, useState, useCallback}from 'react';
function RosterNavBar({
  availableYears,
  availableMonths,
  year,
  setYear,
  selectedMonth,
  onMonthSelect,
  setMonth,
  onCrossMonthDragOver,
  onCrossMonthDragLeave,
  onCrossMonthDrop
}) {
    // Removed excessive console logging to reduce output during hover

    // State for drag over indication
    const [dragOverMonth, setDragOverMonth] = useState(null);

    const handleYearChange = (event) => {
      const newYear = parseInt(event.target.value);
      setYear(newYear);
      // Reset the month to the first available month for the new year, or 1 if none are available
      const firstMonth = availableMonths.length > 0 ? availableMonths[0] : 1;
      setMonth(firstMonth);
    };

    // Handle drag over month button
    const handleMonthDragOver = useCallback((e, targetMonth) => {
      e.preventDefault();
      e.dataTransfer.dropEffect = 'copy';
      setDragOverMonth(targetMonth);

      // Call parent handler if provided
      if (onCrossMonthDragOver) {
        onCrossMonthDragOver(e, year, targetMonth);
      }
    }, [year, onCrossMonthDragOver]);

    // Handle drag leave month button
    const handleMonthDragLeave = useCallback((e) => {
      setDragOverMonth(null);

      // Call parent handler if provided
      if (onCrossMonthDragLeave) {
        onCrossMonthDragLeave(e);
      }
    }, [onCrossMonthDragLeave]);

    // Handle drop on month button
    const handleMonthDrop = useCallback((e, targetMonth) => {
      e.preventDefault();
      setDragOverMonth(null);

      // Call parent handler if provided
      if (onCrossMonthDrop) {
        onCrossMonthDrop(e, year, targetMonth);
      }
    }, [year, onCrossMonthDrop]);
  
    return (
      <div style={{ 
        position: 'fixed', 
        left: 0, 
        top: '85px', 
        width: '120px', 
        height: 'calc(100vh - 60px)', 
        padding: '0.5rem 0', 
        backgroundColor: '#f3f4f6', 
        borderRight: '1px solid #d1d5db',
        overflowY: 'auto',
        boxSizing: 'border-box',
        zIndex: 10
      }}>
        {/* Smart Title */}
        <div style={{
          padding: '0.5rem',
          textAlign: 'center',
          fontSize: '1rem',
          fontWeight: 'bold',
          color: '#1f2937',
          borderBottom: '1px solid #d1d5db',
          marginBottom: '0.5rem'
        }}>
          Roster Select
        </div>
  
        {/* Year Selection Dropdown */}
        <div style={{ padding: '0 0.5rem', marginBottom: '0.5rem' }}>
          <select
            value={year}
            onChange={handleYearChange}
            style={{
              width: '100%',
              padding: '0.25rem',
              fontSize: '0.875rem',
              borderRadius: '0.25rem',
              border: '1px solid #d1d5db',
              backgroundColor: '#ffffff',
              cursor: 'pointer'
            }}
          >
            {availableYears.map(yearOption => (
              <option key={yearOption} value={yearOption}>
                {yearOption}
              </option>
            ))}
          </select>
        </div>
  
        {/* Month List */}
        {!availableMonths || availableMonths.length === 0 ? (
          <p style={{ fontSize: '0.875rem', color: '#b91c1c', textAlign: 'center', padding: '0.5rem' }}>
            No rosters available
          </p>
        ) : (
          availableMonths.map(month => {
            const isActive = month === selectedMonth;
            const isDragOver = dragOverMonth === month;
            // Removed excessive console logging
            return (
              <div
                key={month}
                onClick={() => onMonthSelect(month)}
                onDragOver={(e) => handleMonthDragOver(e, month)}
                onDragLeave={handleMonthDragLeave}
                onDrop={(e) => handleMonthDrop(e, month)}
                style={{
                  backgroundColor: isDragOver
                    ? '#bfdbfe'
                    : isActive
                      ? '#ffffff'
                      : '#e5e7eb',
                  borderRight: isActive ? '2px solid #1f2937' : 'none',
                  border: isDragOver ? '2px dashed #2563eb' : 'none',
                  padding: '0.5rem',
                  marginBottom: '0.25rem',
                  textAlign: 'center',
                  fontSize: '0.875rem',
                  fontWeight: 'bold',
                  color: isDragOver ? '#1e40af' : '#1f2937',
                  cursor: 'pointer',
                  borderRadius: '0.25rem 0 0 0.25rem',
                  transition: 'background-color 0.2s, border 0.2s',
                  position: 'relative'
                }}
              >
                {new Date(year, month - 1).toLocaleString('default', { month: 'short' })} {year}
                {isDragOver && (
                  <div style={{
                    position: 'absolute',
                    top: '50%',
                    right: '4px',
                    transform: 'translateY(-50%)',
                    fontSize: '0.75rem',
                    color: '#1e40af'
                  }}>
                    📅
                  </div>
                )}
              </div>
            );
          })
        )}
      </div>
    );
  }
  
 //export default RosterNavBar;
  export default memo(RosterNavBar);