{"department_name": "Department Name", "config": {"maxAdmins": 2}, "calendar_markers": {"holiday_markers": {"holiday1": {"color": "#4a5899", "createdAt": "2023-01-01T00:00:00.000Z", "endDate": {"day": 25, "month": 12, "year": 2023}, "name": "Christmas", "recurring": true, "startDate": {"day": 25, "month": 12, "year": 2023}, "updatedAt": "2023-01-01T00:00:00.000Z"}}, "special_day_markers": {"special1": {"color": "#d08eea", "createdAt": "2023-01-01T00:00:00.000Z", "endDate": {"day": 31, "month": 3, "year": 2023}, "name": "Special Event", "startDate": {"day": 1, "month": 3, "year": 2023}, "updatedAt": "2023-01-01T00:00:00.000Z"}}, "weekday_markers": {"weekday1": {"color": "#d35555", "createdAt": "2023-01-01T00:00:00.000Z", "name": "Friday", "updatedAt": "2023-01-01T00:00:00.000Z", "weekday": "Friday"}}}, "contactInfo": {"Manager": {"phone": "(+*************"}, "DutyPhone": {"phone": "(+*************"}}, "employees": {"EMP001": {"createdAt": "2023-01-01T00:00:00.000Z", "email": "<EMAIL>", "employeeId": "EMP001", "name": "<PERSON>ple Employee", "phone": "(+*************", "position": "Staff", "rowIndex": 1, "srcNumber": "EMP001", "updatedAt": "2023-01-01T00:00:00.000Z"}}, "shifts": {"D": {"color": "#ffffff", "description": "Day Shift", "endTime": "17:30", "hours": 10, "name": "D", "startTime": "8:00"}, "N": {"color": "#ddebf7", "description": "Night Shift", "endTime": "8:00", "hours": 12, "name": "N", "startTime": "20:00"}, "O": {"color": "#93c47d", "description": "Off", "endTime": "0:00", "hours": 0, "name": "O", "startTime": "0:00"}, "X": {"color": "#ffff00", "description": "Leave", "endTime": "", "hours": 0, "name": "X", "startTime": ""}}, "rosters": {"2023": {"1": {"employeeShifts": {"EMP001": {"aircraft": "0:00", "leaveDays": 0, "name": "<PERSON>ple Employee", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "Staff", "rowIndex": 1, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "EMP001", "workDays": 20, "workTime": "200:00"}}, "month": 1, "year": 2023, "createdAt": "2023-01-01T00:00:00.000Z", "updatedAt": "2023-01-01T00:00:00.000Z"}}}, "rostersDraft": {"2023": {"1": {"employeeShifts": {"EMP001": {"aircraft": "0:00", "leaveDays": 0, "name": "<PERSON>ple Employee", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "Staff", "rowIndex": 1, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "EMP001", "workDays": 20, "workTime": "200:00"}}, "month": 1, "year": 2023, "createdAt": "2023-01-01T00:00:00.000Z", "updatedAt": "2023-01-01T00:00:00.000Z"}}}, "telegram_subscribers": {"123456789": {"status": "active", "subscribed": true}}, "whatsapp_subscribers": {"+123456789": {"status": "active", "subscribed": true}}}