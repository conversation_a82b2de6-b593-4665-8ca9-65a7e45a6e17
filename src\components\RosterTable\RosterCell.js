import React from 'react';

const RosterCell = ({
  shift,
  isSelected,
  isMultiSelected,
  isDropTarget,
  isPasteTarget,
  hasCrossMonthData,
  onPaste,
  onClick,
  onMouseDown,
  onMouseEnter,
  onDragStart,
  onDragOver,
  onDrop,
  shiftTypes,
  shiftColors,
  changedCells,
  isNew,
  isMissing,
  isChanged,
  dragging,
  dropdownPosition,
  setDropdownPosition,
  multiSelection,
  lastSelected,
  crossMonthPasteTarget,
  mode,
  crossMonthDragData
}) => {
  // Visual state logic
  const isCrossMonthPasteTarget = crossMonthPasteTarget && crossMonthPasteTarget.techId && crossMonthPasteTarget.day;
  return (
    <td
      style={{
        width: '40px',
        height: '40px',
        textAlign: 'center',
        verticalAlign: 'middle',
        backgroundColor: isCrossMonthPasteTarget ? '#a5b4fc' : isSelected || isMultiSelected ? '#fef08a' : isDropTarget ? '#bfdbfe' : isChanged ? '#fed7aa' : isMissing ? '#fee2e2' : isNew ? '#f3f4f6' : (shift ? (shiftColors && shiftColors[shift]) : 'white'),
        color: shift ? (shiftColors && shiftColors[shift] ? 'black' : 'gray') : 'gray',
        fontWeight: shift ? 'bold' : 'normal',
        cursor: mode === 'crossMonthDrag' && crossMonthDragData ? 'copy' : 'default',
        border: isDropTarget ? '2.5px solid #0074D9' : isMultiSelected ? '2px solid #3b82f6' : isSelected ? '2px solid #f87171' : '1px solid #e5e7eb',
        transition: 'background-color 0.2s, border 0.18s',
        position: 'relative',
        zIndex: isDropTarget ? 10 : undefined,
        opacity: dragging && !isSelected ? 0.7 : 1,
      }}
      onClick={onClick}
      onMouseDown={onMouseDown}
      onMouseEnter={onMouseEnter}
      onDragStart={onDragStart}
      onDragOver={onDragOver}
      onDrop={onDrop}
      draggable={!!onDragStart}
    >
      {isNew ? (
        <span title="No shifts scheduled" style={{ color: '#6b7280' }}>--</span>
      ) : isMissing ? (
        <span title="Shift data missing for this day" style={{ color: '#b91c1c' }}>?</span>
      ) : (
        shift || '—'
      )}
      {/* Dropdown and other overlays can be added here if needed */}
    </td>
  );
};

export default RosterCell;