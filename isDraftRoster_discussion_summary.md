# isDraftRoster State Management Discussion

## Current Approach (Global State with Overrides)
- **Global state**: `isDraftRoster` is maintained in the context
- **Override mechanism**: Components override this value when fetching data

## Alternative Approach (Local State)
- **Local state**: Each component maintains its own `isDraftRoster` value
- **No global state**: Remove `isDraftRoster` from the context entirely

## Security Analysis

### Global State with Overrides (Current Approach)
- **Pros**:
  - Centralized control over the default state
  - Clear audit trail of which components are overriding the default
  - Explicit overrides make the intention clear in the code
- **Cons**:
  - Risk of forgetting to apply the override in a new component
  - Global state could be accidentally modified by components
  - Technically savvy users could potentially manipulate the global state

### Local State Approach
- **Pros**:
  - Each component is isolated and can't affect others
  - No risk of global state leaking between components
  - Clearer separation of concerns
  - Reduced attack surface for client-side manipulation
- **Cons**:
  - No centralized control or visibility
  - Harder to enforce consistent behavior across components
  - Duplicate code across components

## Data Security Perspective

From a data security perspective, the local state approach is generally safer because:

1. **Isolation**: Components can't accidentally affect each other
2. **Principle of least privilege**: Components only have access to what they need
3. **Reduced attack surface**: No global state that could be manipulated

However, the current approach with explicit overrides also has security benefits:

1. **Visibility**: It's clear which components are using which data
2. **Auditability**: Easier to track and audit data access patterns
3. **Centralized control**: Easier to enforce security policies

## Implementation Complexity

### Converting to Local State

Converting to local state would involve:

1. Removing `isDraftRoster` from the context state
2. Adding local state to each component that needs it
3. Modifying the data fetching functions to accept a direct `isDraftRoster` parameter instead of using context + override

This would simplify the code by removing the override mechanism, but would require changes to multiple files.

## Security Vulnerabilities in Current Implementation

In the current implementation:

1. **Client-Side Manipulation**: A technically savvy user could potentially:
   - Modify the React component state through browser developer tools
   - Call `toggleRosterDraft` directly from the console
   - Intercept and modify network requests

2. **Data Path Determination**:
   - The `isDraftRoster` state determines which Firebase path is used (`rostersDraft` vs `rosters`)
   - If manipulated, users could potentially access draft roster data

3. **Mitigations**:
   - Component-level overrides ensure user-facing components always fetch from the live roster
   - UI restrictions disable certain functionality when not in draft mode

## Performance Considerations

Performance metrics show that core functions are very efficient:

| Operation | Count | Avg (ms) | Min (ms) | Max (ms) |
|-----------|-------|----------|----------|----------|
| getDaysInMonth | 24 | 0.00 | 0.00 | 0.00 |
| getDaysOfWeek | 24 | 0.00 | 0.00 | 0.10 |
| orderContactInfo | 12 | 0.00 | 0.00 | 0.00 |
| updateLocalRosterData | 18 | 0.28 | 0.10 | 0.50 |
| processShiftSummary | 53 | 0.00 | 0.00 | 0.00 |

The performance impact of either approach (global vs. local state) would be minimal.

## Recommendation

From a security and simplicity perspective, the local state approach would be better in the long run. Here's why:

1. **Clearer intent**: Each component explicitly declares whether it uses draft or live data
2. **Reduced complexity**: No need for override mechanisms
3. **Better isolation**: Components can't accidentally affect each other
4. **Easier maintenance**: New components don't need to remember to override the global state
5. **Enhanced security**: Reduced attack surface for client-side manipulation

However, the current approach with overrides is also valid and has the advantage of being more centralized and easier to audit.

## Implementation Plan for Local State Approach

If deciding to move to local state:

1. **Phase 1**: Modify data fetching functions to accept direct parameters
   - Update `fetchRosterData`, `fetchAvailableYears`, and `fetchAvailableMonths` to accept direct parameters
   - Remove override parameters as they'll no longer be needed

2. **Phase 2**: Update components to use local state
   - Add local `isDraftRoster` state to components that need to toggle (like `Home.js`)
   - Set fixed values in user-facing components (e.g., `const isDraftRoster = false` in `EmployeeSearch.jsx`)
   - Pass the local state directly to data fetching functions

3. **Phase 3**: Remove global state
   - Remove `isDraftRoster` from the context state
   - Remove toggle functions from the global context
   - Update any remaining components to use local state

This phased approach allows for incremental changes and testing at each step.
