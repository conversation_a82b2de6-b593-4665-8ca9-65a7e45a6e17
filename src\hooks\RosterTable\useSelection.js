// src/hooks/RosterTable/useSelection.js
import { useState, useEffect, useCallback } from 'react';

/**
 * Custom hook to manage selection state in the roster table
 *
 * @param {Array} rosterData - The roster data
 * @param {number} daysInMonth - Number of days in the month
 * @param {string} mode - Current mode ('update', 'drag', 'dialog', 'view')
 * @param {boolean} isRosterDraft - Whether the roster is in draft mode
 * @returns {Object} Selection state and functions
 */
const useSelection = (rosterData, daysInMonth, mode, isRosterDraft) => {
  // Rectangular selection
  const [selection, setSelection] = useState({
    start: { techId: null, day: null },
    end: { techId: null, day: null }
  });

  // Track last selected cell for Shift+Click
  const [lastSelected, setLastSelected] = useState({ techId: null, day: null });

  // Multi-cell selection for Ctrl+Click
  const [multiSelection, setMultiSelection] = useState([]);

  // Dragging state
  const [dragging, setDragging] = useState(false);

  // Dropdown position for update mode
  const [dropdownPosition, setDropdownPosition] = useState({ techId: null, day: null });

  // Drop target for visual feedback
  const [dropTarget, setDropTarget] = useState(null);

  // Clear selection
  const handleClearSelection = useCallback(() => {
    setSelection({ start: { techId: null, day: null }, end: { techId: null, day: null } });
    setMultiSelection([]);
    setDropTarget(null);
    setDropdownPosition({ techId: null, day: null });
  }, []);

  // Ctrl+A: Select all in drag mode
  useEffect(() => {
    const handleKeyDown = (e) => {
      if ((e.ctrlKey || e.metaKey) && e.key.toLowerCase() === 'a' && mode === 'drag') {
        e.preventDefault();
        setMultiSelection([]); // Clear multi-selection
        if (rosterData.length > 0 && daysInMonth > 0) {
          setSelection({
            start: { techId: rosterData[0].employeeId, day: 1 },
            end: { techId: rosterData[rosterData.length - 1].employeeId, day: daysInMonth }
          });
          setLastSelected({
            techId: rosterData[rosterData.length - 1].employeeId,
            day: daysInMonth
          });
        }
      }
    };
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [mode, rosterData, daysInMonth]);

  // Shift+Arrow: Extend selection in drag mode
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (mode !== 'drag' || !selection.start.techId || !selection.end.techId || !e.shiftKey) return;

      const techIds = rosterData.map(t => t.employeeId);
      let { techId, day } = selection.end;
      let rowIdx = techIds.indexOf(techId);
      let colIdx = parseInt(day);
      let changed = false;

      if (e.key === 'ArrowRight' && colIdx < daysInMonth) {
        colIdx++;
        changed = true;
      } else if (e.key === 'ArrowLeft' && colIdx > 1) {
        colIdx--;
        changed = true;
      } else if (e.key === 'ArrowDown' && rowIdx < techIds.length - 1) {
        rowIdx++;
        changed = true;
      } else if (e.key === 'ArrowUp' && rowIdx > 0) {
        rowIdx--;
        changed = true;
      }

      if (changed) {
        e.preventDefault();
        setSelection(sel => ({
          ...sel,
          end: { techId: techIds[rowIdx], day: colIdx }
        }));
        setLastSelected({ techId: techIds[rowIdx], day: colIdx });
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [mode, rosterData, selection, daysInMonth]);

  // Handle cell mouse down for rectangular selection
  const handleCellMouseDown = useCallback((techId, day, event) => {
    // Prevent selection if not in draft mode
    if (!isRosterDraft || mode !== 'drag') return;

    const dayNum = parseInt(day);
    const techIds = rosterData.map(t => t.employeeId);

    // Ctrl+Click: Toggle cell in multiSelection
    if ((event.ctrlKey || event.metaKey) && !event.shiftKey) {
      setSelection({ start: { techId: null, day: null }, end: { techId: null, day: null } }); // Clear rectangle selection
      setMultiSelection(prev => {
        const exists = prev.some(cell => cell.techId === techId && cell.day === dayNum);
        if (exists) {
          // Remove from selection
          return prev.filter(cell => !(cell.techId === techId && cell.day === dayNum));
        } else {
          // Add to selection
          return [...prev, { techId, day: dayNum }];
        }
      });
      setLastSelected({ techId, day: dayNum });
      return;
    }

    // Shift+Click: Extend selection from last selected cell
    if (event.shiftKey && lastSelected.techId && lastSelected.day) {
      setDragging(true);
      setSelection({
        start: { techId: lastSelected.techId, day: parseInt(lastSelected.day) },
        end: { techId, day: dayNum }
      });
      setLastSelected({ techId, day: dayNum });
      document.addEventListener('mouseup', handleMouseUp);
      return;
    }

    // Check if cell is within current selection rectangle
    if (
      selection.start.techId && selection.end.techId &&
      techIds && Array.isArray(techIds)
    ) {
      const rowIdx = techIds.indexOf(techId);
      const startRow = techIds.indexOf(selection.start.techId);
      const endRow = techIds.indexOf(selection.end.techId);
      const minRow = Math.min(startRow, endRow);
      const maxRow = Math.max(startRow, endRow);
      const minCol = Math.min(selection.start.day, selection.end.day);
      const maxCol = Math.max(selection.start.day, selection.end.day);

      if (
        rowIdx >= minRow && rowIdx <= maxRow &&
        dayNum >= minCol && dayNum <= maxCol
      ) {
        // Do nothing; let drag-and-drop handle it
        return;
      }
    }

    // Otherwise, start a new selection
    if (!(event.ctrlKey || event.metaKey)) {
      setMultiSelection([]); // Clear multi-selection only if not Ctrl/Cmd
    }
    event.preventDefault();
    setDragging(true);
    setSelection({
      start: { techId, day: dayNum },
      end: { techId, day: dayNum }
    });
    setLastSelected({ techId, day: dayNum });
    document.addEventListener('mouseup', handleMouseUp);
  }, [isRosterDraft, mode, rosterData, selection, lastSelected]);

  // Handle cell mouse enter for rectangular selection
  const handleCellMouseEnter = useCallback((techId, day) => {
    if (!isRosterDraft || !dragging || mode !== 'drag') return;
    setSelection(sel => ({
      start: sel.start,
      end: { techId, day: parseInt(day) }
    }));
  }, [isRosterDraft, dragging, mode]);

  // Handle mouse up to finish selection
  const handleMouseUp = useCallback(() => {
    setDragging(false);
    document.removeEventListener('mouseup', handleMouseUp);
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownPosition.techId !== null && !event.target.closest('td')) {
        setDropdownPosition({ techId: null, day: null });
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [dropdownPosition]);

  return {
    selection,
    setSelection,
    lastSelected,
    setLastSelected,
    multiSelection,
    setMultiSelection,
    dragging,
    setDragging,
    dropdownPosition,
    setDropdownPosition,
    dropTarget,
    setDropTarget,
    handleClearSelection,
    handleCellMouseDown,
    handleCellMouseEnter,
    handleMouseUp
  };
};

export default useSelection;
