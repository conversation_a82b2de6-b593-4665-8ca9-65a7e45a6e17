//src\EmployeeSearch.jsx
import React, { useState, useEffect } from 'react';

// Import data provider hooks
import {
  useRosterData,
  useEmployeeData
} from './contexts/DataContext.jsx';
import useDataProvider from './contexts/useDataProvider';

const EmployeeSearch = () => {
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth() + 1; // 1-12
  const currentDay = currentDate.getDate(); // 1-31
  const isDraftRoster = false; // Use live roster data

  const [year, setYear] = useState(currentYear);
  const [month, setMonth] = useState(currentMonth);
  const [day, setDay] = useState(currentDay);
  const [theme, setTheme] = useState('dark'); // Theme toggle

  // Use data from the DataProvider
  const {
    availableYears,
    availableMonths,
    data: rosterData,
    loading: rosterLoading,
    error: rosterError
  } = useRosterData();

  const {
    data: employeesData,
    loading: employeesLoading,
    error: employeesError
  } = useEmployeeData();

  // Use data provider actions
  const {
    changeYear: setDataYear,
    changeMonth: setDataMonth,
    refetchRosterData,
    fetchRosterDataWithOverride,
    fetchAvailableYearsWithOverride,
    fetchAvailableMonthsWithOverride
  } = useDataProvider();

  // Always use live roster data for this component
  useEffect(() => {
    console.log('Setting EmployeeSearch to use live roster data (isDraftRoster=false)');
    fetchRosterDataWithOverride(false);
    fetchAvailableYearsWithOverride(false);
  }, [fetchRosterDataWithOverride, fetchAvailableYearsWithOverride]);

  // Fetch available months with override when year changes
  useEffect(() => {
    console.log('Fetching available months for EmployeeSearch with live roster data');
    fetchAvailableMonthsWithOverride(year, false);
  }, [year, fetchAvailableMonthsWithOverride]);

  // Update DataProvider when year or month changes
  useEffect(() => {
    console.log('Updating data provider with year/month:', year, month);
    setDataYear(year);
    setDataMonth(month);
  }, [year, month, setDataYear, setDataMonth]);

  // Adjust day if selected month/year has fewer days
  useEffect(() => {
    const daysInMonth = new Date(year, month, 0).getDate(); // Get number of days in selected month
    if (day > daysInMonth) {
      setDay(daysInMonth); // Adjust to last day of the month
    }
  }, [year, month, day]);

  // Auto-select first available month when available months change
  useEffect(() => {
    const availableMonthsForYear = availableMonths[year] || [];
    if (availableMonthsForYear.length > 0) {
      // If current month is not in available months, select the first available month
      if (!availableMonthsForYear.includes(month)) {
        console.log('Current month not available, selecting first available month:', availableMonthsForYear[0]);
        setMonth(availableMonthsForYear[0]);
      }
    }
  }, [availableMonths, year, month]);

  // Filter employees who are rostered on the selected day
  const getEmployeesForDay = () => {
    if (!rosterData || rosterData.length === 0 || !employeesData) return [];

    // Convert employeesData array to object for easier lookup
    const employeesMap = {};
    if (Array.isArray(employeesData)) {
      employeesData.forEach(emp => {
        if (emp.employeeId) {
          employeesMap[emp.employeeId] = emp;
        }
      });
    } else if (typeof employeesData === 'object') {
      // If employeesData is already an object, use it directly
      Object.assign(employeesMap, employeesData);
    }

    return rosterData.map(employee => {
      const employeeDetails = employeesMap[employee.employeeId] || {};
      const dayStr = day.toString();
      return {
        name: employee.Name || 'Unknown',
        position: employee.Position || 'Technician',
        shift: employee[dayStr] || '',
        phone: employeeDetails.phone || '',
        email: employeeDetails.email || '',
      };
    });
  };

  const employeesForDay = getEmployeesForDay();

  return (
    <div
      className={`min-h-screen pt-32 pb-6 px-3 flex flex-col items-center ${
        theme === 'light' ? 'bg-gray-200' : 'bg-gray-800'
      }`}
      style={{
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23${theme === 'light' ? 'd1d5db' : '4b5563'}' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30v4h2V4h4V2h-4V0h-2zm-12 4h2v4h4v2h-4v4h-2v-4h-4V8h4V4z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
      }}
    >
      {/* Header */}
      <h1
        className={`text-2xl font-extrabold   mb-4 animate-fade-in ${
          theme === 'light' ? 'text-gray-900' : 'text-gray-100'
        }`}
      >
        Employee Search
      </h1>

      {/* Selectors */}
      <div
        className={`w-full max-w-[340px] rounded-xl shadow-md p-4  pl mb-4 ${
          theme === 'light' ? 'bg-white' : 'bg-gray-700'
        }`}
      >
        {/* Year Selector */}
        <div className="mb-3">
          <label
            className={`block text-xs font-semibold mb-1 ${
              theme === 'light' ? 'text-gray-700' : 'text-gray-200'
            }`}
          >
            Year
          </label>
          <select
            value={year}
            onChange={(e) => {
              const newYear = Number(e.target.value);
              console.log('Year selected:', newYear);
              // Force a re-fetch even if the same year is selected
              if (newYear === year) {
                refetchRosterData();
              }
              setYear(newYear);
            }}
            disabled={rosterLoading}
            className={`w-full p-2 border rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
              theme === 'light'
                ? 'border-gray-300 bg-white text-gray-900'
                : 'border-gray-600 bg-gray-600 text-gray-100'
            }`}
          >
            {rosterLoading ? (
              <option>Loading...</option>
            ) : availableYears.length > 0 ? (
              availableYears.map((y) => (
                <option key={y} value={y}>
                  {y}
                </option>
              ))
            ) : (
              <option value="">No years available</option>
            )}
          </select>
        </div>

        {/* Month Selector */}
        <div className="mb-3">
          <label
            className={`block text-xs font-semibold mb-1 ${
              theme === 'light' ? 'text-gray-700' : 'text-gray-200'
            }`}
          >
            Month
          </label>
          <select
            value={month}
            onChange={(e) => {
              const newMonth = Number(e.target.value);
              console.log('Month selected:', newMonth);
              // Force a re-fetch even if the same month is selected
              setMonth(newMonth);
            }}
            disabled={rosterLoading}
            className={`w-full p-2 border rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
              theme === 'light'
                ? 'border-gray-300 bg-white text-gray-900'
                : 'border-gray-600 bg-gray-600 text-gray-100'
            }`}
          >
            {rosterLoading ? (
              <option>Loading...</option>
            ) : (availableMonths[year] || []).length > 0 ? (
              (availableMonths[year] || []).map((m) => (
                <option key={m} value={m}>
                  {new Date(year, m - 1).toLocaleString('default', { month: 'long' })}
                </option>
              ))
            ) : (
              <option value="">No months available</option>
            )}
          </select>
        </div>

        {/* Day Selector */}
        <div className="mb-3">
          <label
            className={`block text-xs font-semibold mb-1 ${
              theme === 'light' ? 'text-gray-700' : 'text-gray-200'
            }`}
          >
            Day
          </label>
          <select
            value={day}
            onChange={(e) => setDay(Number(e.target.value))}
            className={`w-full p-2 border rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
              theme === 'light'
                ? 'border-gray-300 bg-white text-gray-900'
                : 'border-gray-600 bg-gray-600 text-gray-100'
            }`}
          >
            {Array.from(
              { length: new Date(year, month, 0).getDate() },
              (_, i) => i + 1
            ).map((d) => (
              <option key={d} value={d}>
                {d}
              </option>
            ))}
          </select>
        </div>

        {/* Theme Toggle */}
        <div className="mt-3 flex justify-end">
          <button
            onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
            className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
              theme === 'light'
                ? 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                : 'bg-gray-600 text-gray-200 hover:bg-gray-500'
            }`}
          >
            {theme === 'light' ? 'Dark Mode' : 'Light Mode'}
          </button>
        </div>
      </div>

      {/* Employee List */}
      <div className="w-full max-w-[340px]">
        {rosterLoading || employeesLoading ? (
          <div
            className={`text-center animate-pulse ${
              theme === 'light' ? 'text-gray-600' : 'text-gray-400'
            }`}
          >
            Loading employees...
          </div>
        ) : employeesForDay.length > 0 ? (
          <div className="space-y-4">
            {employeesForDay.map((employee, index) => (
              <div
                key={index}
                className={`rounded-xl shadow-md p-4 ${
                  theme === 'light' ? 'bg-white' : 'bg-gray-700'
                }`}
              >
                <div className="flex justify-between items-center">
                  <div>
                    <p
                      className={`text-sm font-semibold ${
                        theme === 'light' ? 'text-gray-900' : 'text-gray-100'
                      }`}
                    >
                      {employee.name}
                    </p>
                    <p
                      className={`text-xs ${
                        theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                      }`}
                    >
                      {employee.position}
                    </p>
                  </div>
                  <div
                    className={`text-sm font-medium ${
                      theme === 'light' ? 'text-gray-900' : 'text-gray-100'
                    }`}
                  >
                    {employee.shift || 'No Shift'}
                  </div>
                </div>
                {employee.phone && (
                  <p className="mt-2 text-xs">
                    <a
                      href={`tel:${employee.phone}`}
                      className={`underline hover:text-blue-500 transition-colors ${
                        theme === 'light' ? 'text-blue-600' : 'text-blue-400'
                      }`}
                    >
                      Phone: {employee.phone}
                    </a>
                  </p>
                )}
                {employee.email && (
                  <p className="mt-1 text-xs">
                    <a
                      href={`mailto:${employee.email}`}
                      className={`underline hover:text-blue-500 transition-colors ${
                        theme === 'light' ? 'text-blue-600' : 'text-blue-400'
                      }`}
                    >
                      Email: {employee.email}
                    </a>
                  </p>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div
            className={`text-center ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}
          >
            No employees found for this day.
          </div>
        )}
      </div>
    </div>
  );
};

export default EmployeeSearch;