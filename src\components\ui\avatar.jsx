import React, { forwardRef } from "react";
import { cn } from "../../lib/utils";

const Avatar = forwardRef(({ className, children, fallback, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full", className)}
    {...props}
  >
    {children}
    {fallback && (
      <div className="absolute inset-0 flex items-center justify-center bg-muted">
        {fallback}
      </div>
    )}
  </div>
));

const AvatarImage = forwardRef(({ className, ...props }, ref) => (
  <img
    ref={ref}
    className={cn("aspect-square h-full w-full object-cover", className)}
    {...props}
    onError={(e) => {
      console.error("Error loading avatar image", e);
      e.target.style.display = "none"; // Hide broken image
    }}
  />
));

const AvatarFallback = forwardRef(({ className, ...props }, ref) => (
  <span
    ref={ref}
    className={cn("absolute inset-0 flex items-center justify-center bg-muted text-xs font-medium text-muted-foreground", className)}
    {...props}
  />
));

export { Avatar, AvatarImage, AvatarFallback };