//src/hooks/useFetchAvailableRosters.js
import { useState, useEffect, useCallback } from 'react';
import { getDatabase, ref, onValue } from 'firebase/database';

const useFetchRosterRange = (year, month) => {
  const [availableRosters, setAvailableRosters] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchRosterRange = useCallback(() => {
    setLoading(true);
    setError(null);

    const db = getDatabase();

    // Calculate previous, current, and next months with corresponding years
    const monthsToFetch = [];

    // Previous month
    let prevMonth = month - 1;
    let prevYear = year;
    if (prevMonth === 0) {
      prevMonth = 12;
      prevYear = year - 1;
    }
    monthsToFetch.push({ year: prevYear, month: prevMonth });

    // Current month
    monthsToFetch.push({ year, month });

    // Next month
    let nextMonth = month + 1;
    let nextYear = year;
    if (nextMonth === 13) {
      nextMonth = 1;
      nextYear = year + 1;
    }
    monthsToFetch.push({ year: nextYear, month: nextMonth });

    // Fetch rosters for each month/year combination
    const fetchPromises = monthsToFetch.map(({ year: fetchYear, month: fetchMonth }) => {
      return new Promise((resolve) => {
        const rostersRef = ref(db, `rosters/${fetchYear}/${fetchMonth}`);
        onValue(
          rostersRef,
          (snapshot) => {
            resolve({
              year: fetchYear,
              month: fetchMonth,
              exists: snapshot.exists(),
            });
          },
          (err) => {
            resolve({
              year: fetchYear,
              month: fetchMonth,
              exists: false,
              error: err,
            });
          },
          { onlyOnce: true }
        );
      });
    });

    // Wait for all fetches to complete
    Promise.all(fetchPromises)
      .then((results) => {
        const available = results
          .filter((result) => result.exists)
          .map((result) => ({ year: result.year, month: result.month }))
          .sort((a, b) => {
            if (a.year === b.year) return a.month - b.month;
            return a.year - b.year;
          });

        setAvailableRosters(available);

        // Check if there were any errors
        const errors = results.filter((result) => result.error);
        if (errors.length > 0) {
          setError(new Error('Some rosters could not be fetched'));
        } else {
          setError(null);
        }

        // If no rosters are available, set an error
        if (available.length === 0) {
          setError(new Error('No rosters available for the selected range'));
        }
      })
      .catch((err) => {
        setError(err);
        setAvailableRosters([]);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [year, month]);

  useEffect(() => {
    fetchRosterRange();
  }, [fetchRosterRange]);

  return { availableRosters, loading, error, refetch: fetchRosterRange };
};

export default useFetchRosterRange;