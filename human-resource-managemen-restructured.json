{"departments": {"dept_001": {"calendar_markers": {"holiday_markers": {"-OPpKc_3Tcvq_-eD04-0": {"color": "#4a5899", "createdAt": "2025-05-09T13:57:36.577Z", "endDate": {"day": 29, "month": 12, "year": 2026}, "name": "Christmas ", "recurring": true, "startDate": {"day": 25, "month": 12, "year": 2026}, "updatedAt": "2025-05-10T08:57:59.286Z"}, "-OPpLGcCjCz7YIEdUXr4": {"color": "#7d9e2c", "createdAt": "2025-05-09T14:00:24.628Z", "endDate": {"day": 10, "month": 6, "year": 2025}, "name": "EDI", "recurring": false, "startDate": {"day": 5, "month": 6, "year": 2025}, "updatedAt": "2025-05-09T14:00:24.628Z"}}, "special_day_markers": {"-OPpR2ZrRVV2C9OXTvQR": {"color": "#d08eea", "createdAt": "2025-05-09T14:25:39.924Z", "endDate": {"day": 29, "month": 3, "year": 2025}, "name": "<PERSON><PERSON>", "startDate": {"day": 1, "month": 3, "year": 2025}, "updatedAt": "2025-05-09T14:25:39.924Z"}}, "weekday_markers": {"-OPpJiHPcNAOfUj6Bple": {"color": "#d35555", "createdAt": "2025-05-09T13:53:37.814Z", "name": "Friday", "updatedAt": "2025-05-09T13:53:51.580Z", "weekday": "Friday"}, "-OPqIZW5tjufEsTklqxu": {"color": "#d35555", "createdAt": "2025-05-09T18:28:12.224Z", "name": "Saturday", "updatedAt": "2025-05-10T10:41:56.803Z", "weekday": "Saturday"}}}, "config": {"maxAdmins": 2}, "contactInfo": {"AdminLogistics": {"phone": "(+973) 33 9815 62"}, "DutyPhone1": {"phone": "(+973) 33 6464 21"}, "DutyPhone2": {"phone": "(+973) 33 6464 21"}, "DutyPhone3": {"phone": "(+973) 33 544211"}, "Manager": {"phone": "(+973) 33 6464 20"}, "new": {"phone": "+9627012812997"}}, "employees": {"T00003": {"createdAt": "2025-04-18T09:46:03.454Z", "email": "", "employeeId": "T00003", "name": "Employee1", "phone": "", "position": "LMM", "rowIndex": 1, "srcNumber": "T00003", "updatedAt": "2025-04-18T09:46:03.454Z"}, "T00004": {"createdAt": "2025-04-18T09:46:03.848Z", "email": "", "employeeId": "T00004", "name": "Employee2", "phone": "", "position": "A.LC", "rowIndex": 2, "srcNumber": "T00004", "updatedAt": "2025-04-18T09:46:03.848Z"}, "T00005": {"createdAt": "2025-04-18T09:46:04.241Z", "email": "", "employeeId": "T00005", "name": "Employee3", "phone": "", "position": "L.C", "rowIndex": 3, "srcNumber": "T00005", "updatedAt": "2025-04-18T09:46:04.241Z"}, "T00006": {"createdAt": "2025-04-18T09:46:04.634Z", "email": "", "employeeId": "T00006", "name": "Employee4", "phone": "", "position": "MTL", "rowIndex": 4, "srcNumber": "3806", "updatedAt": "2025-04-21T18:51:21.661Z"}, "T00007": {"createdAt": "2025-04-18T09:46:05.037Z", "email": "", "employeeId": "T00007", "name": "Employee5", "phone": "", "position": "MTL", "rowIndex": 5, "srcNumber": "3808", "updatedAt": "2025-04-18T09:46:05.037Z"}}, "rosters": {"2024": {"4": {"createdAt": "2024-04-01T00:00:00Z", "employeeShifts": {"T00003": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee1", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "LMM", "rowIndex": 1, "shifts": [null, "D", "D", "D", "O", "O", "D1", "D", "D", "D", "D", "O", "O", "X", "N2", "DT", "D2", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00003", "workDays": 21, "workTime": "5040:00"}, "T00004": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee2", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "A.LC", "rowIndex": 2, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "TR", "TR", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00004", "workDays": 22, "workTime": "208:00"}, "T00005": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee3", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "L.C", "rowIndex": 3, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "D", "D", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00005", "workDays": 22, "workTime": "216:00"}, "T00006": {"aircraft": "79:12", "leaveDays": 1, "name": "Employee4", "office": "118:48", "overtime": "0:00", "overtimeDays": 0, "position": "LMS", "rowIndex": 4, "shifts": [null, "D1", "D1", "O", "O", "D1", "X", "D1", "O", "D1", "O", "O", "O", "D1", "D", "O", "O", "D", "D", "D", "D1", "D1", "D1", "D1", "D1", "O", "O", "O", "D1", "D1", "D1"], "sickDays": 0, "srcNumber": "3806", "workDays": 18, "workTime": "180:00"}, "T00007": {"aircraft": "165:00", "leaveDays": 0, "name": "Employee5", "office": "55:00", "overtime": "0:00", "overtimeDays": 0, "position": "MTL", "rowIndex": 5, "shifts": [null, "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O"], "sickDays": 0, "srcNumber": "3808", "workDays": 20, "workTime": "224:00"}}, "month": 4, "updatedAt": "2025-04-25T08:21:17.510Z", "year": 2024}}}, "rostersDraft": {"2024": {"4": {"employeeShifts": {"T00003": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee1", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "LMM", "rowIndex": 1, "shifts": [null, "D", "D", "D", "O", "O", "D1", "D", "D", "D", "D", "O", "O", "X", "N2", "DT", "D2", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00003", "workDays": 21, "workTime": "5040:00"}, "T00004": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee2", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "A.LC", "rowIndex": 2, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "TR", "TR", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00004", "workDays": 22, "workTime": "208:00"}, "T00005": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee3", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "L.C", "rowIndex": 3, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "D", "D", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00005", "workDays": 22, "workTime": "216:00"}, "T00006": {"aircraft": "79:12", "leaveDays": 1, "name": "Employee4", "office": "118:48", "overtime": "0:00", "overtimeDays": 0, "position": "LMS", "rowIndex": 4, "shifts": [null, "D1", "D1", "O", "O", "D1", "X", "D1", "O", "D1", "O", "O", "O", "D1", "D", "O", "O", "D", "D", "D", "D1", "D1", "D1", "D1", "D1", "O", "O", "O", "D1", "D1", "D1"], "sickDays": 0, "srcNumber": "3806", "workDays": 18, "workTime": "180:00"}, "T00007": {"aircraft": "165:00", "leaveDays": 0, "name": "Employee5", "office": "55:00", "overtime": "0:00", "overtimeDays": 0, "position": "MTL", "rowIndex": 5, "shifts": [null, "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O"], "sickDays": 0, "srcNumber": "3808", "workDays": 20, "workTime": "224:00"}}}}}}, "dept_002": {"calendar_markers": {"holiday_markers": {"-OPpKc_3Tcvq_-eD04-0": {"color": "#4a5899", "createdAt": "2025-05-09T13:57:36.577Z", "endDate": {"day": 29, "month": 12, "year": 2026}, "name": "Christmas ", "recurring": true, "startDate": {"day": 25, "month": 12, "year": 2026}, "updatedAt": "2025-05-10T08:57:59.286Z"}}, "special_day_markers": {"-OPpR2ZrRVV2C9OXTvQR": {"color": "#d08eea", "createdAt": "2025-05-09T14:25:39.924Z", "endDate": {"day": 29, "month": 3, "year": 2025}, "name": "<PERSON><PERSON>", "startDate": {"day": 1, "month": 3, "year": 2025}, "updatedAt": "2025-05-09T14:25:39.924Z"}}, "weekday_markers": {"-OPpJiHPcNAOfUj6Bple": {"color": "#d35555", "createdAt": "2025-05-09T13:53:37.814Z", "name": "Friday", "updatedAt": "2025-05-09T13:53:51.580Z", "weekday": "Friday"}}}, "config": {"maxAdmins": 2}, "contactInfo": {"AdminLogistics": {"phone": "(+973) 33 9815 62"}, "DutyPhone1": {"phone": "(+973) 33 6464 21"}}, "employees": {"T00003": {"createdAt": "2025-04-18T09:46:03.454Z", "email": "", "employeeId": "T00003", "name": "Employee50", "phone": "", "position": "LMM", "rowIndex": 1, "srcNumber": "T00003", "updatedAt": "2025-04-18T09:46:03.454Z"}, "T00004": {"createdAt": "2025-04-18T09:46:03.848Z", "email": "", "employeeId": "T00004", "name": "Employee51", "phone": "", "position": "A.LC", "rowIndex": 2, "srcNumber": "T00004", "updatedAt": "2025-04-18T09:46:03.848Z"}, "T00005": {"createdAt": "2025-04-18T09:46:04.241Z", "email": "", "employeeId": "T00005", "name": "Employee52", "phone": "", "position": "L.C", "rowIndex": 3, "srcNumber": "T00005", "updatedAt": "2025-04-18T09:46:04.241Z"}, "T00006": {"createdAt": "2025-04-18T09:46:04.634Z", "email": "", "employeeId": "T00006", "name": "Employee53", "phone": "", "position": "MTL", "rowIndex": 4, "srcNumber": "3806", "updatedAt": "2025-04-21T18:51:21.661Z"}, "T00007": {"createdAt": "2025-04-18T09:46:05.037Z", "email": "", "employeeId": "T00007", "name": "Employee54", "phone": "", "position": "MTL", "rowIndex": 5, "srcNumber": "3808", "updatedAt": "2025-04-18T09:46:05.037Z"}}, "rosters": {"2024": {"4": {"createdAt": "2024-04-01T00:00:00Z", "employeeShifts": {"T00003": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee50", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "LMM", "rowIndex": 1, "shifts": [null, "D", "D", "D", "O", "O", "D1", "D", "D", "D", "D", "O", "O", "X", "N2", "DT", "D2", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00003", "workDays": 21, "workTime": "5040:00"}, "T00004": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee51", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "A.LC", "rowIndex": 2, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "TR", "TR", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00004", "workDays": 22, "workTime": "208:00"}, "T00005": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee52", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "L.C", "rowIndex": 3, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "D", "D", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00005", "workDays": 22, "workTime": "216:00"}, "T00006": {"aircraft": "79:12", "leaveDays": 1, "name": "Employee53", "office": "118:48", "overtime": "0:00", "overtimeDays": 0, "position": "LMS", "rowIndex": 4, "shifts": [null, "D1", "D1", "O", "O", "D1", "X", "D1", "O", "D1", "O", "O", "O", "D1", "D", "O", "O", "D", "D", "D", "D1", "D1", "D1", "D1", "D1", "O", "O", "O", "D1", "D1", "D1"], "sickDays": 0, "srcNumber": "3806", "workDays": 18, "workTime": "180:00"}, "T00007": {"aircraft": "165:00", "leaveDays": 0, "name": "Employee54", "office": "55:00", "overtime": "0:00", "overtimeDays": 0, "position": "MTL", "rowIndex": 5, "shifts": [null, "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O"], "sickDays": 0, "srcNumber": "3808", "workDays": 20, "workTime": "224:00"}}, "month": 4, "updatedAt": "2025-04-25T08:21:17.510Z", "year": 2024}}}, "rostersDraft": {"2024": {"4": {"employeeShifts": {"T00003": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee50", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "LMM", "rowIndex": 1, "shifts": [null, "D", "D", "D", "O", "O", "D1", "D", "D", "D", "D", "O", "O", "X", "N2", "DT", "D2", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00003", "workDays": 21, "workTime": "5040:00"}, "T00004": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee51", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "A.LC", "rowIndex": 2, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "TR", "TR", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00004", "workDays": 22, "workTime": "208:00"}, "T00005": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee52", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "L.C", "rowIndex": 3, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "D", "D", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00005", "workDays": 22, "workTime": "216:00"}, "T00006": {"aircraft": "79:12", "leaveDays": 1, "name": "Employee53", "office": "118:48", "overtime": "0:00", "overtimeDays": 0, "position": "LMS", "rowIndex": 4, "shifts": [null, "D1", "D1", "O", "O", "D1", "X", "D1", "O", "D1", "O", "O", "O", "D1", "D", "O", "O", "D", "D", "D", "D1", "D1", "D1", "D1", "D1", "O", "O", "O", "D1", "D1", "D1"], "sickDays": 0, "srcNumber": "3806", "workDays": 18, "workTime": "180:00"}, "T00007": {"aircraft": "165:00", "leaveDays": 0, "name": "Employee54", "office": "55:00", "overtime": "0:00", "overtimeDays": 0, "position": "MTL", "rowIndex": 5, "shifts": [null, "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O"], "sickDays": 0, "srcNumber": "3808", "workDays": 20, "workTime": "224:00"}}}}}}, "dept_003": {"calendar_markers": {"holiday_markers": {"-OPpKc_3Tcvq_-eD04-0": {"color": "#4a5899", "createdAt": "2025-05-09T13:57:36.577Z", "endDate": {"day": 29, "month": 12, "year": 2026}, "name": "Christmas ", "recurring": true, "startDate": {"day": 25, "month": 12, "year": 2026}, "updatedAt": "2025-05-10T08:57:59.286Z"}}, "special_day_markers": {"-OPpR2ZrRVV2C9OXTvQR": {"color": "#d08eea", "createdAt": "2025-05-09T14:25:39.924Z", "endDate": {"day": 29, "month": 3, "year": 2025}, "name": "<PERSON><PERSON>", "startDate": {"day": 1, "month": 3, "year": 2025}, "updatedAt": "2025-05-09T14:25:39.924Z"}}}, "config": {"maxAdmins": 2}, "contactInfo": {"AdminLogistics": {"phone": "(+973) 33 9815 62"}}, "employees": {"T00003": {"createdAt": "2025-04-18T09:46:03.454Z", "email": "", "employeeId": "T00003", "name": "Employee100", "phone": "", "position": "LMM", "rowIndex": 1, "srcNumber": "T00003", "updatedAt": "2025-04-18T09:46:03.454Z"}, "T00004": {"createdAt": "2025-04-18T09:46:03.848Z", "email": "", "employeeId": "T00004", "name": "Employee101", "phone": "", "position": "A.LC", "rowIndex": 2, "srcNumber": "T00004", "updatedAt": "2025-04-18T09:46:03.848Z"}, "T00005": {"createdAt": "2025-04-18T09:46:04.241Z", "email": "", "employeeId": "T00005", "name": "Employee102", "phone": "", "position": "L.C", "rowIndex": 3, "srcNumber": "T00005", "updatedAt": "2025-04-18T09:46:04.241Z"}, "T00006": {"createdAt": "2025-04-18T09:46:04.634Z", "email": "", "employeeId": "T00006", "name": "Employee103", "phone": "", "position": "MTL", "rowIndex": 4, "srcNumber": "3806", "updatedAt": "2025-04-21T18:51:21.661Z"}, "T00007": {"createdAt": "2025-04-18T09:46:05.037Z", "email": "", "employeeId": "T00007", "name": "Employee104", "phone": "", "position": "MTL", "rowIndex": 5, "srcNumber": "3808", "updatedAt": "2025-04-18T09:46:05.037Z"}}, "rosters": {"2024": {"4": {"createdAt": "2024-04-01T00:00:00Z", "employeeShifts": {"T00003": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee100", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "LMM", "rowIndex": 1, "shifts": [null, "D", "D", "D", "O", "O", "D1", "D", "D", "D", "D", "O", "O", "X", "N2", "DT", "D2", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00003", "workDays": 21, "workTime": "5040:00"}, "T00004": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee101", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "A.LC", "rowIndex": 2, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "TR", "TR", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00004", "workDays": 22, "workTime": "208:00"}, "T00005": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee102", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "L.C", "rowIndex": 3, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "D", "D", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00005", "workDays": 22, "workTime": "216:00"}, "T00006": {"aircraft": "79:12", "leaveDays": 1, "name": "Employee103", "office": "118:48", "overtime": "0:00", "overtimeDays": 0, "position": "LMS", "rowIndex": 4, "shifts": [null, "D1", "D1", "O", "O", "D1", "X", "D1", "O", "D1", "O", "O", "O", "D1", "D", "O", "O", "D", "D", "D", "D1", "D1", "D1", "D1", "D1", "O", "O", "O", "D1", "D1", "D1"], "sickDays": 0, "srcNumber": "3806", "workDays": 18, "workTime": "180:00"}, "T00007": {"aircraft": "165:00", "leaveDays": 0, "name": "Employee104", "office": "55:00", "overtime": "0:00", "overtimeDays": 0, "position": "MTL", "rowIndex": 5, "shifts": [null, "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O"], "sickDays": 0, "srcNumber": "3808", "workDays": 20, "workTime": "224:00"}}, "month": 4, "updatedAt": "2025-04-25T08:21:17.510Z", "year": 2024}}}, "rostersDraft": {"2024": {"4": {"employeeShifts": {"T00003": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee100", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "LMM", "rowIndex": 1, "shifts": [null, "D", "D", "D", "O", "O", "D1", "D", "D", "D", "D", "O", "O", "X", "N2", "DT", "D2", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00003", "workDays": 21, "workTime": "5040:00"}, "T00004": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee101", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "A.LC", "rowIndex": 2, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "TR", "TR", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00004", "workDays": 22, "workTime": "208:00"}, "T00005": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee102", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "L.C", "rowIndex": 3, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "D", "D", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00005", "workDays": 22, "workTime": "216:00"}, "T00006": {"aircraft": "79:12", "leaveDays": 1, "name": "Employee103", "office": "118:48", "overtime": "0:00", "overtimeDays": 0, "position": "LMS", "rowIndex": 4, "shifts": [null, "D1", "D1", "O", "O", "D1", "X", "D1", "O", "D1", "O", "O", "O", "D1", "D", "O", "O", "D", "D", "D", "D1", "D1", "D1", "D1", "D1", "O", "O", "O", "D1", "D1", "D1"], "sickDays": 0, "srcNumber": "3806", "workDays": 18, "workTime": "180:00"}, "T00007": {"aircraft": "165:00", "leaveDays": 0, "name": "Employee104", "office": "55:00", "overtime": "0:00", "overtimeDays": 0, "position": "MTL", "rowIndex": 5, "shifts": [null, "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O"], "sickDays": 0, "srcNumber": "3808", "workDays": 20, "workTime": "224:00"}}}}}}, "dept_004": {"calendar_markers": {"holiday_markers": {"-OPpKc_3Tcvq_-eD04-0": {"color": "#4a5899", "createdAt": "2025-05-09T13:57:36.577Z", "endDate": {"day": 29, "month": 12, "year": 2026}, "name": "Christmas ", "recurring": true, "startDate": {"day": 25, "month": 12, "year": 2026}, "updatedAt": "2025-05-10T08:57:59.286Z"}}}, "config": {"maxAdmins": 2}, "contactInfo": {"AdminLogistics": {"phone": "(+973) 33 9815 62"}}, "employees": {"T00003": {"createdAt": "2025-04-18T09:46:03.454Z", "email": "", "employeeId": "T00003", "name": "Employee150", "phone": "", "position": "LMM", "rowIndex": 1, "srcNumber": "T00003", "updatedAt": "2025-04-18T09:46:03.454Z"}, "T00004": {"createdAt": "2025-04-18T09:46:03.848Z", "email": "", "employeeId": "T00004", "name": "Employee151", "phone": "", "position": "A.LC", "rowIndex": 2, "srcNumber": "T00004", "updatedAt": "2025-04-18T09:46:03.848Z"}, "T00005": {"createdAt": "2025-04-18T09:46:04.241Z", "email": "", "employeeId": "T00005", "name": "Employee152", "phone": "", "position": "L.C", "rowIndex": 3, "srcNumber": "T00005", "updatedAt": "2025-04-18T09:46:04.241Z"}, "T00006": {"createdAt": "2025-04-18T09:46:04.634Z", "email": "", "employeeId": "T00006", "name": "Employee153", "phone": "", "position": "MTL", "rowIndex": 4, "srcNumber": "3806", "updatedAt": "2025-04-21T18:51:21.661Z"}, "T00007": {"createdAt": "2025-04-18T09:46:05.037Z", "email": "", "employeeId": "T00007", "name": "Employee154", "phone": "", "position": "MTL", "rowIndex": 5, "srcNumber": "3808", "updatedAt": "2025-04-18T09:46:05.037Z"}}, "rosters": {"2024": {"4": {"createdAt": "2024-04-01T00:00:00Z", "employeeShifts": {"T00003": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee150", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "LMM", "rowIndex": 1, "shifts": [null, "D", "D", "D", "O", "O", "D1", "D", "D", "D", "D", "O", "O", "X", "N2", "DT", "D2", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00003", "workDays": 21, "workTime": "5040:00"}, "T00004": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee151", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "A.LC", "rowIndex": 2, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "TR", "TR", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00004", "workDays": 22, "workTime": "208:00"}, "T00005": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee152", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "L.C", "rowIndex": 3, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "D", "D", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00005", "workDays": 22, "workTime": "216:00"}, "T00006": {"aircraft": "79:12", "leaveDays": 1, "name": "Employee153", "office": "118:48", "overtime": "0:00", "overtimeDays": 0, "position": "LMS", "rowIndex": 4, "shifts": [null, "D1", "D1", "O", "O", "D1", "X", "D1", "O", "D1", "O", "O", "O", "D1", "D", "O", "O", "D", "D", "D", "D1", "D1", "D1", "D1", "D1", "O", "O", "O", "D1", "D1", "D1"], "sickDays": 0, "srcNumber": "3806", "workDays": 18, "workTime": "180:00"}, "T00007": {"aircraft": "165:00", "leaveDays": 0, "name": "Employee154", "office": "55:00", "overtime": "0:00", "overtimeDays": 0, "position": "MTL", "rowIndex": 5, "shifts": [null, "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O"], "sickDays": 0, "srcNumber": "3808", "workDays": 20, "workTime": "224:00"}}, "month": 4, "updatedAt": "2025-04-25T08:21:17.510Z", "year": 2024}}}, "rostersDraft": {"2024": {"4": {"employeeShifts": {"T00003": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee150", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "LMM", "rowIndex": 1, "shifts": [null, "D", "D", "D", "O", "O", "D1", "D", "D", "D", "D", "O", "O", "X", "N2", "DT", "D2", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00003", "workDays": 21, "workTime": "5040:00"}, "T00004": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee151", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "A.LC", "rowIndex": 2, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "TR", "TR", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00004", "workDays": 22, "workTime": "208:00"}, "T00005": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee152", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "L.C", "rowIndex": 3, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "D", "D", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00005", "workDays": 22, "workTime": "216:00"}, "T00006": {"aircraft": "79:12", "leaveDays": 1, "name": "Employee153", "office": "118:48", "overtime": "0:00", "overtimeDays": 0, "position": "LMS", "rowIndex": 4, "shifts": [null, "D1", "D1", "O", "O", "D1", "X", "D1", "O", "D1", "O", "O", "O", "D1", "D", "O", "O", "D", "D", "D", "D1", "D1", "D1", "D1", "D1", "O", "O", "O", "D1", "D1", "D1"], "sickDays": 0, "srcNumber": "3806", "workDays": 18, "workTime": "180:00"}, "T00007": {"aircraft": "165:00", "leaveDays": 0, "name": "Employee154", "office": "55:00", "overtime": "0:00", "overtimeDays": 0, "position": "MTL", "rowIndex": 5, "shifts": [null, "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O"], "sickDays": 0, "srcNumber": "3808", "workDays": 20, "workTime": "224:00"}}}}}}, "dept_005": {"calendar_markers": {"holiday_markers": {"-OPpKc_3Tcvq_-eD04-0": {"color": "#4a5899", "createdAt": "2025-05-09T13:57:36.577Z", "endDate": {"day": 29, "month": 12, "year": 2026}, "name": "Christmas ", "recurring": true, "startDate": {"day": 25, "month": 12, "year": 2026}, "updatedAt": "2025-05-10T08:57:59.286Z"}}}, "config": {"maxAdmins": 2}, "contactInfo": {"AdminLogistics": {"phone": "(+973) 33 9815 62"}}, "employees": {"T00003": {"createdAt": "2025-04-18T09:46:03.454Z", "email": "", "employeeId": "T00003", "name": "Employee200", "phone": "", "position": "LMM", "rowIndex": 1, "srcNumber": "T00003", "updatedAt": "2025-04-18T09:46:03.454Z"}, "T00004": {"createdAt": "2025-04-18T09:46:03.848Z", "email": "", "employeeId": "T00004", "name": "Employee201", "phone": "", "position": "A.LC", "rowIndex": 2, "srcNumber": "T00004", "updatedAt": "2025-04-18T09:46:03.848Z"}, "T00005": {"createdAt": "2025-04-18T09:46:04.241Z", "email": "", "employeeId": "T00005", "name": "Employee202", "phone": "", "position": "L.C", "rowIndex": 3, "srcNumber": "T00005", "updatedAt": "2025-04-18T09:46:04.241Z"}, "T00006": {"createdAt": "2025-04-18T09:46:04.634Z", "email": "", "employeeId": "T00006", "name": "Employee203", "phone": "", "position": "MTL", "rowIndex": 4, "srcNumber": "3806", "updatedAt": "2025-04-21T18:51:21.661Z"}, "T00007": {"createdAt": "2025-04-18T09:46:05.037Z", "email": "", "employeeId": "T00007", "name": "Employee204", "phone": "", "position": "MTL", "rowIndex": 5, "srcNumber": "3808", "updatedAt": "2025-04-18T09:46:05.037Z"}}, "rosters": {"2024": {"4": {"createdAt": "2024-04-01T00:00:00Z", "employeeShifts": {"T00003": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee200", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "LMM", "rowIndex": 1, "shifts": [null, "D", "D", "D", "O", "O", "D1", "D", "D", "D", "D", "O", "O", "X", "N2", "DT", "D2", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00003", "workDays": 21, "workTime": "5040:00"}, "T00004": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee201", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "A.LC", "rowIndex": 2, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "TR", "TR", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00004", "workDays": 22, "workTime": "208:00"}, "T00005": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee202", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "L.C", "rowIndex": 3, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "D", "D", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00005", "workDays": 22, "workTime": "216:00"}, "T00006": {"aircraft": "79:12", "leaveDays": 1, "name": "Employee203", "office": "118:48", "overtime": "0:00", "overtimeDays": 0, "position": "LMS", "rowIndex": 4, "shifts": [null, "D1", "D1", "O", "O", "D1", "X", "D1", "O", "D1", "O", "O", "O", "D1", "D", "O", "O", "D", "D", "D", "D1", "D1", "D1", "D1", "D1", "O", "O", "O", "D1", "D1", "D1"], "sickDays": 0, "srcNumber": "3806", "workDays": 18, "workTime": "180:00"}, "T00007": {"aircraft": "165:00", "leaveDays": 0, "name": "Employee204", "office": "55:00", "overtime": "0:00", "overtimeDays": 0, "position": "MTL", "rowIndex": 5, "shifts": [null, "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O"], "sickDays": 0, "srcNumber": "3808", "workDays": 20, "workTime": "224:00"}}, "month": 4, "updatedAt": "2025-04-25T08:21:17.510Z", "year": 2024}}}, "rostersDraft": {"2024": {"4": {"employeeShifts": {"T00003": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee200", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "LMM", "rowIndex": 1, "shifts": [null, "D", "D", "D", "O", "O", "D1", "D", "D", "D", "D", "O", "O", "X", "N2", "DT", "D2", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00003", "workDays": 21, "workTime": "5040:00"}, "T00004": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee201", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "A.LC", "rowIndex": 2, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "TR", "TR", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00004", "workDays": 22, "workTime": "208:00"}, "T00005": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee202", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "L.C", "rowIndex": 3, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "D", "D", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00005", "workDays": 22, "workTime": "216:00"}, "T00006": {"aircraft": "79:12", "leaveDays": 1, "name": "Employee203", "office": "118:48", "overtime": "0:00", "overtimeDays": 0, "position": "LMS", "rowIndex": 4, "shifts": [null, "D1", "D1", "O", "O", "D1", "X", "D1", "O", "D1", "O", "O", "O", "D1", "D", "O", "O", "D", "D", "D", "D1", "D1", "D1", "D1", "D1", "O", "O", "O", "D1", "D1", "D1"], "sickDays": 0, "srcNumber": "3806", "workDays": 18, "workTime": "180:00"}, "T00007": {"aircraft": "165:00", "leaveDays": 0, "name": "Employee204", "office": "55:00", "overtime": "0:00", "overtimeDays": 0, "position": "MTL", "rowIndex": 5, "shifts": [null, "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O"], "sickDays": 0, "srcNumber": "3808", "workDays": 20, "workTime": "224:00"}}}}}}, "dept_006": {"calendar_markers": {"holiday_markers": {"-OPpKc_3Tcvq_-eD04-0": {"color": "#4a5899", "createdAt": "2025-05-09T13:57:36.577Z", "endDate": {"day": 29, "month": 12, "year": 2026}, "name": "Christmas ", "recurring": true, "startDate": {"day": 25, "month": 12, "year": 2026}, "updatedAt": "2025-05-10T08:57:59.286Z"}}}, "config": {"maxAdmins": 2}, "contactInfo": {"AdminLogistics": {"phone": "(+973) 33 9815 62"}}, "employees": {"T00003": {"createdAt": "2025-04-18T09:46:03.454Z", "email": "", "employeeId": "T00003", "name": "Employee250", "phone": "", "position": "LMM", "rowIndex": 1, "srcNumber": "T00003", "updatedAt": "2025-04-18T09:46:03.454Z"}, "T00004": {"createdAt": "2025-04-18T09:46:03.848Z", "email": "", "employeeId": "T00004", "name": "Employee251", "phone": "", "position": "A.LC", "rowIndex": 2, "srcNumber": "T00004", "updatedAt": "2025-04-18T09:46:03.848Z"}, "T00005": {"createdAt": "2025-04-18T09:46:04.241Z", "email": "", "employeeId": "T00005", "name": "Employee252", "phone": "", "position": "L.C", "rowIndex": 3, "srcNumber": "T00005", "updatedAt": "2025-04-18T09:46:04.241Z"}, "T00006": {"createdAt": "2025-04-18T09:46:04.634Z", "email": "", "employeeId": "T00006", "name": "Employee253", "phone": "", "position": "MTL", "rowIndex": 4, "srcNumber": "3806", "updatedAt": "2025-04-21T18:51:21.661Z"}, "T00007": {"createdAt": "2025-04-18T09:46:05.037Z", "email": "", "employeeId": "T00007", "name": "Employee254", "phone": "", "position": "MTL", "rowIndex": 5, "srcNumber": "3808", "updatedAt": "2025-04-18T09:46:05.037Z"}}, "rosters": {"2024": {"4": {"createdAt": "2024-04-01T00:00:00Z", "employeeShifts": {"T00003": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee250", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "LMM", "rowIndex": 1, "shifts": [null, "D", "D", "D", "O", "O", "D1", "D", "D", "D", "D", "O", "O", "X", "N2", "DT", "D2", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00003", "workDays": 21, "workTime": "5040:00"}, "T00004": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee251", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "A.LC", "rowIndex": 2, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "TR", "TR", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00004", "workDays": 22, "workTime": "208:00"}, "T00005": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee252", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "L.C", "rowIndex": 3, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "D", "D", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00005", "workDays": 22, "workTime": "216:00"}, "T00006": {"aircraft": "79:12", "leaveDays": 1, "name": "Employee253", "office": "118:48", "overtime": "0:00", "overtimeDays": 0, "position": "LMS", "rowIndex": 4, "shifts": [null, "D1", "D1", "O", "O", "D1", "X", "D1", "O", "D1", "O", "O", "O", "D1", "D", "O", "O", "D", "D", "D", "D1", "D1", "D1", "D1", "D1", "O", "O", "O", "D1", "D1", "D1"], "sickDays": 0, "srcNumber": "3806", "workDays": 18, "workTime": "180:00"}, "T00007": {"aircraft": "165:00", "leaveDays": 0, "name": "Employee254", "office": "55:00", "overtime": "0:00", "overtimeDays": 0, "position": "MTL", "rowIndex": 5, "shifts": [null, "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O"], "sickDays": 0, "srcNumber": "3808", "workDays": 20, "workTime": "224:00"}}, "month": 4, "updatedAt": "2025-04-25T08:21:17.510Z", "year": 2024}}}, "rostersDraft": {"2024": {"4": {"employeeShifts": {"T00003": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee250", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "LMM", "rowIndex": 1, "shifts": [null, "D", "D", "D", "O", "O", "D1", "D", "D", "D", "D", "O", "O", "X", "N2", "DT", "D2", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00003", "workDays": 21, "workTime": "5040:00"}, "T00004": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee251", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "A.LC", "rowIndex": 2, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "TR", "TR", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00004", "workDays": 22, "workTime": "208:00"}, "T00005": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee252", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "L.C", "rowIndex": 3, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "D", "D", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00005", "workDays": 22, "workTime": "216:00"}, "T00006": {"aircraft": "79:12", "leaveDays": 1, "name": "Employee253", "office": "118:48", "overtime": "0:00", "overtimeDays": 0, "position": "LMS", "rowIndex": 4, "shifts": [null, "D1", "D1", "O", "O", "D1", "X", "D1", "O", "D1", "O", "O", "O", "D1", "D", "O", "O", "D", "D", "D", "D1", "D1", "D1", "D1", "D1", "O", "O", "O", "D1", "D1", "D1"], "sickDays": 0, "srcNumber": "3806", "workDays": 18, "workTime": "180:00"}, "T00007": {"aircraft": "165:00", "leaveDays": 0, "name": "Employee254", "office": "55:00", "overtime": "0:00", "overtimeDays": 0, "position": "MTL", "rowIndex": 5, "shifts": [null, "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O"], "sickDays": 0, "srcNumber": "3808", "workDays": 20, "workTime": "224:00"}}}}}}, "dept_007": {"calendar_markers": {"holiday_markers": {"-OPpKc_3Tcvq_-eD04-0": {"color": "#4a5899", "createdAt": "2025-05-09T13:57:36.577Z", "endDate": {"day": 29, "month": 12, "year": 2026}, "name": "Christmas ", "recurring": true, "startDate": {"day": 25, "month": 12, "year": 2026}, "updatedAt": "2025-05-10T08:57:59.286Z"}}}, "config": {"maxAdmins": 2}, "contactInfo": {"AdminLogistics": {"phone": "(+973) 33 9815 62"}}, "employees": {"T00003": {"createdAt": "2025-04-18T09:46:03.454Z", "email": "", "employeeId": "T00003", "name": "Employee300", "phone": "", "position": "LMM", "rowIndex": 1, "srcNumber": "T00003", "updatedAt": "2025-04-18T09:46:03.454Z"}, "T00004": {"createdAt": "2025-04-18T09:46:03.848Z", "email": "", "employeeId": "T00004", "name": "Employee301", "phone": "", "position": "A.LC", "rowIndex": 2, "srcNumber": "T00004", "updatedAt": "2025-04-18T09:46:03.848Z"}, "T00005": {"createdAt": "2025-04-18T09:46:04.241Z", "email": "", "employeeId": "T00005", "name": "Employee302", "phone": "", "position": "L.C", "rowIndex": 3, "srcNumber": "T00005", "updatedAt": "2025-04-18T09:46:04.241Z"}, "T00006": {"createdAt": "2025-04-18T09:46:04.634Z", "email": "", "employeeId": "T00006", "name": "Employee303", "phone": "", "position": "MTL", "rowIndex": 4, "srcNumber": "3806", "updatedAt": "2025-04-21T18:51:21.661Z"}, "T00007": {"createdAt": "2025-04-18T09:46:05.037Z", "email": "", "employeeId": "T00007", "name": "Employee304", "phone": "", "position": "MTL", "rowIndex": 5, "srcNumber": "3808", "updatedAt": "2025-04-18T09:46:05.037Z"}}, "rosters": {"2024": {"4": {"createdAt": "2024-04-01T00:00:00Z", "employeeShifts": {"T00003": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee300", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "LMM", "rowIndex": 1, "shifts": [null, "D", "D", "D", "O", "O", "D1", "D", "D", "D", "D", "O", "O", "X", "N2", "DT", "D2", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00003", "workDays": 21, "workTime": "5040:00"}, "T00004": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee301", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "A.LC", "rowIndex": 2, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "TR", "TR", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00004", "workDays": 22, "workTime": "208:00"}, "T00005": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee302", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "L.C", "rowIndex": 3, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "D", "D", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00005", "workDays": 22, "workTime": "216:00"}, "T00006": {"aircraft": "79:12", "leaveDays": 1, "name": "Employee303", "office": "118:48", "overtime": "0:00", "overtimeDays": 0, "position": "LMS", "rowIndex": 4, "shifts": [null, "D1", "D1", "O", "O", "D1", "X", "D1", "O", "D1", "O", "O", "O", "D1", "D", "O", "O", "D", "D", "D", "D1", "D1", "D1", "D1", "D1", "O", "O", "O", "D1", "D1", "D1"], "sickDays": 0, "srcNumber": "3806", "workDays": 18, "workTime": "180:00"}, "T00007": {"aircraft": "165:00", "leaveDays": 0, "name": "Employee304", "office": "55:00", "overtime": "0:00", "overtimeDays": 0, "position": "MTL", "rowIndex": 5, "shifts": [null, "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O"], "sickDays": 0, "srcNumber": "3808", "workDays": 20, "workTime": "224:00"}}, "month": 4, "updatedAt": "2025-04-25T08:21:17.510Z", "year": 2024}}}, "rostersDraft": {"2024": {"4": {"employeeShifts": {"T00003": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee300", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "LMM", "rowIndex": 1, "shifts": [null, "D", "D", "D", "O", "O", "D1", "D", "D", "D", "D", "O", "O", "X", "N2", "DT", "D2", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00003", "workDays": 21, "workTime": "5040:00"}, "T00004": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee301", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "A.LC", "rowIndex": 2, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "TR", "TR", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00004", "workDays": 22, "workTime": "208:00"}, "T00005": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee302", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "L.C", "rowIndex": 3, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "D", "D", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00005", "workDays": 22, "workTime": "216:00"}, "T00006": {"aircraft": "79:12", "leaveDays": 1, "name": "Employee303", "office": "118:48", "overtime": "0:00", "overtimeDays": 0, "position": "LMS", "rowIndex": 4, "shifts": [null, "D1", "D1", "O", "O", "D1", "X", "D1", "O", "D1", "O", "O", "O", "D1", "D", "O", "O", "D", "D", "D", "D1", "D1", "D1", "D1", "D1", "O", "O", "O", "D1", "D1", "D1"], "sickDays": 0, "srcNumber": "3806", "workDays": 18, "workTime": "180:00"}, "T00007": {"aircraft": "165:00", "leaveDays": 0, "name": "Employee304", "office": "55:00", "overtime": "0:00", "overtimeDays": 0, "position": "MTL", "rowIndex": 5, "shifts": [null, "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O"], "sickDays": 0, "srcNumber": "3808", "workDays": 20, "workTime": "224:00"}}}}}}, "dept_008": {"calendar_markers": {"holiday_markers": {"-OPpKc_3Tcvq_-eD04-0": {"color": "#4a5899", "createdAt": "2025-05-09T13:57:36.577Z", "endDate": {"day": 29, "month": 12, "year": 2026}, "name": "Christmas ", "recurring": true, "startDate": {"day": 25, "month": 12, "year": 2026}, "updatedAt": "2025-05-10T08:57:59.286Z"}}}, "config": {"maxAdmins": 2}, "contactInfo": {"AdminLogistics": {"phone": "(+973) 33 9815 62"}}, "employees": {"T00003": {"createdAt": "2025-04-18T09:46:03.454Z", "email": "", "employeeId": "T00003", "name": "Employee350", "phone": "", "position": "LMM", "rowIndex": 1, "srcNumber": "T00003", "updatedAt": "2025-04-18T09:46:03.454Z"}, "T00004": {"createdAt": "2025-04-18T09:46:03.848Z", "email": "", "employeeId": "T00004", "name": "Employee351", "phone": "", "position": "A.LC", "rowIndex": 2, "srcNumber": "T00004", "updatedAt": "2025-04-18T09:46:03.848Z"}, "T00005": {"createdAt": "2025-04-18T09:46:04.241Z", "email": "", "employeeId": "T00005", "name": "Employee352", "phone": "", "position": "L.C", "rowIndex": 3, "srcNumber": "T00005", "updatedAt": "2025-04-18T09:46:04.241Z"}, "T00006": {"createdAt": "2025-04-18T09:46:04.634Z", "email": "", "employeeId": "T00006", "name": "Employee353", "phone": "", "position": "MTL", "rowIndex": 4, "srcNumber": "3806", "updatedAt": "2025-04-21T18:51:21.661Z"}, "T00007": {"createdAt": "2025-04-18T09:46:05.037Z", "email": "", "employeeId": "T00007", "name": "Employee354", "phone": "", "position": "MTL", "rowIndex": 5, "srcNumber": "3808", "updatedAt": "2025-04-18T09:46:05.037Z"}}, "rosters": {"2024": {"4": {"createdAt": "2024-04-01T00:00:00Z", "employeeShifts": {"T00003": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee350", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "LMM", "rowIndex": 1, "shifts": [null, "D", "D", "D", "O", "O", "D1", "D", "D", "D", "D", "O", "O", "X", "N2", "DT", "D2", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00003", "workDays": 21, "workTime": "5040:00"}, "T00004": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee351", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "A.LC", "rowIndex": 2, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "TR", "TR", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00004", "workDays": 22, "workTime": "208:00"}, "T00005": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee352", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "L.C", "rowIndex": 3, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "D", "D", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00005", "workDays": 22, "workTime": "216:00"}, "T00006": {"aircraft": "79:12", "leaveDays": 1, "name": "Employee353", "office": "118:48", "overtime": "0:00", "overtimeDays": 0, "position": "LMS", "rowIndex": 4, "shifts": [null, "D1", "D1", "O", "O", "D1", "X", "D1", "O", "D1", "O", "O", "O", "D1", "D", "O", "O", "D", "D", "D", "D1", "D1", "D1", "D1", "D1", "O", "O", "O", "D1", "D1", "D1"], "sickDays": 0, "srcNumber": "3806", "workDays": 18, "workTime": "180:00"}, "T00007": {"aircraft": "165:00", "leaveDays": 0, "name": "Employee354", "office": "55:00", "overtime": "0:00", "overtimeDays": 0, "position": "MTL", "rowIndex": 5, "shifts": [null, "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O"], "sickDays": 0, "srcNumber": "3808", "workDays": 20, "workTime": "224:00"}}, "month": 4, "updatedAt": "2025-04-25T08:21:17.510Z", "year": 2024}}}, "rostersDraft": {"2024": {"4": {"employeeShifts": {"T00003": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee350", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "LMM", "rowIndex": 1, "shifts": [null, "D", "D", "D", "O", "O", "D1", "D", "D", "D", "D", "O", "O", "X", "N2", "DT", "D2", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00003", "workDays": 21, "workTime": "5040:00"}, "T00004": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee351", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "A.LC", "rowIndex": 2, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "TR", "TR", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00004", "workDays": 22, "workTime": "208:00"}, "T00005": {"aircraft": "0:00", "leaveDays": 0, "name": "Employee352", "office": "0:00", "overtime": "0:00", "overtimeDays": 0, "position": "L.C", "rowIndex": 3, "shifts": [null, "D", "D", "D", "O", "O", "D", "D", "D", "D", "TR", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D", "D", "O", "O", "D", "D", "D", "D"], "sickDays": 0, "srcNumber": "T00005", "workDays": 22, "workTime": "216:00"}, "T00006": {"aircraft": "79:12", "leaveDays": 1, "name": "Employee353", "office": "118:48", "overtime": "0:00", "overtimeDays": 0, "position": "LMS", "rowIndex": 4, "shifts": [null, "D1", "D1", "O", "O", "D1", "X", "D1", "O", "D1", "O", "O", "O", "D1", "D", "O", "O", "D", "D", "D", "D1", "D1", "D1", "D1", "D1", "O", "O", "O", "D1", "D1", "D1"], "sickDays": 0, "srcNumber": "3806", "workDays": 18, "workTime": "180:00"}, "T00007": {"aircraft": "165:00", "leaveDays": 0, "name": "Employee354", "office": "55:00", "overtime": "0:00", "overtimeDays": 0, "position": "MTL", "rowIndex": 5, "shifts": [null, "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O", "O", "O", "D1", "D1", "N1", "N1", "N2", "O"], "sickDays": 0, "srcNumber": "3808", "workDays": 20, "workTime": "224:00"}}}}}}}, "users": {"admin": {"departmentId": "dept_001", "role": "admin"}, "manager1": {"departmentId": "dept_002", "role": "manager"}, "manager2": {"departmentId": "dept_003", "role": "manager"}, "user1": {"departmentId": "dept_001", "role": "user"}, "user2": {"departmentId": "dept_002", "role": "user"}}}