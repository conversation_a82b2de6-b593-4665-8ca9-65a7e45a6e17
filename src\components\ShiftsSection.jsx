//src/components/ShiftsSection.jsx
import React, { useState, memo, useContext } from 'react';
import { toast } from 'react-toastify';
import { ChromePicker } from 'react-color';
import { AuthContext } from './AuthProvider';

const ModalContent = memo(
  ({ isEdit, shiftForm, setShiftForm, isSubmitting, isInitializing, onSubmit, onCancel }) => {
    const handleChange = (field, value) => {
      setShiftForm((prev) => ({ ...prev, [field]: value }));
    };

    return (
      <div className="space-y-4">
        {!isEdit && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Name *</label>
            <input
              key="name"
              type="text"
              value={shiftForm.name}
              onChange={(e) => handleChange('name', e.target.value)}
              className="w-full p-2 border rounded-md"
              required
              disabled={isSubmitting || isInitializing}
            />
          </div>
        )}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
          <input
            key="description"
            type="text"
            value={shiftForm.description}
            onChange={(e) => handleChange('description', e.target.value)}
            className="w-full p-2 border rounded-md"
            disabled={isSubmitting || isInitializing}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Start Time</label>
          <input
            key="startTime"
            type="time"
            value={shiftForm.startTime}
            onChange={(e) => handleChange('startTime', e.target.value)}
            className="w-full p-2 border rounded-md"
            disabled={isSubmitting || isInitializing}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">End Time</label>
          <input
            key="endTime"
            type="time"
            value={shiftForm.endTime}
            onChange={(e) => handleChange('endTime', e.target.value)}
            className="w-full p-2 border rounded-md"
            disabled={isSubmitting || isInitializing}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Hours</label>
          <input
            key="hours"
            type="number"
            step="0.1"
            value={shiftForm.hours}
            onChange={(e) => handleChange('hours', e.target.value)}
            className="w-full p-2 border rounded-md"
            disabled={isSubmitting || isInitializing}
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Color</label>
          <ChromePicker
            key="color"
            color={shiftForm.color}
            onChange={(color) => handleChange('color', color.hex)}
            disableAlpha
            disabled={isSubmitting || isInitializing}
          />
        </div>
        <div className="mt-6 flex justify-end space-x-2">
          <button
            onClick={onCancel}
            className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 disabled:bg-gray-400"
            disabled={isSubmitting || isInitializing}
          >
            Cancel
          </button>
          <button
            onClick={onSubmit}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400"
            disabled={isSubmitting || isInitializing}
          >
            {isEdit ? 'Update' : 'Add'}
          </button>
        </div>
      </div>
    );
  }
);

const ShiftsSection = ({ shifts, onAddShift, onUpdateShift, onDeleteShift, isSubmitting, isInitializing }) => {
  const { userDepartment } = useContext(AuthContext);

  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [currentShift, setCurrentShift] = useState(null);
  const [shiftForm, setShiftForm] = useState({
    name: '',
    description: '',
    startTime: '',
    endTime: '',
    hours: '',
    color: '#ffffff',
  });

  const handleAdd = async () => {
    if (isSubmitting || isInitializing) return;

    if (!shiftForm.name) {
      toast.error('Shift name is required.');
      return;
    }
    if (shifts[shiftForm.name]) {
      toast.error('Shift name already exists.');
      return;
    }

    const newShift = {
      name: shiftForm.name,
      description: shiftForm.description || '',
      startTime: shiftForm.startTime || '',
      endTime: shiftForm.endTime || '',
      hours: parseFloat(shiftForm.hours) || 0,
      color: shiftForm.color || '#ffffff',
    };

    try {
      await onAddShift(shiftForm.name, newShift);
      toast.success('Shift added successfully!');
      setIsAddModalOpen(false);
      setShiftForm({ name: '', description: '', startTime: '', endTime: '', hours: '', color: '#ffffff' });
    } catch (err) {
      toast.error(err.message);
    }
  };

  const handleEdit = async () => {
    if (isSubmitting || isInitializing) return;

    const updatedShift = {
      name: currentShift.name,
      description: shiftForm.description || '',
      startTime: shiftForm.startTime || '',
      endTime: shiftForm.endTime || '',
      hours: parseFloat(shiftForm.hours) || 0,
      color: shiftForm.color || '#ffffff',
    };

    try {
      await onUpdateShift(currentShift.name, updatedShift);
      toast.success('Shift updated successfully!');
      setIsEditModalOpen(false);
      setShiftForm({ name: '', description: '', startTime: '', endTime: '', hours: '', color: '#ffffff' });
      setCurrentShift(null);
    } catch (err) {
      toast.error(err.message);
    }
  };

  const handleDelete = async (shiftName) => {
    if (isSubmitting || isInitializing) return;

    if (!window.confirm(`Are you sure you want to delete shift ${shiftName}?`)) return;

    try {
      await onDeleteShift(shiftName);
      toast.success('Shift deleted successfully!');
    } catch (err) {
      toast.error(err.message);
    }
  };

  const handleCancel = () => {
    if (isEditModalOpen) {
      setIsEditModalOpen(false);
      setCurrentShift(null);
    } else {
      setIsAddModalOpen(false);
    }
    setShiftForm({ name: '', description: '', startTime: '', endTime: '', hours: '', color: '#ffffff' });
  };

  return (
    <div>
      <h2 className="text-xl font-bold mb-4">Shifts</h2>
      <div className="mb-4">
        <button
          onClick={() => setIsAddModalOpen(true)}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400"
          disabled={isSubmitting || isInitializing}
          title="Add a new shift"
        >
          Add Shift
        </button>
      </div>
      <div className="overflow-x-auto">
        <table className="min-w-full border-collapse border border-gray-300">
          <thead>
            <tr className="bg-gray-100">
              <th className="border border-gray-300 p-2 text-left">Name</th>
              <th className="border border-gray-300 p-2 text-left">Description</th>
              <th className="border border-gray-300 p-2 text-left">Start Time</th>
              <th className="border border-gray-300 p-2 text-left">End Time</th>
              <th className="border border-gray-300 p-2 text-left">Hours</th>
              <th className="border border-gray-300 p-2 text-left">Color</th>
              <th className="border border-gray-300 p-2 text-left">Actions</th>
            </tr>
          </thead>
          <tbody>
            {Object.entries(shifts).map(([name, shift]) => (
              <tr key={name} className="hover:bg-gray-50">
                <td className="border border-gray-300 p-2">{shift.name}</td>
                <td className="border border-gray-300 p-2">{shift.description || '-'}</td>
                <td className="border border-gray-300 p-2">{shift.startTime || '-'}</td>
                <td className="border border-gray-300 p-2">{shift.endTime || '-'}</td>
                <td className="border border-gray-300 p-2">{shift.hours}</td>
                <td className="border border-gray-300 p-2">
                  <div className="w-6 h-6 inline-block mr-2" style={{ backgroundColor: shift.color }}></div>
                  {shift.color}
                </td>
                <td className="border border-gray-300 p-2">
                  <button
                    onClick={() => {
                      setCurrentShift(shift);
                      setShiftForm({
                        name: shift.name,
                        description: shift.description || '',
                        startTime: shift.startTime || '',
                        endTime: shift.endTime || '',
                        hours: shift.hours.toString(),
                        color: shift.color || '#ffffff',
                      });
                      setIsEditModalOpen(true);
                    }}
                    className="mr-2 text-blue-600 hover:underline"
                    disabled={isSubmitting || isInitializing}
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => handleDelete(shift.name)}
                    className="text-red-600 hover:underline"
                    disabled={isSubmitting || isInitializing}
                  >
                    Delete
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      {isAddModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[900]">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h2 className="text-xl font-bold mb-4">Add Shift</h2>
            <ModalContent
              isEdit={false}
              shiftForm={shiftForm}
              setShiftForm={setShiftForm}
              isSubmitting={isSubmitting}
              isInitializing={isInitializing}
              onSubmit={handleAdd}
              onCancel={handleCancel}
            />
          </div>
        </div>
      )}
      {isEditModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[900]">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h2 className="text-xl font-bold mb-4">Edit Shift: {currentShift.name}</h2>
            <ModalContent
              isEdit={true}
              shiftForm={shiftForm}
              setShiftForm={setShiftForm}
              isSubmitting={isSubmitting}
              isInitializing={isInitializing}
              onSubmit={handleEdit}
              onCancel={handleCancel}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default React.memo(ShiftsSection);