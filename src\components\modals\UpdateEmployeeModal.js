// src/components/modals/UpdateEmployeeModal.js
import React from 'react';

/**
 * UpdateEmployeeModal component - Modal for confirming employee updates
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {Object} props.previewData - Preview data from previewEmployeeUpdate
 * @param {Object} props.updateResult - Result of updating an employee
 * @param {boolean} props.isUpdating - Whether updating an employee is in progress
 * @param {Function} props.closeModal - Function to close the modal
 * @param {Function} props.confirmUpdate - Function to confirm updating an employee
 * @returns {JSX.Element|null} UpdateEmployeeModal component or null if not open
 */
const UpdateEmployeeModal = ({
  isOpen,
  previewData,
  updateResult,
  isUpdating,
  closeModal,
  confirmUpdate,
}) => {
  if (!isOpen || !previewData) return null;

  const {
    currentEmployee,
    changedFields,
    isRowIndexChange,
    oldRowIndex,
    newRowIndex,
    hasR<PERSON><PERSON><PERSON><PERSON><PERSON>,
    affectedEmployees,
    affectedMonths,
    changeType
  } = previewData;

  const formatFieldName = (field) => {
    const fieldNames = {
      name: 'Name',
      email: 'Email',
      phone: 'Phone',
      position: 'Position',
      srcNumber: 'SRC Number'
    };
    return fieldNames[field] || field;
  };

  const renderChangeType = () => {
    switch (changeType) {
      case 'rowIndex':
        return (
          <>
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800">Position Change</h3>
                  <p className="text-sm text-yellow-700 mt-1">
                    Moving <strong>{currentEmployee.name}</strong> from position <strong>{oldRowIndex}</strong> to position <strong>{newRowIndex}</strong>
                  </p>
                </div>
              </div>
            </div>

            {affectedEmployees.length > 0 && (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Affected Employees:</h4>
                <div className="bg-gray-50 rounded-md p-3 max-h-32 overflow-y-auto">
                  {affectedEmployees.map((emp) => (
                    <div key={emp.employeeId} className="text-sm text-gray-700 mb-1">
                      <strong>{emp.name}</strong>: Position {emp.oldRowIndex} → {emp.newRowIndex}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </>
        );

      case 'rowIndexSkipped':
        return (
          <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-4">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-800">Position Change Skipped</h3>
                <p className="text-sm text-blue-700 mt-1">
                  <strong>{currentEmployee.name}</strong> has no future roster data. Position will remain <strong>{oldRowIndex}</strong>.
                </p>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-lg max-h-[90vh] overflow-y-auto">
        <h2 className="text-xl font-bold mb-4">
          {changeType === 'rowIndex' ? 'Update Employee Position' : 'Update Employee Information'}
        </h2>
        
        {!updateResult ? (
          <>
            <div className="mb-4">
              <p className="text-sm text-gray-700 mb-2">
                Updating information for <strong>{currentEmployee.name}</strong>
              </p>
              
              {changedFields.length > 0 && (
                <div className="mb-3">
                  <p className="text-sm text-gray-600">Changes:</p>
                  <ul className="list-disc pl-5 text-sm text-gray-700">
                    {changedFields.map(field => (
                      <li key={field}>{formatFieldName(field)}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            {renderChangeType()}

            {affectedMonths.length > 0 && (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Affected Rosters:</h4>
                <ul className="list-disc pl-5 text-sm text-gray-700">
                  {affectedMonths.length === 0 ? (
                    <li>No future rosters available</li>
                  ) : (
                    affectedMonths.map(({ year, month }) => (
                      <li key={`${year}-${month}`}>
                        {new Date(year, month - 1).toLocaleString('default', { month: 'long' })} {year}
                      </li>
                    ))
                  )}
                </ul>
              </div>
            )}

            <p className="text-sm text-gray-700 mb-4">
              Do you want to proceed with these changes?
            </p>

            {isUpdating && (
              <div className="flex flex-col items-center justify-center mb-4">
                <p className="text-sm text-gray-600 mb-2">Updating employee...</p>
                <div
                  className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-600"
                  role="status"
                  aria-label="Loading"
                ></div>
              </div>
            )}

            <div className="flex justify-end space-x-2">
              <button
                onClick={closeModal}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                disabled={isUpdating}
              >
                Cancel
              </button>
              <button
                onClick={confirmUpdate}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                disabled={isUpdating}
              >
                Confirm Update
              </button>
            </div>
          </>
        ) : updateResult.success ? (
          <>
            <div className="text-center mb-4">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-3">
                <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Update Successful</h3>
              <p className="text-sm text-green-600">
                <strong>{currentEmployee.name}</strong> has been updated successfully.
              </p>
            </div>
            <div className="flex justify-end">
              <button
                onClick={closeModal}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Close
              </button>
            </div>
          </>
        ) : (
          <>
            <div className="text-center mb-4">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-3">
                <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Update Failed</h3>
              <p className="text-sm text-red-600 mb-4">
                {updateResult.error || 'An error occurred while updating the employee.'}
              </p>
            </div>
            <div className="flex justify-end space-x-2">
              <button
                onClick={closeModal}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
              >
                Cancel
              </button>
              <button
                onClick={confirmUpdate}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Retry
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default UpdateEmployeeModal;
