// src/components/RosterTable/RosterInstructions.js
import React from 'react';

/**
 * RosterInstructions component - Displays instructions for using the roster table
 *
 * @returns {JSX.Element} RosterInstructions component
 */
const RosterInstructions = () => {
  return (
    <div style={{
      marginBottom: '1rem',
      width: 'fit-content',
      padding: '0.5rem',
      backgroundColor: '#e5e7eb',
      borderRadius: '0.375rem'
    }}>
      <strong style={{ fontSize: '0.875rem', color: '#374151' }}>📋 Instructions:</strong>
      <ul style={{
        fontSize: '0.875rem',
        color: '#374151',
        listStyleType: 'disc',
        marginLeft: '1.5rem',
        marginTop: '0.25rem'
      }}>
        <li><strong>🔄 Update Mode</strong>: Click a cell to edit its shift via dropdown.</li>
        <li><strong>🖱️ Drag Mode</strong>: Click to select a cell, Ctrl+click for multiple ranges, Shift+click to extend a range, then drag to copy shifts to another row or within the same row.</li>
        <li><strong>📝 Dialog Mode</strong>: Click a cell to edit shifts for a technician over a date range via modal.</li>
        <li><strong>↕️ Employee Reorder Mode</strong>: Drag employee names (↕️) to reorder their positions. Changes affect future rosters from the current viewing month forward.</li>
        <li><strong>⌨️ Keyboard Shortcuts</strong>: Ctrl+A (select all), Shift+Arrow keys (extend selection) in Drag Mode.</li>
        <li><strong>💾 Save/Undo</strong>: Save changes to keep edits or undo to revert. Changes are highlighted until saved.</li>
      </ul>
    </div>
  );
};

export default RosterInstructions;
