// src/utils/performanceMonitor.js

/**
 * A utility for monitoring and logging performance metrics
 */
class PerformanceMonitor {
  constructor() {
    this.measurements = {};
    this.isEnabled = process.env.NODE_ENV !== 'production';
    this.logLevel = 'warn'; // 'log', 'warn', 'error', or 'none'
  }

  /**
   * Enable or disable performance monitoring
   * @param {boolean} isEnabled - Whether monitoring is enabled
   */
  setEnabled(isEnabled) {
    this.isEnabled = isEnabled;
  }

  /**
   * Set the log level for performance monitoring
   * @param {string} level - Log level ('log', 'warn', 'error', or 'none')
   */
  setLogLevel(level) {
    if (['log', 'warn', 'error', 'none'].includes(level)) {
      this.logLevel = level;
    } else {
      console.error(`Invalid log level: ${level}. Using 'warn' instead.`);
      this.logLevel = 'warn';
    }
  }

  /**
   * Start measuring performance for a specific operation
   * @param {string} operationName - Name of the operation to measure
   */
  startMeasure(operationName) {
    if (!this.isEnabled) return;
    
    if (!this.measurements[operationName]) {
      this.measurements[operationName] = {
        startTime: performance.now(),
        endTime: null,
        duration: null,
        count: 1,
        totalDuration: 0,
        minDuration: Infinity,
        maxDuration: 0
      };
    } else {
      this.measurements[operationName].startTime = performance.now();
      this.measurements[operationName].count++;
    }
  }

  /**
   * End measuring performance for a specific operation
   * @param {string} operationName - Name of the operation to measure
   * @param {number} threshold - Threshold in ms for logging warnings
   */
  endMeasure(operationName, threshold = 100) {
    if (!this.isEnabled) return;
    
    if (!this.measurements[operationName]) {
      console.error(`No measurement started for operation: ${operationName}`);
      return;
    }

    const measurement = this.measurements[operationName];
    measurement.endTime = performance.now();
    measurement.duration = measurement.endTime - measurement.startTime;
    measurement.totalDuration += measurement.duration;
    measurement.minDuration = Math.min(measurement.minDuration, measurement.duration);
    measurement.maxDuration = Math.max(measurement.maxDuration, measurement.duration);

    // Log if duration exceeds threshold
    if (measurement.duration > threshold && this.logLevel !== 'none') {
      const message = `Performance warning: ${operationName} took ${measurement.duration.toFixed(2)}ms (threshold: ${threshold}ms)`;
      
      switch (this.logLevel) {
        case 'log':
          console.log(message);
          break;
        case 'warn':
          console.warn(message);
          break;
        case 'error':
          console.error(message);
          break;
      }
    }
  }

  /**
   * Measure the performance of a function
   * @param {Function} fn - Function to measure
   * @param {string} operationName - Name of the operation
   * @param {number} threshold - Threshold in ms for logging warnings
   * @returns {any} Result of the function
   */
  measureFunction(fn, operationName, threshold = 100) {
    if (!this.isEnabled) return fn();
    
    this.startMeasure(operationName);
    const result = fn();
    this.endMeasure(operationName, threshold);
    return result;
  }

  /**
   * Measure the performance of an async function
   * @param {Function} asyncFn - Async function to measure
   * @param {string} operationName - Name of the operation
   * @param {number} threshold - Threshold in ms for logging warnings
   * @returns {Promise<any>} Result of the async function
   */
  async measureAsyncFunction(asyncFn, operationName, threshold = 100) {
    if (!this.isEnabled) return await asyncFn();
    
    this.startMeasure(operationName);
    try {
      const result = await asyncFn();
      this.endMeasure(operationName, threshold);
      return result;
    } catch (error) {
      this.endMeasure(operationName, threshold);
      throw error;
    }
  }

  /**
   * Get performance report for all measured operations
   * @returns {Object} Performance report
   */
  getReport() {
    if (!this.isEnabled) return {};
    
    const report = {};
    
    Object.entries(this.measurements).forEach(([operationName, measurement]) => {
      report[operationName] = {
        count: measurement.count,
        averageDuration: measurement.totalDuration / measurement.count,
        minDuration: measurement.minDuration === Infinity ? 0 : measurement.minDuration,
        maxDuration: measurement.maxDuration,
        totalDuration: measurement.totalDuration
      };
    });
    
    return report;
  }

  /**
   * Log performance report for all measured operations
   */
  logReport() {
    if (!this.isEnabled || this.logLevel === 'none') return;
    
    const report = this.getReport();
    
    if (Object.keys(report).length === 0) {
      console.log('No performance measurements recorded.');
      return;
    }
    
    console.log('Performance Report:');
    console.table(report);
  }

  /**
   * Clear all measurements
   */
  clearMeasurements() {
    this.measurements = {};
  }
}

// Create a singleton instance
const performanceMonitor = new PerformanceMonitor();

export default performanceMonitor;
