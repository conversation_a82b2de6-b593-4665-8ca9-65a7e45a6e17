# DataProvider API Documentation

## Overview

The DataProvider is a centralized data management system for the roster application. It uses React Context and the Provider pattern to make data available throughout the application without prop drilling.

## Key Features

- **Centralized State Management**: All application data is managed in a single place
- **Caching**: Efficient data fetching with automatic caching
- **Optimistic Updates**: UI updates immediately before database operations complete
- **Performance Monitoring**: Built-in performance tracking for data operations

## Core Components

### DataProvider

The main provider component that wraps your application:

```jsx
import { DataProvider } from './contexts/DataContext';

function App() {
  return (
    <DataProvider>
      <YourApp />
    </DataProvider>
  );
}
```

### Data Hooks

Access data from anywhere in your component tree:

```jsx
import { 
  useRosterData, 
  useEmployeeData,
  useShiftData,
  useContactInfo,
  useCalendarMarkers
} from './contexts/DataContext';

function YourComponent() {
  // Access roster data
  const {
    year,
    month,
    isDraftRoster,
    data: rosterData,
    availableYears,
    availableMonths,
    loading,
    error
  } = useRosterData();
  
  // Use the data in your component
  return (
    <div>
      <h1>Roster for {month}/{year}</h1>
      {/* Your component JSX */}
    </div>
  );
}
```

### Action Hook

Perform data operations using the useDataProvider hook:

```jsx
import useDataProvider from './contexts/useDataProvider';

function YourComponent() {
  const {
    // Roster actions
    changeYear,
    changeMonth,
    toggleDraftMode,
    refetchRosterData,
    
    // Employee actions
    addNewEmployee,
    updateExistingEmployee,
    removeEmployee,
    refetchEmployeesData,
    
    // Shift actions
    updateShiftData,
    removeShift,
    refetchShiftsData,
    
    // Contact info actions
    updateContactInfoData,
    removeContactInfo,
    refetchContactInfo,
    
    // Calendar markers actions
    refetchCalendarMarkers,
    
    // Cache management
    clearCache
  } = useDataProvider();
  
  // Example: Change the year
  const handleYearChange = (newYear) => {
    changeYear(newYear);
  };
  
  // Example: Add a new employee
  const handleAddEmployee = (employeeData) => {
    addNewEmployee(employeeData)
      .then(success => {
        if (success) {
          // Handle success
        } else {
          // Handle failure
        }
      });
  };
  
  return (
    <div>
      {/* Your component JSX */}
    </div>
  );
}
```

## Data Structure

### Roster Data

```javascript
{
  year: 2023,                  // Current year
  month: 6,                    // Current month (1-12)
  isDraftRoster: true,         // Whether viewing draft or published roster
  data: [...],                 // Array of roster entries
  availableYears: [2022, 2023],// Available years with roster data
  availableMonths: {           // Available months for each year
    2022: [1, 2, 3],
    2023: [1, 2, 3, 4, 5, 6]
  },
  loading: false,              // Whether data is loading
  error: null                  // Error object if fetch failed
}
```

### Employee Data

```javascript
{
  data: [...],                 // Array of employee objects
  loading: false,              // Whether data is loading
  error: null                  // Error object if fetch failed
}
```

### Shift Data

```javascript
{
  data: {...},                 // Raw shift data
  types: [...],                // Array of shift types
  hours: {...},                // Map of shift type to hours
  colors: {...},               // Map of shift type to colors
  startTimes: {...},           // Map of shift type to start times
  endTimes: {...},             // Map of shift type to end times
  loading: false,              // Whether data is loading
  error: null                  // Error object if fetch failed
}
```

## Performance Optimization

The DataProvider includes several performance optimizations:

1. **Caching**: Data is cached to reduce Firebase reads
2. **Memoization**: Expensive calculations are memoized
3. **Optimistic Updates**: UI updates immediately for better UX
4. **Selective Re-rendering**: Components only re-render when their data changes

## Best Practices

1. **Use the hooks at the top level** of your component
2. **Destructure only what you need** to minimize re-renders
3. **Use optimistic updates** for better user experience
4. **Clear the cache** when you need fresh data
5. **Monitor performance** using the PerformanceMonitor component

## Troubleshooting

Common issues and solutions:

1. **Component not updating**: Make sure you're using the correct hook and destructuring the data you need
2. **Data not refreshing**: Try using the refetch methods or clearing the cache
3. **Performance issues**: Use the PerformanceMonitor to identify bottlenecks

## Example: Complete Component

```jsx
import React, { useEffect } from 'react';
import { useRosterData } from './contexts/DataContext';
import useDataProvider from './contexts/useDataProvider';

function RosterComponent() {
  // Get data
  const {
    year,
    month,
    data: rosterData,
    loading,
    error
  } = useRosterData();
  
  // Get actions
  const {
    changeYear,
    changeMonth,
    refetchRosterData
  } = useDataProvider();
  
  // Fetch data when component mounts
  useEffect(() => {
    refetchRosterData();
  }, [refetchRosterData]);
  
  // Handle year change
  const handleYearChange = (e) => {
    changeYear(parseInt(e.target.value, 10));
  };
  
  // Handle month change
  const handleMonthChange = (e) => {
    changeMonth(parseInt(e.target.value, 10));
  };
  
  // Render loading state
  if (loading) {
    return <div>Loading...</div>;
  }
  
  // Render error state
  if (error) {
    return <div>Error: {error.message}</div>;
  }
  
  // Render data
  return (
    <div>
      <h1>Roster for {month}/{year}</h1>
      
      <div>
        <label>
          Year:
          <select value={year} onChange={handleYearChange}>
            {[2022, 2023, 2024].map(y => (
              <option key={y} value={y}>{y}</option>
            ))}
          </select>
        </label>
        
        <label>
          Month:
          <select value={month} onChange={handleMonthChange}>
            {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map(m => (
              <option key={m} value={m}>{m}</option>
            ))}
          </select>
        </label>
      </div>
      
      {/* Render roster data */}
      <table>
        <thead>
          <tr>
            <th>Employee</th>
            <th>Position</th>
            {/* Add more headers */}
          </tr>
        </thead>
        <tbody>
          {rosterData.map(employee => (
            <tr key={employee.employeeId}>
              <td>{employee.Name}</td>
              <td>{employee.Position}</td>
              {/* Add more cells */}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

export default RosterComponent;
```
