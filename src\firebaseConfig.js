// Import the functions you need from the SDKs you need
//firebaseConfig.js
import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
import { get, getDatabase } from "firebase/database";  
import {getFunctions} from "firebase/functions"
import {getAuth} from "firebase/auth"; // Import auth if you need authentication features
import { data } from "react-router-dom";
// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyC3oOqBmVqJylWeAyfwU1mDSrGztMtndDU",
  authDomain: "human-resource-managemen.firebaseapp.com",
  projectId: "human-resource-managemen",
  storageBucket: "human-resource-managemen.firebasestorage.app",
  messagingSenderId: "221355359371",
  appId: "1:221355359371:web:ab8f50ae0344709ee3e88e",
  measurementId: "G-8MJ6P269TZ"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const database = getDatabase(app);
// Connect Database to Emulator (default port is 9000)
//connectDatabaseEmulator(database, "localhost", 9000);
export {database};
export const auth = getAuth(app);
export const functions = getFunctions(app);

const analytics = getAnalytics(app);