//src/Home.js
import React, { useState, useEffect, useMemo, useCallback } from "react";
import {
  getDaysInMonth,
  getDaysOfWeek,
  formatHoursMinutes,
} from "./utils/dateUtils";
import { orderContactInfo } from "./utils/contactUtils";
import RosterHeader from "./components/RosterHeader";
import RosterNavBar from "./components/RosterNavBar";
import RosterControls from "./components/RosterControls";
import RosterContent from "./components/RosterContent";
import AddEmployeeModal from "./components/modals/AddEmployeeModal";
import AddRosterModal from "./components/modals/AddRosterModal";
import DeleteRowModal from "./components/modals/DeleteRowModal";
import PublishRosterModal from "./components/modals/PublishRosterModal";
import useAddEmployee from "./useAddEmployee";
import useAddRoster from "./hooks/useAddRoster";

// Import custom hooks for refactoring
import useEmployeeManagement from "./hooks/useEmployeeManagement";
import useRosterManagement from "./hooks/useRosterManagement";
import usePublishRoster from "./hooks/usePublishRoster";
import useShiftSummary from "./hooks/useShiftSummary";

// Import data provider hooks
import {
  useRosterData,
  useShiftData,
  useContactInfo,
  useCalendarMarkers
} from "./contexts/DataContext.jsx";
import useDataProvider from "./contexts/useDataProvider";

// Import performance monitoring utilities
import performanceMonitor from "./utils/performanceMonitor";

function Home() {
  // Use data from the DataProvider
  const {
    year,
    month,
    isDraftRoster: isRosterDraft,
    data: rosterData,
    availableYears,
    availableMonths,
    loading: rosterLoading,
    error: rosterError
  } = useRosterData();

  const {
    types: shiftTypes,
    hours: shiftHours,
    colors: shiftColors,
    startTimes: shiftStartTimes,
    endTimes: shiftEndTimes,
    loading: shiftLoading,
    error: shiftError
  } = useShiftData();

  const {
    data: contactInfo,
    loading: contactLoading,
    error: contactError
  } = useContactInfo();

  const {
    weekdayMarkers,
    holidayMarkers,
    specialDayMarkers,
    loading: markersLoading,
    error: markersError
  } = useCalendarMarkers();

  // Debug calendar markers
  useEffect(() => {
    console.log('Calendar Markers:', {
      weekdayMarkers,
      holidayMarkers,
      specialDayMarkers,
      loading: markersLoading,
      error: markersError
    });
  }, [weekdayMarkers, holidayMarkers, specialDayMarkers, markersLoading, markersError]);

  // Use data provider actions
  const {
    changeYear: setYear,
    changeMonth: setMonth,
    toggleDraftMode: toggleRosterView,
    refetchRosterData: refetch
  } = useDataProvider();

  const [localRosterData, setLocalRosterData] = useState([]);

  // Legacy hooks that will be migrated in future phases
  const {
    addEmployee,
    loading: addEmployeeLoading,
    error: addEmployeeError,
  } = useAddEmployee(year, month);

  const {
    addRoster,
    loading: addRosterLoading,
    error: addRosterError,
  } = useAddRoster();

    // Use employee management hook
    const {
      availableEmployees,
      isAddEmployeeModalOpen,
      selectedEmployee,
      addEmployeeResult,
      affectedMonths,
      fetchAvailableEmployees,
      handleAddEmployee,
      confirmAddEmployee,
      closeAddEmployeeModal,
    } = useEmployeeManagement(year, month, addEmployee, refetch);

    // Use roster management hook - Note: refetchRosters is now handled by DataProvider
    const {
      isAddRosterModalOpen,
      addRosterResult,
      nextRoster,
      handleAddRoster,
      confirmAddRoster,
      closeAddRosterModal,
      isDeleteModalOpen,
      deleteResult,
      isDeleting,
      affectedDeleteMonths,
      isCurrentOrFutureMonth,
      handleDeleteLastRow,
      confirmDeleteLastRow,
      closeDeleteModal,
    } = useRosterManagement(
      year,
      month,
      localRosterData,
      setLocalRosterData,
      addRoster,
      refetch,
      () => {}, // refetchRosters is now handled by DataProvider
      fetchAvailableEmployees
    );

    // Use publish roster hook
    const {
      isPublishModalOpen,
      publishResult,
      isPublishing,
      handlePublishChanges,
      confirmPublishChanges,
      closePublishModal,
    } = usePublishRoster(year, month, localRosterData);

    // Memoize expensive calculations
    const daysInMonth = useMemo(() => {
      performanceMonitor.startMeasure('getDaysInMonth');
      const result = getDaysInMonth(year, month);
      performanceMonitor.endMeasure('getDaysInMonth');
      return result;
    }, [year, month]);

    const daysOfWeek = useMemo(() => {
      performanceMonitor.startMeasure('getDaysOfWeek');
      const result = getDaysOfWeek(year, month, daysInMonth);
      performanceMonitor.endMeasure('getDaysOfWeek');
      return result;
    }, [year, month, daysInMonth]);

    // Memoize event handlers
    const handleToggleRosterView = useCallback(() => {
      toggleRosterView();
    }, [toggleRosterView]);

    // Log data changes for debugging (only in development)
    useEffect(() => {
      if (process.env.NODE_ENV !== 'production') {
        console.log("Available months:", availableMonths);
      }
    }, [availableMonths]);

    useEffect(() => {
      if (process.env.NODE_ENV !== 'production') {
        console.log("Available years:", availableYears);
      }
    }, [availableYears]);

    // Update local roster data when roster data changes
    useEffect(() => {
      performanceMonitor.startMeasure('updateLocalRosterData');

      if (rosterData && rosterData.length > 0) {
        if (process.env.NODE_ENV !== 'production') {
          console.log("Home.js setting localRosterData:", rosterData);
        }
        setLocalRosterData(rosterData);
      } else {
        setLocalRosterData([]);
      }

      performanceMonitor.endMeasure('updateLocalRosterData');
    }, [rosterData]);

    // Memoize loading and error states
    const isLoading = useMemo(() =>
      rosterLoading || shiftLoading || contactLoading || markersLoading,
      [rosterLoading, shiftLoading, contactLoading, markersLoading]
    );

    const error = useMemo(() =>
      rosterError || shiftError || contactError || markersError,
      [rosterError, shiftError, contactError, markersError]
    );
    // Get shift summary data (hooks must be called at the top level)
    const { shiftSummary, shiftTotals } = useShiftSummary(localRosterData, daysInMonth);

    // Performance monitoring for shift summary calculation
    useEffect(() => {
      performanceMonitor.startMeasure('processShiftSummary');
      performanceMonitor.endMeasure('processShiftSummary');
    }, [shiftSummary, shiftTotals]);

    // Memoize contact info ordering
    const orderedContactInfo = useMemo(() => {
      performanceMonitor.startMeasure('orderContactInfo');
      const result = orderContactInfo(contactInfo);
      performanceMonitor.endMeasure('orderContactInfo');
      return result;
    }, [contactInfo]);

    return (
      <div style={{ display: "flex", minHeight: "100vh", position: "relative", margin: "0 auto" }}>
        {!isLoading && (
          <RosterNavBar
            availableYears={availableYears}
            availableMonths={availableMonths[year] || []}
            year={year}
            setYear={setYear}
            selectedMonth={month}
            onMonthSelect={setMonth}
            setMonth={setMonth}
          />
        )}
        <div
          style={{
            paddingLeft: isLoading ? "0" : "120px",
            paddingTop: "100px",
            flex: 1,
            minHeight: "100vh",
            backgroundColor: "#f9fafb",
            paddingBottom: "1rem",
            boxSizing: "border-box",
          }}
        >
          {isLoading ? (
            <div
              style={{
                minHeight: "calc(100vh - 60px)",
                display: "flex",
                width: "100%",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <div style={{ textAlign: "center", fontSize: "1.25rem" }}>
                Loading roster, shift, and contact data...
              </div>
              <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-[1000]">
                <div className="animate-spin rounded-full h-20 w-20 border-t-4 border-blue-600"></div>
              </div>
            </div>
          ) : error ? (
            <div
              style={{
                minHeight: "calc(100vh - 60px)",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <div
                style={{
                  backgroundColor: "#fee2e2",
                  border: "1px solid #f87171",
                  color: "#b91c1c",
                  padding: "0.75rem 1rem",
                  borderRadius: "0.375rem",
                  maxWidth: "28rem",
                  textAlign: "center",
                }}
              >
                <strong style={{ fontWeight: "bold" }}>Error!</strong>
                <span> {error.message}</span>
                <p style={{ marginTop: "0.5rem", fontSize: "0.875rem" }}>
                  Please try again later.
                </p>
              </div>
            </div>
          ) : (
            <div style={{ minHeight: "calc(100vh - 200px)" }}>
              {localRosterData.length === 0 ? (
                <div
                  style={{
                    minHeight: "calc(100vh - 60px)",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <div style={{ textAlign: "center", fontSize: "1.25rem" }}>
                    No roster data available for{" "}
                    {new Date(year, month - 1).toLocaleString("default", { month: "long" })}{" "}
                    {year}.
                  </div>
                </div>
              ) : (
                <>
                  <RosterHeader year={year} month={month} />
                  <div className="mb-6">
                  <div className="mb-6 ml-[100px]">
  <span
    className={`text-lg font-semibold ${
      isRosterDraft ? 'text-gray-700' : 'bg-gradient-to-r from-yellow-400 to-orange-500 text-transparent bg-clip-text'
    }`}
  >
    {isRosterDraft ? 'Draft Roster' : '⭐ Live Roster ⭐'}
  </span>
</div>
</div>

                  <RosterControls
                    availableEmployees={availableEmployees}
                    handleAddEmployee={handleAddEmployee}
                    handleAddRoster={handleAddRoster}
                    handleDeleteLastRow={handleDeleteLastRow}
                    handlePublishChanges={handlePublishChanges}
                    handleToggleRosterView={handleToggleRosterView}
                    addEmployeeLoading={addEmployeeLoading}
                    addEmployeeError={addEmployeeError}
                    addRosterLoading={addRosterLoading}
                    isCurrentOrFutureMonth={isCurrentOrFutureMonth}
                    localRosterData={localRosterData}
                    isDeleting={isDeleting}
                    isPublishing={isPublishing}
                    isRosterDraft={isRosterDraft}
                  />
                  <RosterContent
                    localRosterData={localRosterData}
                    setLocalRosterData={setLocalRosterData}
                    shiftTypes={shiftTypes}
                    shiftHours={shiftHours}
                    shiftColors={shiftColors}
                    daysOfWeek={daysOfWeek}
                    formatHoursMinutes={formatHoursMinutes}
                    year={year}
                    month={month}
                    weekdayMarkers={weekdayMarkers}
                    holidayMarkers={holidayMarkers}
                    specialDayMarkers={specialDayMarkers}
                    shiftSummary={shiftSummary}
                    shiftTotals={shiftTotals}
                    daysInMonth={daysInMonth}
                    shiftStartTimes={shiftStartTimes}
                    shiftEndTimes={shiftEndTimes}
                    orderedContactInfo={orderedContactInfo}
                    isRosterDraft={isRosterDraft}
                  />
                </>
              )}
            </div>
          )}
        </div>

        {/* Add Employee Modal */}
        <AddEmployeeModal
          isOpen={isAddEmployeeModalOpen}
          selectedEmployee={selectedEmployee}
          addEmployeeResult={addEmployeeResult}
          affectedMonths={affectedMonths}
          addEmployeeLoading={addEmployeeLoading}
          closeModal={closeAddEmployeeModal}
          confirmAddEmployee={confirmAddEmployee}
          year={year}
          month={month}
        />

        {/* Add Roster Modal */}
        <AddRosterModal
          isOpen={isAddRosterModalOpen}
          nextRoster={nextRoster}
          addRosterResult={addRosterResult}
          addRosterLoading={addRosterLoading}
          closeModal={closeAddRosterModal}
          confirmAddRoster={confirmAddRoster}
        />

        {/* Delete Last Row Modal */}
        <DeleteRowModal
          isOpen={isDeleteModalOpen}
          localRosterData={localRosterData}
          deleteResult={deleteResult}
          affectedDeleteMonths={affectedDeleteMonths}
          isDeleting={isDeleting}
          closeModal={closeDeleteModal}
          confirmDeleteLastRow={confirmDeleteLastRow}
          year={year}
          month={month}
        />

        {/* Publish Changes Modal */}
        <PublishRosterModal
          isOpen={isPublishModalOpen}
          publishResult={publishResult}
          isPublishing={isPublishing}
          closeModal={closePublishModal}
          confirmPublishChanges={confirmPublishChanges}
          year={year}
          month={month}
        />
      </div>
    );
  }

export default Home;
