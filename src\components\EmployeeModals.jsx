//src/components/EmployeeModals.jsx
import React, { useState, useEffect, useMemo } from 'react';
import PropTypes from 'prop-types';
import { toast } from 'react-toastify';

const EmployeeModals = ({
  employees,
  isAddModalOpen,
  setIsAddModalOpen,
  isEditModalOpen,
  setIsEditModalOpen,
  currentEmployee,
  setCurrentEmployee,
  onAddEmployee,
  onUpdateEmployee,
  isSubmitting,
  isInitializing,
}) => {
  const [employeeForm, setEmployeeForm] = useState({
    name: '',
    srcNumber: '',
    position: '',
    phone: '',
    email: '',
    rowIndex: '',
    useCustomPosition: false,
  });

  const positions = useMemo(() => {
    const uniquePositions = Array.from(new Set(employees.map((emp) => emp.position || '')));
    return uniquePositions.sort();
  }, [employees]);

  useEffect(() => {
    if (isAddModalOpen) {
      setEmployeeForm({
        name: '',
        srcNumber: '',
        position: '',
        phone: '',
        email: '',
        rowIndex: (employees.length + 1).toString(),
        useCustomPosition: false,
      });
    } else if (currentEmployee && isEditModalOpen) {
      setEmployeeForm({
        name: currentEmployee.name || '',
        srcNumber: currentEmployee.srcNumber || '',
        position: currentEmployee.position || '',
        phone: currentEmployee.phone || '',
        email: currentEmployee.email || '',
        rowIndex: (currentEmployee.rowIndex || '').toString(),
        useCustomPosition: false,
      });
    }
  }, [currentEmployee, isEditModalOpen, isAddModalOpen, employees]);

  const validateEmail = (email) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email) || !email;
  const validatePhone = (phone) => /^\+?\d{1,4}[-.\s]?\d{1,14}$/.test(phone) || !phone;
  const validateRowIndex = (rowIndex) => {
    const num = parseInt(rowIndex);
    const maxRowIndex = isEditModalOpen ? employees.length : employees.length + 1;
    return !isNaN(num) && num >= 1 && num <= maxRowIndex;
  };

  const handleChange = (field, value) => {
    setEmployeeForm((prev) => ({ ...prev, [field]: value }));
  };

  const handleCustomPositionToggle = (checked) => {
    setEmployeeForm((prev) => ({
      ...prev,
      useCustomPosition: checked,
      position: checked ? '' : prev.position,
    }));
  };

  const generateEmployeeId = () => {
    if (!employees.length) return 'T00001';
    const lastId = employees
      .map((emp) => parseInt(emp.employeeId.replace('T', '')))
      .reduce((max, id) => Math.max(max, id), 0);
    return `T${(lastId + 1).toString().padStart(5, '0')}`;
  };

  const handleAdd = async () => {
    if (isSubmitting || isInitializing) return;
    if (!employeeForm.name || !employeeForm.srcNumber) {
      toast.error('Name and SRC Number are required.');
      return;
    }
    if (!validateEmail(employeeForm.email)) {
      toast.error('Invalid email format.');
      return;
    }
    if (!validatePhone(employeeForm.phone)) {
      toast.error('Invalid phone format.');
      return;
    }

    const now = new Date().toISOString();
    const newEmployee = {
      employeeId: generateEmployeeId(),
      name: employeeForm.name,
      srcNumber: employeeForm.srcNumber,
      position: employeeForm.useCustomPosition ? employeeForm.position : employeeForm.position || 'Technician',
      phone: employeeForm.phone || '',
      email: employeeForm.email || '',
      createdAt: now,
      updatedAt: now,
      rowIndex: employees.length + 1,
    };

    try {
      await onAddEmployee(newEmployee);
      toast.success('Employee added successfully!');
      setIsAddModalOpen(false);
      setEmployeeForm({
        name: '',
        srcNumber: '',
        position: '',
        phone: '',
        email: '',
        rowIndex: '',
        useCustomPosition: false,
      });
    } catch (err) {
      toast.error(err.message || 'Failed to add employee.');
    }
  };

  const handleEdit = async () => {
    if (isSubmitting || isInitializing) return;
    if (!employeeForm.name || !employeeForm.srcNumber) {
      toast.error('Name and SRC Number are required.');
      return;
    }
    if (!validateEmail(employeeForm.email)) {
      toast.error('Invalid email format.');
      return;
    }
    if (!validatePhone(employeeForm.phone)) {
      toast.error('Invalid phone format.');
      return;
    }
    const rowIndex = parseInt(employeeForm.rowIndex);
    if (!validateRowIndex(rowIndex)) {
      toast.error(`Row Index must be between 1 and ${employees.length}.`);
      return;
    }

    const updatedEmployee = {
      ...currentEmployee,
      name: employeeForm.name,
      srcNumber: employeeForm.srcNumber,
      position: employeeForm.useCustomPosition ? employeeForm.position : employeeForm.position || '',
      phone: employeeForm.phone,
      email: employeeForm.email,
      updatedAt: new Date().toISOString(),
      rowIndex,
    };

    try {
      await onUpdateEmployee(currentEmployee.employeeId, updatedEmployee);
      toast.success('Employee updated successfully!');
      setIsEditModalOpen(false);
      setEmployeeForm({
        name: '',
        srcNumber: '',
        position: '',
        phone: '',
        email: '',
        rowIndex: '',
        useCustomPosition: false,
      });
      setCurrentEmployee(null);
    } catch (err) {
      toast.error(err.message || 'Failed to update employee.');
    }
  };

  const handleCancel = () => {
    setIsAddModalOpen(false);
    setIsEditModalOpen(false);
    setEmployeeForm({
      name: '',
      srcNumber: '',
      position: '',
      phone: '',
      email: '',
      rowIndex: '',
      useCustomPosition: false,
    });
    setCurrentEmployee(null);
  };

  return (
    <>
      {isAddModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[900]" role="dialog" aria-modal="true" aria-labelledby="add-modal-title">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h2 id="add-modal-title" className="text-xl font-bold mb-4">Add Employee</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="rowIndex">Row Index *</label>
                <input
                  id="rowIndex"
                  type="number"
                  value={employeeForm.rowIndex}
                  className="w-full p-2 border rounded-md bg-gray-100"
                  disabled={true}
                  aria-required="true"
                />
                <p className="text-sm text-gray-500 mt-1">
                  New employees are added to the end of the list (Row Index: {employees.length + 1}).
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="name">Name *</label>
                <input
                  id="name"
                  type="text"
                  value={employeeForm.name}
                  onChange={(e) => handleChange('name', e.target.value)}
                  className="w-full p-2 border rounded-md"
                  required
                  disabled={isSubmitting || isInitializing}
                  aria-required="true"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="srcNumber">SRC Number *</label>
                <input
                  id="srcNumber"
                  type="text"
                  value={employeeForm.srcNumber}
                  onChange={(e) => handleChange('srcNumber', e.target.value)}
                  className="w-full p-2 border rounded-md"
                  required
                  disabled={isSubmitting || isInitializing}
                  aria-required="true"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Position</label>
                <div className="flex items-center mb-2">
                  <input
                    id="useCustomPosition"
                    type="checkbox"
                    checked={employeeForm.useCustomPosition}
                    onChange={(e) => handleCustomPositionToggle(e.target.checked)}
                    className="mr-2"
                    disabled={isSubmitting || isInitializing}
                    aria-label="Use custom position"
                  />
                  <label className="text-sm text-gray-700" htmlFor="useCustomPosition">Use Custom Position</label>
                </div>
                {employeeForm.useCustomPosition ? (
                  <input
                    id="customPosition"
                    type="text"
                    value={employeeForm.position}
                    onChange={(e) => handleChange('position', e.target.value)}
                    className="w-full p-2 border rounded-md"
                    placeholder="Enter custom position"
                    disabled={isSubmitting || isInitializing}
                    aria-label="Custom position"
                  />
                ) : (
                  <select
                    id="selectPosition"
                    value={employeeForm.position}
                    onChange={(e) => handleChange('position', e.target.value)}
                    className="w-full p-2 border rounded-md"
                    disabled={isSubmitting || isInitializing}
                    aria-label="Select position"
                  >
                    <option value="">Select Position</option>
                    {positions.map((pos) => (
                      <option key={pos} value={pos}>
                        {pos || 'No Position'}
                      </option>
                    ))}
                  </select>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="phone">Phone</label>
                <input
                  id="phone"
                  type="text"
                  value={employeeForm.phone}
                  onChange={(e) => handleChange('phone', e.target.value)}
                  className="w-full p-2 border rounded-md"
                  placeholder="e.g., (+973) 33 6464 21"
                  disabled={isSubmitting || isInitializing}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="email">Email</label>
                <input
                  id="email"
                  type="email"
                  value={employeeForm.email}
                  onChange={(e) => handleChange('email', e.target.value)}
                  className="w-full p-2 border rounded-md"
                  placeholder="e.g., <EMAIL>"
                  disabled={isSubmitting || isInitializing}
                />
              </div>
              <div className="mt-6 flex justify-end space-x-2">
                <button
                  onClick={handleCancel}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 disabled:bg-gray-400"
                  disabled={isSubmitting || isInitializing}
                  aria-label="Cancel"
                >
                  Cancel
                </button>
                <button
                  onClick={handleAdd}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400"
                  disabled={isSubmitting || isInitializing}
                  aria-label="Add employee"
                >
                  Add
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      {isEditModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[900]" role="dialog" aria-modal="true" aria-labelledby="edit-modal-title">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h2 id="edit-modal-title" className="text-xl font-bold mb-4">Edit Employee</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="rowIndex">Row Index *</label>
                <input
                  id="rowIndex"
                  type="number"
                  value={employeeForm.rowIndex}
                  onChange={(e) => handleChange('rowIndex', e.target.value)}
                  className="w-full p-2 border rounded-md"
                  min="1"
                  max={employees.length}
                  disabled={isSubmitting || isInitializing}
                  aria-required="true"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="name">Name *</label>
                <input
                  id="name"
                  type="text"
                  value={employeeForm.name}
                  onChange={(e) => handleChange('name', e.target.value)}
                  className="w-full p-2 border rounded-md"
                  required
                  disabled={isSubmitting || isInitializing}
                  aria-required="true"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="srcNumber">SRC Number *</label>
                <input
                  id="srcNumber"
                  type="text"
                  value={employeeForm.srcNumber}
                  onChange={(e) => handleChange('srcNumber', e.target.value)}
                  className="w-full p-2 border rounded-md"
                  required
                  disabled={isSubmitting || isInitializing}
                  aria-required="true"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Position</label>
                <div className="flex items-center mb-2">
                  <input
                    id="useCustomPosition"
                    type="checkbox"
                    checked={employeeForm.useCustomPosition}
                    onChange={(e) => handleCustomPositionToggle(e.target.checked)}
                    className="mr-2"
                    disabled={isSubmitting || isInitializing}
                    aria-label="Use custom position"
                  />
                  <label className="text-sm text-gray-700" htmlFor="useCustomPosition">Use Custom Position</label>
                </div>
                {employeeForm.useCustomPosition ? (
                  <input
                    id="customPosition"
                    type="text"
                    value={employeeForm.position}
                    onChange={(e) => handleChange('position', e.target.value)}
                    className="w-full p-2 border rounded-md"
                    placeholder="Enter custom position"
                    disabled={isSubmitting || isInitializing}
                    aria-label="Custom position"
                  />
                ) : (
                  <select
                    id="selectPosition"
                    value={employeeForm.position}
                    onChange={(e) => handleChange('position', e.target.value)}
                    className="w-full p-2 border rounded-md"
                    disabled={isSubmitting || isInitializing}
                    aria-label="Select position"
                  >
                    <option value="">Select Position</option>
                    {positions.map((pos) => (
                      <option key={pos} value={pos}>
                        {pos || 'No Position'}
                      </option>
                    ))}
                  </select>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="phone">Phone</label>
                <input
                  id="phone"
                  type="text"
                  value={employeeForm.phone}
                  onChange={(e) => handleChange('phone', e.target.value)}
                  className="w-full p-2 border rounded-md"
                  placeholder="e.g., (+973) 33 6464 21"
                  disabled={isSubmitting || isInitializing}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="email">Email</label>
                <input
                  id="email"
                  type="email"
                  value={employeeForm.email}
                  onChange={(e) => handleChange('email', e.target.value)}
                  className="w-full p-2 border rounded-md"
                  placeholder="e.g., <EMAIL>"
                  disabled={isSubmitting || isInitializing}
                />
              </div>
              <div className="mt-6 flex justify-end space-x-2">
                <button
                  onClick={handleCancel}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 disabled:bg-gray-400"
                  disabled={isSubmitting || isInitializing}
                  aria-label="Cancel"
                >
                  Cancel
                </button>
                <button
                  onClick={handleEdit}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400"
                  disabled={isSubmitting || isInitializing}
                  aria-label="Update employee"
                >
                  Update
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

EmployeeModals.propTypes = {
  employees: PropTypes.arrayOf(
    PropTypes.shape({
      employeeId: PropTypes.string.isRequired,
      rowIndex: PropTypes.number,
      name: PropTypes.string,
      srcNumber: PropTypes.string,
      position: PropTypes.string,
      phone: PropTypes.string,
      email: PropTypes.string,
      createdAt: PropTypes.string,
      updatedAt: PropTypes.string,
    })
  ).isRequired,
  isAddModalOpen: PropTypes.bool.isRequired,
  setIsAddModalOpen: PropTypes.func.isRequired,
  isEditModalOpen: PropTypes.bool.isRequired,
  setIsEditModalOpen: PropTypes.func.isRequired,
  currentEmployee: PropTypes.object,
  setCurrentEmployee: PropTypes.func.isRequired,
  onAddEmployee: PropTypes.func.isRequired,
  onUpdateEmployee: PropTypes.func.isRequired,
  isSubmitting: PropTypes.bool.isRequired,
  isInitializing: PropTypes.bool.isRequired,
};

export default EmployeeModals;