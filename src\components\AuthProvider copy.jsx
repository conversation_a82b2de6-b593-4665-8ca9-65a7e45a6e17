
// src\components\AuthProvider.jsx
import React, { createContext, useState, useEffect } from 'react';
import { auth, database } from '../firebaseConfig';
import { onAuthStateChanged } from 'firebase/auth';
import { get, ref, set } from 'firebase/database';
import { toast } from 'react-toastify';

export const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [userRole, setUserRole] = useState(null);
  const [userDepartment, setUserDepartment] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setCurrentUser(user);
      if (user) {
        try {
          const userRef = ref(database, `users/${user.uid}`);
          const snapshot = await get(userRef);
          let role = 'default'; // Default role
          let department = null; // Default department

          if (snapshot.exists()) {
            role = snapshot.val().role || 'default';
            department = snapshot.val().department || null;
          } else {
            // Create default user entry
            await set(userRef, {
              email: user.email,
              role: 'default',
              uid: user.uid,
            });
          }
          console.log('AuthProvider: Setting user role and department', { uid: user.uid, role, department });
          setUserRole(role);
          setUserDepartment(department);
        } catch (error) {
          console.error('Error fetching user data:', error);
          toast.error('Failed to load user profile.');
          setUserRole('default');
          setUserDepartment(null);
        }
      } else {
        setUserRole(null);
        setUserDepartment(null);
      }
      setLoading(false);
    }, (error) => {
      console.error('Auth state error:', error);
      toast.error('Authentication error.');
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  // Helper function to check if user is super-admin
  const isSuperAdmin = () => {
    return userRole === 'super-admin';
  };

  // Helper function to check if user has access to a specific department
  const hasAccessToDepartment = (departmentId) => {
    // Super-admin can access all departments
    if (isSuperAdmin()) return true;

    // Admin can read and write to their own department
    if (userRole === 'admin' && userDepartment === departmentId) return true;

    // Regular user can only read their own department
    if (userRole === 'user' && userDepartment === departmentId) return true;

    return false;
  };

  // Helper function to check if user has write access to a specific department
  const hasWriteAccessToDepartment = (departmentId) => {
    // Super-admin can write to all departments
    if (isSuperAdmin()) return true;

    // Admin can write to their own department
    if (userRole === 'admin' && userDepartment === departmentId) return true;

    // Regular users don't have write access
    return false;
  };

  const value = {
    currentUser,
    userRole,
    userDepartment,
    loading,
    isSuperAdmin,
    hasAccessToDepartment,
    hasWriteAccessToDepartment
  };

  return <AuthContext.Provider value={value}>{!loading && children}</AuthContext.Provider>;
};
