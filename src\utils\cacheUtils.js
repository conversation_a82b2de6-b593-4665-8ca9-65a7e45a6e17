// src/utils/cacheUtils.js

/**
 * A simple in-memory cache utility for storing and retrieving data
 */
class CacheManager {
  constructor(options = {}) {
    this.cache = new Map();
    this.defaultTTL = options.defaultTTL || 5 * 60 * 1000; // 5 minutes default TTL
    this.maxSize = options.maxSize || 100; // Maximum number of items in cache
  }

  /**
   * Set a value in the cache with an optional TTL
   * @param {string} key - The cache key
   * @param {any} value - The value to store
   * @param {number} ttl - Time to live in milliseconds (optional)
   */
  set(key, value, ttl = this.defaultTTL) {
    // If cache is full, remove the oldest item
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }

    const expiresAt = Date.now() + ttl;
    this.cache.set(key, {
      value,
      expiresAt
    });

    return value;
  }

  /**
   * Get a value from the cache
   * @param {string} key - The cache key
   * @returns {any|null} The cached value or null if not found or expired
   */
  get(key) {
    if (!this.cache.has(key)) {
      return null;
    }

    const cachedItem = this.cache.get(key);
    
    // Check if the item has expired
    if (cachedItem.expiresAt < Date.now()) {
      this.cache.delete(key);
      return null;
    }

    return cachedItem.value;
  }

  /**
   * Check if a key exists in the cache and is not expired
   * @param {string} key - The cache key
   * @returns {boolean} True if the key exists and is not expired
   */
  has(key) {
    if (!this.cache.has(key)) {
      return false;
    }

    const cachedItem = this.cache.get(key);
    
    // Check if the item has expired
    if (cachedItem.expiresAt < Date.now()) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  /**
   * Remove a value from the cache
   * @param {string} key - The cache key
   */
  delete(key) {
    this.cache.delete(key);
  }

  /**
   * Clear all items from the cache
   */
  clear() {
    this.cache.clear();
  }

  /**
   * Get all valid (non-expired) keys in the cache
   * @returns {Array} Array of cache keys
   */
  keys() {
    const validKeys = [];
    for (const [key, item] of this.cache.entries()) {
      if (item.expiresAt >= Date.now()) {
        validKeys.push(key);
      } else {
        // Clean up expired items
        this.cache.delete(key);
      }
    }
    return validKeys;
  }

  /**
   * Get the number of items in the cache
   * @returns {number} Number of items in the cache
   */
  size() {
    // Clean up expired items first
    this.keys();
    return this.cache.size;
  }
}

// Create a singleton instance
const dataCache = new CacheManager({
  defaultTTL: 10 * 60 * 1000, // 10 minutes
  maxSize: 200
});

export default dataCache;
