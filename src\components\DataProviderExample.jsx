// src/components/DataProviderExample.jsx
import React from 'react';
import { 
  useRosterData, 
  useEmployeeData, 
  useShiftData, 
  useContactInfo, 
  useCalendarMarkers 
} from '../contexts/DataContext';
import useDataProvider from '../contexts/useDataProvider';

const DataProviderExample = () => {
  // Access data from contexts
  const rosterData = useRosterData();
  const employeeData = useEmployeeData();
  const shiftData = useShiftData();
  const contactInfo = useContactInfo();
  const calendarMarkers = useCalendarMarkers();
  
  // Access data provider functions
  const {
    changeYear,
    changeMonth,
    toggleDraftMode,
    refetchRosterData,
    addNewEmployee,
    updateExistingEmployee,
    removeEmployee
  } = useDataProvider();

  // Handle year change
  const handleYearChange = (e) => {
    changeYear(parseInt(e.target.value));
  };

  // Handle month change
  const handleMonthChange = (e) => {
    changeMonth(parseInt(e.target.value));
  };

  // Toggle draft mode
  const handleToggleDraftMode = () => {
    toggleDraftMode();
  };

  // Refresh roster data
  const handleRefreshRoster = () => {
    refetchRosterData();
  };

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Data Provider Example</h1>
      
      {/* Roster Controls */}
      <div className="mb-6 p-4 border rounded">
        <h2 className="text-xl font-semibold mb-2">Roster Controls</h2>
        <div className="flex flex-wrap gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium mb-1">Year:</label>
            <select 
              value={rosterData.year} 
              onChange={handleYearChange}
              className="border rounded px-2 py-1"
            >
              {rosterData.availableYears.map(year => (
                <option key={year} value={year}>{year}</option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Month:</label>
            <select 
              value={rosterData.month} 
              onChange={handleMonthChange}
              className="border rounded px-2 py-1"
            >
              {rosterData.availableMonths[rosterData.year]?.map(month => (
                <option key={month} value={month}>{month}</option>
              ))}
            </select>
          </div>
          
          <div className="flex items-end">
            <button 
              onClick={handleToggleDraftMode}
              className="bg-blue-500 text-white px-3 py-1 rounded"
            >
              {rosterData.isDraftRoster ? 'Switch to Live' : 'Switch to Draft'}
            </button>
          </div>
          
          <div className="flex items-end">
            <button 
              onClick={handleRefreshRoster}
              className="bg-green-500 text-white px-3 py-1 rounded"
            >
              Refresh Roster
            </button>
          </div>
        </div>
        
        {/* Roster Data Status */}
        <div>
          <p>
            <strong>Status:</strong> 
            {rosterData.loading ? ' Loading...' : rosterData.error ? ' Error!' : ' Ready'}
          </p>
          <p><strong>Data Count:</strong> {rosterData.data.length} employees</p>
        </div>
      </div>
      
      {/* Employee Data */}
      <div className="mb-6 p-4 border rounded">
        <h2 className="text-xl font-semibold mb-2">Employee Data</h2>
        <p>
          <strong>Status:</strong> 
          {employeeData.loading ? ' Loading...' : employeeData.error ? ' Error!' : ' Ready'}
        </p>
        <p><strong>Data Count:</strong> {employeeData.data.length} employees</p>
      </div>
      
      {/* Shift Data */}
      <div className="mb-6 p-4 border rounded">
        <h2 className="text-xl font-semibold mb-2">Shift Data</h2>
        <p>
          <strong>Status:</strong> 
          {shiftData.loading ? ' Loading...' : shiftData.error ? ' Error!' : ' Ready'}
        </p>
        <p><strong>Shift Types:</strong> {shiftData.types.join(', ')}</p>
      </div>
      
      {/* Contact Info */}
      <div className="mb-6 p-4 border rounded">
        <h2 className="text-xl font-semibold mb-2">Contact Info</h2>
        <p>
          <strong>Status:</strong> 
          {contactInfo.loading ? ' Loading...' : contactInfo.error ? ' Error!' : ' Ready'}
        </p>
        <p><strong>Data Count:</strong> {Object.keys(contactInfo.data).length} contacts</p>
      </div>
      
      {/* Calendar Markers */}
      <div className="mb-6 p-4 border rounded">
        <h2 className="text-xl font-semibold mb-2">Calendar Markers</h2>
        <p>
          <strong>Status:</strong> 
          {calendarMarkers.loading ? ' Loading...' : calendarMarkers.error ? ' Error!' : ' Ready'}
        </p>
        <p>
          <strong>Marker Types:</strong> 
          Weekday, Holiday, Special Day
        </p>
      </div>
    </div>
  );
};

export default DataProviderExample;
