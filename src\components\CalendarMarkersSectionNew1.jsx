//src\components\CalendarMarkersSectionNew1.jsx
import React, { useState, memo } from 'react';
import PropTypes from 'prop-types';
import { toast } from 'react-toastify';
import { ChromePicker } from 'react-color';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { useDataProvider } from '../contexts/useDataProvider';
import { useCalendarMarkers } from '../contexts/DataContext';

const ModalContent = memo(
  ({ isEdit, markerType, markerForm, setMarkerForm, isSubmitting, isInitializing, onSubmit, onCancel }) => {
    const handleChange = (field, value) => {
      setMarkerForm((prev) => ({ ...prev, [field]: value }));
    };

    const handleDateChange = (dateField, date) => {
      if (!date) return;
      setMarkerForm((prev) => ({
        ...prev,
        [dateField]: {
          day: date.getDate(),
          month: date.getMonth() + 1,
          year: date.getFullYear(),
        },
      }));
    };

    return (
      <div className="space-y-4 mt-20">
        {!isEdit && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Name *</label>
            <input
              type="text"
              value={markerForm.name}
              onChange={(e) => handleChange('name', e.target.value)}
              className="w-full p-2 border rounded-md"
              required
              disabled={isSubmitting || isInitializing}
            />
          </div>
        )}
        {markerType === 'weekday' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Weekday *</label>
            <select
              value={markerForm.weekday}
              onChange={(e) => handleChange('weekday', e.target.value)}
              className="w-full p-2 border rounded-md"
              disabled={isSubmitting || isInitializing}
            >
              {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map((day) => (
                <option key={day} value={day}>{day}</option>
              ))}
            </select>
          </div>
        )}
        {markerType !== 'weekday' && (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Start Date *</label>
              <DatePicker
                selected={
                  markerForm.startDate
                    ? new Date(markerForm.startDate.year, markerForm.startDate.month - 1, markerForm.startDate.day)
                    : null
                }
                onChange={(date) => handleDateChange('startDate', date)}
                dateFormat="dd/MM/yyyy"
                className="w-full p-2 border rounded-md"
                placeholderText="Select start date"
                disabled={isSubmitting || isInitializing}
                showYearDropdown
                yearDropdownItemNumber={50}
                scrollableYearDropdown
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">End Date *</label>
              <DatePicker
                selected={
                  markerForm.endDate
                    ? new Date(markerForm.endDate.year, markerForm.endDate.month - 1, markerForm.endDate.day)
                    : null
                }
                onChange={(date) => handleDateChange('endDate', date)}
                dateFormat="dd/MM/yyyy"
                className="w-full p-2 border rounded-md"
                placeholderText="Select end date"
                disabled={isSubmitting || isInitializing}
                showYearDropdown
                yearDropdownItemNumber={50}
                scrollableYearDropdown
              />
            </div>
          </>
        )}
        {markerType === 'holiday' && (
          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={markerForm.recurring}
                onChange={(e) => handleChange('recurring', e.target.checked)}
                className="mr-2"
                disabled={isSubmitting || isInitializing}
              />
              Recurring Annually
            </label>
          </div>
        )}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Color *</label>
          <ChromePicker
            color={markerForm.color}
            onChange={(color) => handleChange('color', color.hex)}
            disableAlpha
            disabled={isSubmitting || isInitializing}
          />
        </div>
        <div className="mt-6 flex justify-end space-x-2">
          <button
            onClick={onCancel}
            className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 disabled:bg-gray-400"
            disabled={isSubmitting || isInitializing}
          >
            Cancel
          </button>
          <button
            onClick={onSubmit}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400"
            disabled={isSubmitting || isInitializing}
          >
            {isEdit ? 'Update' : 'Add'}
          </button>
        </div>
      </div>
    );
  }
);

const CalendarMarkersSection = ({ isSubmitting, isInitializing }) => {
  // Get calendar markers data directly from context
  const { weekdayMarkers, holidayMarkers, specialDayMarkers, loading, error } = useCalendarMarkers();

  // Get calendar marker actions from useDataProvider
  const {
    addNewCalendarMarker,
    updateExistingCalendarMarker,
    deleteExistingCalendarMarker,
  } = useDataProvider();

  // Debug logging to track re-renders
  console.log('CalendarMarkersSection render:', {
    weekdayMarkersCount: Object.keys(weekdayMarkers || {}).length,
    holidayMarkersCount: Object.keys(holidayMarkers || {}).length,
    specialDayMarkersCount: Object.keys(specialDayMarkers || {}).length,
    loading,
    error: error?.message,
    isSubmitting,
    isInitializing
  });

  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [currentMarker, setCurrentMarker] = useState(null);
  const [markerType, setMarkerType] = useState('weekday'); // 'weekday', 'holiday', 'special_day'
  const [markerForm, setMarkerForm] = useState({
    name: '',
    weekday: 'Monday',
    startDate: { day: 1, month: 1, year: new Date().getFullYear() },
    endDate: { day: 1, month: 1, year: new Date().getFullYear() },
    recurring: false,
    color: '#ffffff',
  });

  // Helper function to reset form
  const resetForm = () => {
    setMarkerForm({
      name: '',
      weekday: 'Monday',
      startDate: { day: 1, month: 1, year: new Date().getFullYear() },
      endDate: { day: 1, month: 1, year: new Date().getFullYear() },
      recurring: false,
      color: '#ffffff',
    });
  };

  // Note: Calendar markers are automatically fetched by useDataProvider
  // No need for manual refetch on mount

  const handleAdd = async () => {
    if (isSubmitting || isInitializing) return;

    if (!markerForm.name) {
      toast.error('Marker name is required.');
      return;
    }
    if (markerType !== 'weekday') {
      if (
        !markerForm.startDate.day || !markerForm.startDate.month || !markerForm.startDate.year ||
        !markerForm.endDate.day || !markerForm.endDate.month || !markerForm.endDate.year
      ) {
        toast.error('Please select valid start and end dates.');
        return;
      }
      const startDate = new Date(markerForm.startDate.year, markerForm.startDate.month - 1, markerForm.startDate.day);
      const endDate = new Date(markerForm.endDate.year, markerForm.endDate.month - 1, markerForm.endDate.day);
      if (endDate < startDate) {
        toast.error('End date cannot be before start date.');
        return;
      }
    }
    if (!/^#[0-9A-F]{6}$/i.test(markerForm.color)) {
      toast.error('Invalid color format. Use a hex color (e.g., #FF0000).');
      return;
    }

    // Check for duplicate names
    const allMarkers = {
      weekday: weekdayMarkers,
      holiday: holidayMarkers,
      special_day: specialDayMarkers,
    };
    if (allMarkers[markerType][markerForm.name]) {
      toast.error('Marker name already exists.');
      return;
    }

    const timestamp = new Date().toISOString();
    const newMarker = {
      name: markerForm.name,
      color: markerForm.color,
      createdAt: timestamp,
      updatedAt: timestamp,
      ...(markerType === 'weekday' ? { weekday: markerForm.weekday } : {
        startDate: markerForm.startDate,
        endDate: markerForm.endDate,
        ...(markerType === 'holiday' ? { recurring: markerForm.recurring } : {}),
      }),
    };

    try {
      const success = await addNewCalendarMarker(markerType, newMarker);
      if (success) {
        toast.success('Marker added successfully!');
        setIsAddModalOpen(false);
        resetForm();
      } else {
        toast.error('Failed to add marker.');
      }
    } catch (err) {
      toast.error(err.message || 'Failed to add marker.');
    }
  };

  const handleEdit = async () => {
    if (isSubmitting || isInitializing) return;
    if (!currentMarker) return;

    if (markerType !== 'weekday') {
      if (
        !markerForm.startDate.day || !markerForm.startDate.month || !markerForm.startDate.year ||
        !markerForm.endDate.day || !markerForm.endDate.month || !markerForm.endDate.year
      ) {
        toast.error('Please select valid start and end dates.');
        return;
      }
      const startDate = new Date(markerForm.startDate.year, markerForm.startDate.month - 1, markerForm.startDate.day);
      const endDate = new Date(markerForm.endDate.year, markerForm.endDate.month - 1, markerForm.endDate.day);
      if (endDate < startDate) {
        toast.error('End date cannot be before start date.');
        return;
      }
    }
    if (!/^#[0-9A-F]{6}$/i.test(markerForm.color)) {
      toast.error('Invalid color format. Use a hex color (e.g., #FF0000).');
      return;
    }

    const timestamp = new Date().toISOString();
    const updatedMarker = {
      name: currentMarker.name,
      color: markerForm.color,
      createdAt: currentMarker.createdAt,
      updatedAt: timestamp,
      ...(markerType === 'weekday' ? { weekday: markerForm.weekday } : {
        startDate: markerForm.startDate,
        endDate: markerForm.endDate,
        ...(markerType === 'holiday' ? { recurring: markerForm.recurring } : {}),
      }),
    };

    try {
      const success = await updateExistingCalendarMarker(markerType, currentMarker.id, updatedMarker);
      if (success) {
        toast.success('Marker updated successfully!');
        setIsEditModalOpen(false);
        setCurrentMarker(null);
        resetForm();
      } else {
        toast.error('Failed to update marker.');
      }
    } catch (err) {
      toast.error(err.message || 'Failed to update marker.');
    }
  };

  const handleDelete = async (markerType, markerName, markerId) => {
    if (isSubmitting || isInitializing) return;

    if (!window.confirm(`Are you sure you want to delete marker ${markerName}?`)) return;

    try {
      const success = await deleteExistingCalendarMarker(markerId, markerType);
      if (success) {
        toast.success('Marker deleted successfully!');
      } else {
        toast.error('Failed to delete marker.');
      }
    } catch (err) {
      toast.error(err.message || 'Failed to delete marker.');
    }
  };

  const handleCancel = () => {
    if (isEditModalOpen) {
      setIsEditModalOpen(false);
      setCurrentMarker(null);
    } else {
      setIsAddModalOpen(false);
    }
    resetForm();
  };

  // Calculate loading state
  const isLoading = loading || isSubmitting || isInitializing;

  return (
    <div className='mt-10'>
      <h2 className="text-xl font-bold mb-4">Calendar Markers</h2>

      {/* Error Display */}
      {error && (
        <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
          Error: {error.message || 'Failed to load calendar markers'}
        </div>
      )}

      {/* Loading Indicator */}
      {isLoading && (
        <div className="mb-4 p-4 bg-blue-100 border border-blue-400 text-blue-700 rounded">
          Loading calendar markers...
        </div>
      )}

      <div className="mb-4 space-x-2">
        <button
          onClick={() => {
            setMarkerType('weekday');
            setIsAddModalOpen(true);
          }}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400"
          disabled={isLoading}
          title="Add a new weekday marker"
        >
          Add Weekday Marker
        </button>
        <button
          onClick={() => {
            setMarkerType('holiday');
            setIsAddModalOpen(true);
          }}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400"
          disabled={isLoading}
          title="Add a new holiday marker"
        >
          Add Holiday Marker
        </button>
        <button
          onClick={() => {
            setMarkerType('special_day');
            setIsAddModalOpen(true);
          }}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400"
          disabled={isLoading}
          title="Add a new special day marker"
        >
          Add Special Day Marker
        </button>
      </div>

      {/* Weekday Markers Table */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">Weekday Markers</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full border-collapse border border-gray-300">
            <thead>
              <tr className="bg-gray-100">
                <th className="border border-gray-300 p-2 text-left">Name</th>
                <th className="border border-gray-300 p-2 text-left">Weekday</th>
                <th className="border border-gray-300 p-2 text-left">Color</th>
                <th className="border border-gray-300 p-2 text-left">Actions</th>
              </tr>
            </thead>
            <tbody>
              {Object.entries(weekdayMarkers).map(([id, marker]) => (
                <tr key={id} className="hover:bg-gray-50">
                  <td className="border border-gray-300 p-2">{marker.name}</td>
                  <td className="border border-gray-300 p-2">{marker.weekday}</td>
                  <td className="border border-gray-300 p-2">
                    <div className="flex items-center">
                      <div className="w-6 h-6 mr-2" style={{ backgroundColor: marker.color }}></div>
                      {marker.color}
                    </div>
                  </td>
                  <td className="border border-gray-300 p-2">
                    <button
                      onClick={() => {
                        setCurrentMarker({ id, ...marker });
                        setMarkerType('weekday');
                        setMarkerForm({
                          name: marker.name,
                          weekday: marker.weekday,
                          startDate: { day: 1, month: 1, year: new Date().getFullYear() },
                          endDate: { day: 1, month: 1, year: new Date().getFullYear() },
                          recurring: false,
                          color: marker.color,
                        });
                        setIsEditModalOpen(true);
                      }}
                      className="mr-2 text-blue-600 hover:underline"
                      disabled={isLoading}
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => handleDelete('weekday', marker.name, id)}
                      className="text-red-600 hover:underline"
                      disabled={isLoading}
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          {Object.keys(weekdayMarkers).length === 0 && <p className="text-gray-500 mt-2">No weekday markers found.</p>}
        </div>
      </div>

      {/* Holiday Markers Table */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">Holiday Markers</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full border-collapse border border-gray-300">
            <thead>
              <tr className="bg-gray-100">
                <th className="border border-gray-300 p-2 text-left">Name</th>
                <th className="border border-gray-300 p-2 text-left">Date Range</th>
                <th className="border border-gray-300 p-2 text-left">Recurring</th>
                <th className="border border-gray-300 p-2 text-left">Color</th>
                <th className="border border-gray-300 p-2 text-left">Actions</th>
              </tr>
            </thead>
            <tbody>
              {Object.entries(holidayMarkers).map(([id, marker]) => (
                <tr key={id} className="hover:bg-gray-50">
                  <td className="border border-gray-300 p-2">{marker.name}</td>
                  <td className="border border-gray-300 p-2">
                    {`${marker.startDate.day}/${marker.startDate.month}/${marker.startDate.year}`}
                    {marker.startDate.day !== marker.endDate.day ||
                    marker.startDate.month !== marker.endDate.month ||
                    marker.startDate.year !== marker.endDate.year
                      ? ` - ${marker.endDate.day}/${marker.endDate.month}/${marker.endDate.year}`
                      : ''}
                  </td>
                  <td className="border border-gray-300 p-2">{marker.recurring ? 'Yes' : 'No'}</td>
                  <td className="border border-gray-300 p-2">
                    <div className="flex items-center">
                      <div className="w-6 h-6 mr-2" style={{ backgroundColor: marker.color }}></div>
                      {marker.color}
                    </div>
                  </td>
                  <td className="border border-gray-300 p-2">
                    <button
                      onClick={() => {
                        setCurrentMarker({ id, ...marker });
                        setMarkerType('holiday');
                        setMarkerForm({
                          name: marker.name,
                          weekday: 'Monday',
                          startDate: marker.startDate,
                          endDate: marker.endDate,
                          recurring: marker.recurring,
                          color: marker.color,
                        });
                        setIsEditModalOpen(true);
                      }}
                      className="mr-2 text-blue-600 hover:underline"
                      disabled={isLoading}
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => handleDelete('holiday', marker.name, id)}
                      className="text-red-600 hover:underline"
                      disabled={isLoading}
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          {Object.keys(holidayMarkers).length === 0 && <p className="text-gray-500 mt-2">No holiday markers found.</p>}
        </div>
      </div>

      {/* Special Day Markers Table */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">Special Day Markers</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full border-collapse border border-gray-300">
            <thead>
              <tr className="bg-gray-100">
                <th className="border border-gray-300 p-2 text-left">Name</th>
                <th className="border border-gray-300 p-2 text-left">Date Range</th>
                <th className="border border-gray-300 p-2 text-left">Color</th>
                <th className="border border-gray-300 p-2 text-left">Actions</th>
              </tr>
            </thead>
            <tbody>
              {Object.entries(specialDayMarkers).map(([id, marker]) => (
                <tr key={id} className="hover:bg-gray-50">
                  <td className="border border-gray-300 p-2">{marker.name}</td>
                  <td className="border border-gray-300 p-2">
                    {`${marker.startDate.day}/${marker.startDate.month}/${marker.startDate.year}`}
                    {marker.startDate.day !== marker.endDate.day ||
                    marker.startDate.month !== marker.endDate.month ||
                    marker.startDate.year !== marker.endDate.year
                      ? ` - ${marker.endDate.day}/${marker.endDate.month}/${marker.endDate.year}`
                      : ''}
                  </td>
                  <td className="border border-gray-300 p-2">
                    <div className="flex items-center">
                      <div className="w-6 h-6 mr-2" style={{ backgroundColor: marker.color }}></div>
                      {marker.color}
                    </div>
                  </td>
                  <td className="border border-gray-300 p-2">
                    <button
                      onClick={() => {
                        setCurrentMarker({ id, ...marker });
                        setMarkerType('special_day');
                        setMarkerForm({
                          name: marker.name,
                          weekday: 'Monday',
                          startDate: marker.startDate,
                          endDate: marker.endDate,
                          recurring: false,
                          color: marker.color,
                        });
                        setIsEditModalOpen(true);
                      }}
                      className="mr-2 text-blue-600 hover:underline"
                      disabled={isLoading}
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => handleDelete('special_day', marker.name, id)}
                      className="text-red-600 hover:underline"
                      disabled={isLoading}
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          {Object.keys(specialDayMarkers).length === 0 && <p className="text-gray-500 mt-2">No special day markers found.</p>}
        </div>
      </div>

      {isAddModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[900]">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h2 className="text-xl font-bold mb-4">Add {markerType.replace('_', ' ').toUpperCase()} Marker</h2>
            <ModalContent
              isEdit={false}
              markerType={markerType}
              markerForm={markerForm}
              setMarkerForm={setMarkerForm}
              isSubmitting={isLoading}
              isInitializing={isLoading}
              onSubmit={handleAdd}
              onCancel={handleCancel}
            />
          </div>
        </div>
      )}
      {isEditModalOpen && currentMarker && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[900]">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h2 className="text-xl font-bold mb-4">Edit {markerType.replace('_', ' ').toUpperCase()} Marker: {currentMarker.name}</h2>
            <ModalContent
              isEdit={true}
              markerType={markerType}
              markerForm={markerForm}
              setMarkerForm={setMarkerForm}
              isSubmitting={isLoading}
              isInitializing={isLoading}
              onSubmit={handleEdit}
              onCancel={handleCancel}
            />
          </div>
        </div>
      )}
    </div>
  );
};

CalendarMarkersSection.propTypes = {
  isSubmitting: PropTypes.bool.isRequired,
  isInitializing: PropTypes.bool.isRequired,
};

// Memoize the component with custom comparison to prevent unnecessary re-renders
export default React.memo(CalendarMarkersSection, (prevProps, nextProps) => {
  // Only re-render if the props actually change in a meaningful way
  return (
    prevProps.isSubmitting === nextProps.isSubmitting &&
    prevProps.isInitializing === nextProps.isInitializing
  );
});