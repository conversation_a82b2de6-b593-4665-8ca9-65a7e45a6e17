# Task ID: 7
# Title: Roster Publishing
# Status: pending
# Dependencies: 6
# Priority: medium
# Description: Implement roster publishing functionality. Allow managers to publish a roster, making it visible to employees. Implement a 'draft' and 'published' status.
# Details:


# Test Strategy:
Test the publishing and unpublishing of rosters. Verify that employees can only view published rosters. Check the status changes in the database.
