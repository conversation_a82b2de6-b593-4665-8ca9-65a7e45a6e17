//src\MobileRoster.jsx
import React, { useState, useEffect, useMemo } from 'react';
import Calendar from 'react-calendar';
import 'react-calendar/dist/Calendar.css';

// Import data provider hooks
import {
  useRosterData,
  useShiftData
} from './contexts/DataContext.jsx';
import useDataProvider from './contexts/useDataProvider';

const MobileRoster = () => {
  const isDraftRoster=false;
  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth() + 1; // 1-12
  const [year, setYear] = useState(currentYear);
  const [month, setMonth] = useState(currentMonth);
  const [selectedEmployeeId, setSelectedEmployeeId] = useState(null);
  const [calendarDate, setCalendarDate] = useState(new Date(currentYear, currentMonth - 1));
  const [theme, setTheme] = useState('dark'); // Add theme toggle

  // Use data from the DataProvider
  const {
    availableYears,
    availableMonths,
    data: rosterData,
    loading: rosterLoading,
    error: rosterError
  } = useRosterData();

  const {
    types: shiftTypes,
    hours: shiftHours,
    colors: shiftColors,
    loading: shiftLoading,
    error: shiftError
  } = useShiftData();

  // Use data provider actions
  const {
    changeYear: setDataYear,
    changeMonth: setDataMonth,
    refetchRosterData: fetchRosterData,
    fetchRosterDataWithOverride,
    fetchAvailableYearsWithOverride,
    fetchAvailableMonthsWithOverride
  } = useDataProvider();

  // Define loading states for backward compatibility
  const yearsLoading = rosterLoading;
  const monthsLoading = rosterLoading;

  // Update calendar when year/month changes
  useEffect(() => {
    console.log('Month state updated:', month);
    setCalendarDate(new Date(year, month - 1));
  }, [year, month]);

  // Always use live roster data for this component
  useEffect(() => {
    console.log('Setting MobileRoster to use live roster data (isDraftRoster=false)');
    fetchRosterDataWithOverride(false);
    fetchAvailableYearsWithOverride(false);
  }, [fetchRosterDataWithOverride, fetchAvailableYearsWithOverride]);

  // Fetch available months with override when year changes
  useEffect(() => {
    console.log('Fetching available months for MobileRoster with live roster data');
    fetchAvailableMonthsWithOverride(year, false);
  }, [year, fetchAvailableMonthsWithOverride]);

  // Update DataProvider when year or month changes
  useEffect(() => {
    console.log('Updating data provider with year/month:', year, month);
    setDataYear(year);
    setDataMonth(month);
  }, [year, month, setDataYear, setDataMonth]);

  // Auto-select first available month when available months change
  useEffect(() => {
    const availableMonthsForYear = availableMonths[year] || [];
    if (availableMonthsForYear.length > 0) {
      // If current month is not in available months, select the first available month
      if (!availableMonthsForYear.includes(month)) {
        console.log('Current month not available, selecting first available month:', availableMonthsForYear[0]);
        setMonth(availableMonthsForYear[0]);
      }
    }
  }, [availableMonths, year, month]);


  // Auto-select first employee and log rosterData
  useEffect(() => {
    console.log('MobileRoster rosterData:', rosterData);
    if (rosterData && rosterData.length > 0) {
      // If no employee is selected or the selected employee is not in the current roster data
      const employeeExists = rosterData.some(emp => emp.employeeId === selectedEmployeeId);
      if (!selectedEmployeeId || !employeeExists) {
        console.log('Setting selected employee to:', rosterData[0].employeeId);
        setSelectedEmployeeId(rosterData[0].employeeId);
      }
    }
  }, [rosterData, selectedEmployeeId]);

  // Get shifts for selected employee
  const getEmployeeShifts = () => {
    if (!selectedEmployeeId || !rosterData) return {};
    const employee = rosterData.find((emp) => emp.employeeId === selectedEmployeeId);
    if (!employee) return {};

    // Extract shifts from employee data (format is different in useFetchRosterGen)
    const shiftMap = {};
    for (let i = 1; i <= 31; i++) {
      const dayStr = i.toString();
      if (employee[dayStr]) {
        shiftMap[dayStr] = employee[dayStr];
      }
    }
    return shiftMap;
  };

  // Custom tile content for calendar
  const tileContent = ({ date, view }) => {
    if (view !== 'month') return null;
    const day = date.getDate().toString();
    const shifts = getEmployeeShifts();
    const shift = shifts[day];
    if (!shift) return null;

    return (
      <div
        className="h-8 flex items-center justify-center text-xs font-semibold rounded-full transition-transform transform hover:scale-110"
        style={{
          backgroundColor: shiftColors[shift] || '#d1d5db',
          color: getContrastTextColor(shiftColors[shift] || '#d1d5db'),
        }}
      >
        {shift}
      </div>
    );
  };

  // Calculate contrast text color for shift markers
  const getContrastTextColor = (bgColor) => {
    if (!bgColor) return theme === 'light' ? '#1f2937' : '#e5e7eb';
    const hex = bgColor.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
    return luminance > 0.5 ? '#1f2937' : '#ffffff';
  };

  // Memoize the safeShift variables to prevent unnecessary re-renders
  const safeShiftTypes = useMemo(() => {
    return shiftTypes && Array.isArray(shiftTypes) ? shiftTypes : [];
  }, [shiftTypes]);

  const safeShiftHours = useMemo(() => {
    return shiftHours && typeof shiftHours === 'object' ? shiftHours : {};
  }, [shiftHours]);

  const safeShiftColors = useMemo(() => {
    return shiftColors && typeof shiftColors === 'object' ? shiftColors : {};
  }, [shiftColors]);

  // Log shift data for debugging
  useEffect(() => {
    console.log('Shift data:', { safeShiftTypes, safeShiftHours, safeShiftColors });
  }, [safeShiftTypes, safeShiftHours, safeShiftColors]);

  // Define tileDisabled function to disable clicking on dates
  const tileDisabled = () => true;

  return (
    <div
      className={`min-h-screen pt-32 pb-6 px-3 flex flex-col items-center ${
        theme === 'light' ? 'bg-gray-200' : 'bg-gray-800'
      }`}
      style={{
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23${theme === 'light' ? 'd1d5db' : '4b5563'}' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30v4h2V4h4V2h-4V0h-2zm-12 4h2v4h4v2h-4v4h-2v-4h-4V8h4V4z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
      }}
    >
      {/* Custom CSS to change day numbers color to black */}
      <style>
        {`
          .react-calendar__month-view__days__day {
            color: #000000 !important; /* Force day numbers to be black */
          }
          .react-calendar__month-view__days__day--weekend {
            color: #000000 !important; /* Ensure weekend days are also black */
          }
          .react-calendar__month-view__days__day--neighboringMonth {
            color: #000000 !important; /* Ensure neighboring month days are also black */
          }
        `}
      </style>
      {/* Header */}
      <h1
        className={`text-2xl font-extrabold mb-4 animate-fade-in ${
          theme === 'light' ? 'text-gray-900' : 'text-gray-100'
        }`}
      >
        Mobile Roster
      </h1>

      {/* Selectors */}
      <div
        className={`w-full max-w-[340px] rounded-xl shadow-md p-4 mb-4 ${
          theme === 'light' ? 'bg-white' : 'bg-gray-700'
        }`}
      >
        {/* Year Selector */}
        <div className="mb-3">
          <label
            className={`block text-xs font-semibold mb-1 ${
              theme === 'light' ? 'text-gray-700' : 'text-gray-200'
            }`}
          >
            Year
          </label>
          <select
            value={year}
            onChange={(e) => setYear(Number(e.target.value))}
            disabled={yearsLoading}
            className={`w-full p-2 border rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
              theme === 'light'
                ? 'border-gray-300 bg-white text-gray-900'
                : 'border-gray-600 bg-gray-600 text-gray-100'
            }`}
          >
            {yearsLoading ? (
              <option>Loading...</option>
            ) : (
              availableYears.map((y) => (
                <option key={y} value={y}>
                  {y}
                </option>
              ))
            )}
          </select>
        </div>

        {/* Month Selector */}
        <div className="mb-3">
          <label
            className={`block text-xs font-semibold mb-1 ${
              theme === 'light' ? 'text-gray-700' : 'text-gray-200'
            }`}
          >
            Month
          </label>
          <select
            value={month}
            onChange={(e) => {
              const newMonth = Number(e.target.value);
              console.log('Month selected:', newMonth);
              // Force a re-fetch even if the same month is selected
              if (newMonth === month) {
                fetchRosterData();
              }
              setMonth(newMonth);
            }}
            disabled={monthsLoading}
            className={`w-full p-2 border rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
              theme === 'light'
                ? 'border-gray-300 bg-white text-gray-900'
                : 'border-gray-600 bg-gray-600 text-gray-100'
            }`}
          >
            {monthsLoading ? (
              <option>Loading...</option>
            ) : (availableMonths[year] || []).length > 0 ? (
              (availableMonths[year] || []).map((m) => (
                <option key={m} value={m}>
                  {new Date(year, m - 1).toLocaleString('default', { month: 'long' })}
                </option>
              ))
            ) : (
              <option value="">No months available</option>
            )}
          </select>
        </div>

        {/* Employee Selector */}
        <div>
          <label
            className={`block text-xs font-semibold mb-1 ${
              theme === 'light' ? 'text-gray-700' : 'text-gray-200'
            }`}
          >
            Select Name
          </label>
          <select
            value={selectedEmployeeId || ''}
            onChange={(e) => {
              const newEmployeeId = e.target.value;
              console.log('Employee selected:', newEmployeeId);
              // Force a re-render even if the same employee is selected
              if (newEmployeeId === selectedEmployeeId) {
                // Create a temporary copy of the shifts to force a re-render
                const employee = rosterData.find(emp => emp.employeeId === selectedEmployeeId);
                if (employee) {
                  // This will trigger the useEffect that depends on selectedEmployeeId
                  setSelectedEmployeeId(null);
                  setTimeout(() => setSelectedEmployeeId(newEmployeeId), 10);
                }
              } else {
                setSelectedEmployeeId(newEmployeeId);
              }
            }}
            disabled={rosterLoading}
            className={`w-full p-2 border rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
              theme === 'light'
                ? 'border-gray-300 bg-white text-gray-900'
                : 'border-gray-600 bg-gray-600 text-gray-100'
            }`}
          >
            {rosterLoading ? (
              <option>Loading...</option>
            ) : rosterData && rosterData.length > 0 ? (
              rosterData.map((emp) => (
                <option key={emp.employeeId} value={emp.employeeId}>
                  {emp.Name} ({emp['Staff/SRC#']})
                </option>
              ))
            ) : (
              <option value="">No employees available</option>
            )}
          </select>
        </div>

        {/* Theme Toggle */}
        <div className="mt-3 flex justify-end">
          <button
            onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
            className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
              theme === 'light'
                ? 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                : 'bg-gray-600 text-gray-200 hover:bg-gray-500'
            }`}
          >
            {theme === 'light' ? 'Dark Mode' : 'Light Mode'}
          </button>
        </div>
      </div>

      {/* Calendar */}
      <div className="w-full max-w-[340px]">
        {shiftLoading || rosterLoading ? (
          <div
            className={`text-center animate-pulse ${
              theme === 'light' ? 'text-gray-600' : 'text-gray-400'
            }`}
          >
            Loading calendar...
          </div>
        ) : selectedEmployeeId ? (
          <Calendar
            value={calendarDate}
            tileContent={tileContent}
            tileDisabled={tileDisabled} // Disable clicking on dates
            className={`w-full rounded-xl shadow-md p-3 ${
              theme === 'light' ? 'bg-gray-100' : 'bg-gray-900'
            }`}
            calendarType="gregory"
            prevLabel={null}  // Disable previous month navigation
            nextLabel={null}  // Disable next month navigation
            prev2Label={null}
            next2Label={null}
            onClick={() => {}}
            onChange={() => {}}
            maxDetail="month" // Prevent zooming out to year/decade view
            minDetail="month" // Prevent zooming in beyond month view
            tileClassName="text-sm"
          />
        ) : (
          <div
            className={`text-center ${theme === 'light' ? 'text-gray-600' : 'text-gray-400'}`}
          >
            Select an employee to view shifts
          </div>
        )}
      </div>

      {/* Shift Legend */}
      {safeShiftTypes.length > 0 && (
        <div className="w-full max-w-[340px] mt-4">
          <h3
            className={`text-xs font-semibold mb-2 ${
              theme === 'light' ? 'text-gray-700' : 'text-gray-200'
            }`}
          >
            Shift Legend
          </h3>
          <div className="grid grid-cols-2 gap-2">
            {safeShiftTypes.map((shift) => (
              <div key={shift} className="flex items-center space-x-2 animate-fade-in">
                <div
                  className="w-4 h-4 rounded-full shadow-sm"
                  style={{ backgroundColor: safeShiftColors[shift] || '#d1d5db' }}
                ></div>
                <span
                  className={`text-xs font-medium ${
                    theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                  }`}
                >
                  {shift} ({safeShiftHours[shift] || '0'})
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default MobileRoster;