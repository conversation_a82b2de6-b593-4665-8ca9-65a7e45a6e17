// src/hooks/RosterTable/useRosterMode.js
import { useState, useEffect } from 'react';

/**
 * Custom hook to manage roster table mode
 * 
 * @param {boolean} isRosterDraft - Whether the roster is in draft mode
 * @returns {Array} [mode, setMode] - The current mode and a function to set it
 */
const useRosterMode = (isRosterDraft = true) => {
  // Initialize mode based on draft status
  const [mode, setMode] = useState(isRosterDraft ? 'update' : 'view');

  // Ensure mode is 'view' when not in draft mode
  useEffect(() => {
    if (!isRosterDraft && mode !== 'view') {
      console.log('Switching to view mode');
      setMode('view');
    }
  }, [isRosterDraft, mode]);

  // Return mode and setMode function
  return [mode, setMode];
};

export default useRosterMode;
