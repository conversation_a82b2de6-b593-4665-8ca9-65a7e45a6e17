import { useState, useContext, useEffect } from 'react';
import { getDatabase, ref, get, set } from 'firebase/database';
import { AuthContext } from '../components/AuthProvider';
import { getDaysInMonth } from '../utils/dateUtils';

const useAddRoster = () => {
    const { userDepartment, selectedDepartment, isRegionManager, isSuperAdmin, isSuperUser, isRegionUser } = useContext(AuthContext);

    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [departmentPath, setDepartmentPath] = useState('');

    // Set department path when department changes
    useEffect(() => {
      const deptToUse = isRegionManager || isSuperAdmin() || isSuperUser || isRegionUser ? selectedDepartment : userDepartment;
      if (deptToUse) {
        const path = `departments/${deptToUse}/`;
        console.log('useAddRoster: Setting department path:', path);
        setDepartmentPath(path);
      } else {
        console.log('useAddRoster: No department found, using empty path');
        setDepartmentPath('');
      }
    }, [userDepartment, selectedDepartment, isRegionManager, isSuperAdmin, isSuperUser, isRegionUser]);

    const addRoster = async (targetYear, targetMonth) => {
      setLoading(true);
      setError(null);

      // Check if department path is set
      if (!departmentPath) {
        const errorMsg = 'Department path not set, cannot add roster';
        console.warn('useAddRoster: ' + errorMsg);
        setError(errorMsg);
        setLoading(false);
        return { success: false, error: errorMsg };
      }

      try {
        const db = getDatabase();
        console.log('useAddRoster: Adding roster for department path:', departmentPath);

        // Determine previous month
        let prevYear = targetYear;
        let prevMonth = targetMonth - 1;
        if (prevMonth < 1) {
          prevMonth = 12;
          prevYear--;
        }

        // Fetch previous month's roster from department
        const prevRosterRef = ref(db, `${departmentPath}rostersDraft/${prevYear}/${prevMonth}`);
        console.log('useAddRoster: Fetching previous roster from path:', `${departmentPath}rostersDraft/${prevYear}/${prevMonth}`);
        const prevSnapshot = await get(prevRosterRef);
        if (!prevSnapshot.exists()) {
          throw new Error(`No roster found for ${new Date(prevYear, prevMonth - 1).toLocaleString('default', { month: 'long' })} ${prevYear} in department ${userDepartment}`);
        }

        const prevRoster = prevSnapshot.val();
        const prevEmployeeShifts = prevRoster.employeeShifts || {};

        // Get days in target and previous months
        const targetDays = getDaysInMonth(targetYear, targetMonth);
        const prevDays = getDaysInMonth(prevYear, prevMonth);

        // Prepare new roster
        const newEmployeeShifts = {};
        Object.entries(prevEmployeeShifts).forEach(([employeeId, data]) => {
          const newShifts = {};
          // Copy shifts up to the target month's length
          for (let day = 1; day <= targetDays; day++) {
            const dayStr = day.toString();
            // Only copy if the day exists in the previous month
            newShifts[dayStr] = day <= prevDays ? data.shifts?.[dayStr] || null : null;
          }

          newEmployeeShifts[employeeId] = {
            name: data.name || 'Unknown',
            srcNumber: data.srcNumber || '',
            position: data.position || 'Technician',
            rowIndex: data.rowIndex || 0,
            shifts: newShifts,
            leaveDays: data.leaveDays || 0, // Copy leaveDays
            sickDays: 0, // Reset
            overtimeDays: 0, // Reset
            overtime: '0:00', // Reset
            aircraft: '0:00', // Reset
            office: '0:00', // Reset
          };
        });

        // Write new roster to Firebase in department
        const newRosterRef = ref(db, `${departmentPath}rostersDraft/${targetYear}/${targetMonth}`);
        console.log('useAddRoster: Creating new roster at path:', `${departmentPath}rostersDraft/${targetYear}/${targetMonth}`);
        await set(newRosterRef, { employeeShifts: newEmployeeShifts });

        console.log(`Created roster for ${departmentPath}rostersDraft/${targetYear}/${targetMonth} with ${targetDays} days, copied from ${departmentPath}rostersDraft/${prevYear}/${prevMonth}`);

        return { success: true };
      } catch (err) {
        setError(err.message);
        console.error('Error creating roster:', err);
        return { success: false, error: err.message };
      } finally {
        setLoading(false);
      }
    };

    return { addRoster, loading, error };
  };

  export default useAddRoster;