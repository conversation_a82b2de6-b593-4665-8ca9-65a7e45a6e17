//src/components/RosterTable/RosterRow.js
import React, { memo } from 'react';

// Utility to check if an employee is new (no shifts assigned)
const isNewEmployee = (employee) => {
  return !Object.keys(employee).some(key => /^\d+$/.test(key));
};

const RosterRow = memo(({ tech, idx, selection, dragging, mode, handleCellClick, handleCellMouseDown, handleCellMouseEnter, handleDragStart, handleDragOver, handleDrop, handleShiftChange, dropdownPosition, setDropdownPosition, shiftTypes, shiftColors, changedCells, daysInMonth, minRow, maxRow, minCol, maxCol, techIds, multiSelection, dropTarget, lastSelected }) => {
  const isNew = isNewEmployee(tech);

  return (
    <tr key={tech.employeeId} style={{ backgroundColor: idx % 2 === 0 ? '#f9fafb' : 'white' }}>
      <td draggable={false} style={{ border: '1px solid #d1d5db', padding: '0.5rem 1rem', width: 'auto', minWidth: '150px', whiteSpace: 'nowrap', height: '40px', textAlign: 'left' }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          {tech['Name']}
          {isNew && (
            <span
              title="New employee: No shifts scheduled yet"
              style={{ marginLeft: '0.5rem', color: '#f59e0b', cursor: 'help' }}
            >
              ⚠️
            </span>
          )}
        </div>
      </td>
      <td draggable={false} style={{ border: '1px solid #d1d5db', padding: '0.5rem 1rem', fontWeight: '500', width: '120px', minWidth: '120px', height: '40px' }}>{tech['Staff/SRC#']}</td>
      <td draggable={false} style={{ border: '1px solid #d1d5db', padding: '0.5rem 1rem', width: '80px', minWidth: '80px', height: '40px' }}>{tech['Position']}</td>
      {Array.from({ length: daysInMonth }, (_, i) => {
        const day = (i + 1).toString();
        const dayNum = parseInt(day);
        // Determine if cell is within rectangular selection
        let isSelected = false;
        let isMultiSelected = false;
        if (mode === 'drag' && selection.start.techId && selection.end.techId && multiSelection.length === 0) {
          const rowIdx = techIds.indexOf(tech.employeeId);
          isSelected = rowIdx >= minRow && rowIdx <= maxRow && dayNum >= minCol && dayNum <= maxCol;
        }
        if (mode === 'drag' && multiSelection.length > 0 && multiSelection.some(cell => cell.techId === tech.employeeId && cell.day === dayNum)) {
          isMultiSelected = true;
        }
        const shift = tech[day]; // No default value
        const isMissing = shift === undefined && !isNew;
        const isDropdownOpen = dropdownPosition.techId === tech.employeeId && dropdownPosition.day === day;
        const isChanged = changedCells.some(cell => cell.techId === tech.employeeId && cell.day === day);

        // Visual feedback: selection outline, drop target, active cell, smooth transitions
        const isDropTarget = dropTarget && dropTarget.techId === tech.employeeId && dropTarget.day === dayNum;
        const isActiveCell = lastSelected && lastSelected.techId === tech.employeeId && parseInt(lastSelected.day) === dayNum;
        return (
          <td
            key={i}
            draggable={mode === 'drag' && isSelected}
            onMouseDown={mode === 'drag' ? (e) => handleCellMouseDown(tech.employeeId, day, e) : undefined}
            onMouseEnter={mode === 'drag' ? () => handleCellMouseEnter(tech.employeeId, day) : undefined}
            onClick={mode === 'update' || mode === 'dialog' ? (e) => handleCellClick(tech.employeeId, day, e) : undefined}
            onDragStart={(e) => handleDragStart(e, tech.employeeId, dayNum, isSelected, isMultiSelected)}
            onDragOver={(e) => handleDragOver(e, tech.employeeId, dayNum)}
            onDrop={(e) => handleDrop(e, tech.employeeId, day)}
            style={{
              border: isDropTarget
                ? '2.5px solid #0074D9'
                : isActiveCell
                  ? '2.5px solid #2563eb'
                  : isMultiSelected
                    ? '2px solid #3b82f6'
                    : isSelected
                      ? '2px solid #f87171'
                      : '1px solid #d1d5db',
              boxShadow: isDropTarget ? '0 0 6px 2px #0074D9' : undefined,
              transition: 'border 0.18s, background 0.18s, box-shadow 0.18s',
              padding: '0.25rem 0.5rem',
              textAlign: 'center',
              cursor: mode === 'drag' && isSelected ? 'grab' : 'pointer',
              background: isDropTarget
                ? '#e0f2fe'
                : isChanged && (isSelected || isMultiSelected)
                  ? 'linear-gradient(90deg, #ff9500 0%, #ff9500 50%, #bde0fe 50%, #bde0fe 100%)'
                  : isChanged
                    ? '#ff9500'
                    : isMultiSelected
                      ? '#bde0fe'
                      : isSelected
                        ? '#ffcccb'
                        : isNew
                          ? '#f3f4f6'
                          : isMissing
                            ? '#fee2e2'
                            : (shiftColors[shift] || 'transparent'),
              opacity: dragging && !isSelected ? 0.7 : 1,
              width: '40px',
              minWidth: '40px',
              height: '40px',
              position: 'relative',
              zIndex: isDropTarget ? 10 : undefined,
            }}
          >
            {isNew ? (
              <span title="No shifts scheduled" style={{ color: '#6b7280' }}>
                --
              </span>
            ) : isMissing ? (
              <span title="Shift data missing for this day" style={{ color: '#b91c1c' }}>
                ?
              </span>
            ) : (
              shift || ''
            )}
            {isDropdownOpen && (
              <div
                style={{
                  position: 'absolute',
                  top: '100%',
                  left: 0,
                  zIndex: '10',
                  backgroundColor: 'white',
                  border: '1px solid #d1d5db',
                  borderRadius: '0.25rem',
                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                  maxHeight: '150px',
                  overflowY: 'auto',
                }}
              >
                {shiftTypes.map((shiftOption) => (
                  <div
                    key={shiftOption}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleShiftChange(tech.employeeId, day, shiftOption);
                    }}
                    style={{
                      padding: '0.25rem 0.5rem',
                      cursor: 'pointer',
                      backgroundColor: shiftOption === shift ? '#e5e7eb' : 'white',
                      ':hover': { backgroundColor: '#f3f4f6' },
                    }}
                  >
                    {shiftOption}
                  </div>
                ))}
              </div>
            )}
          </td>
        );
      })}
      <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', height: '40px' }}>{tech['Leave days'] || 0}</td>
      <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', height: '40px' }}>{tech['Sick days'] || 0}</td>
      <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', height: '40px' }}>{tech['Work days'] || 0}</td>
      <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', height: '40px' }}>{tech['Work Time'] || '0:00'}</td>
      <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', height: '40px' }}>{tech['Overtime days'] || 0}</td>
      <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', height: '40px' }}>{tech['Overtime'] || '0:00'}</td>
      <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', height: '40px' }}>{tech['Aircraft'] || '0:00'}</td>
      <td style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', height: '40px' }}>{tech['Office'] || '0:00'}</td>
    </tr>
  );
});

export default RosterRow;
