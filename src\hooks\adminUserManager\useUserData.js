import { useState, useEffect, useMemo, useCallback, useContext } from 'react';
import { ref, set, get, onValue } from 'firebase/database';
import { sendPasswordResetEmail, onAuthStateChanged } from 'firebase/auth';
import { auth, database } from '../../firebaseConfig';
import { toast } from 'react-toastify';
import { AuthContext } from '../../components/AuthProvider';

/**
 * Custom hook for handling user data management and CRUD operations
 * Extracted from AdminUserManager.js to improve maintainability
 */
export const useUserData = (propEmployees, departmentPath) => {
  const { userDepartment, selectedDepartment, isSuperAdmin, isRegionManager, isSuperUser, isRegionUser } = useContext(AuthContext);

  // State management
  const [selectedEmployee, setSelectedEmployee] = useState('');
  const [users, setUsers] = useState([]);
  const [localEmployees, setLocalEmployees] = useState(propEmployees || []);
  const [isAuthReady, setIsAuthReady] = useState(false);
  const [duplicateEmails, setDuplicateEmails] = useState([]);
  const [actionLoading, setActionLoading] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  // Wait for auth state to settle
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (user) {
        console.log('User signed in:', user.uid);
      } else {
        console.warn('No user signed in. Please sign in to test deletion.');
        toast.warn('Sign in to test user management features.');
      }
      setIsAuthReady(true);
    }, (error) => {
      console.error('Auth state error:', error);
      toast.error('Failed to initialize authentication.');
      setIsAuthReady(true);
    });
    return () => unsubscribe();
  }, []);

  // Update localEmployees based on propEmployees
  useEffect(() => {
    console.log('propEmployees received:', propEmployees?.map(emp => ({ employeeId: emp.employeeId, name: emp.name, email: emp.email })));

    if (!propEmployees) {
      console.warn('propEmployees is null or undefined, setting empty array...');
      setLocalEmployees([]);
      return;
    }

    setLocalEmployees(propEmployees);
    console.log('Updated localEmployees:', propEmployees.map(emp => ({ employeeId: emp.employeeId, name: emp.name, email: emp.email })));

    // Detect duplicate emails
    const emailCounts = {};
    propEmployees.forEach(emp => {
      if (emp.email && emp.email !== '') {
        emailCounts[emp.email] = (emailCounts[emp.email] || 0) + 1;
      }
    });
    const duplicates = Object.entries(emailCounts)
      .filter(([_, count]) => count > 1)
      .map(([email]) => email);
    setDuplicateEmails(duplicates);
  }, [propEmployees]);

  // Fetch users data
  useEffect(() => {
    console.log('Fetching users with department path:', departmentPath);

    // Only fetch department-specific data if we have a department path
    if (!departmentPath) {
      console.log('Waiting for department path to be set before fetching users');
      return () => {}; // Return empty cleanup function
    }

    // Determine which department to filter by
    const targetDepartment = isRegionManager || isSuperAdmin() || isSuperUser || isRegionUser
      ? selectedDepartment
      : userDepartment;

    if (!targetDepartment) {
      console.warn('No department selected or assigned, cannot fetch users');
      setUsers([]);
      return () => {}; // Return empty cleanup function
    }

    const usersRef = ref(database, 'users');
    const unsubscribeUsers = onValue(
      usersRef,
      (snapshot) => {
        const data = snapshot.val();
        const allUsers = data
          ? Object.entries(data)
              .map(([uid, user]) => ({
                uid,
                email: user.email || 'Unknown',
                role: user.role || 'user',
                department: user.department || null,
              }))
          : [];

        // Filter users by department
        const userList = allUsers.filter((user) => {
          const hasValidData = user.uid && user.email !== 'Unknown';
          const belongsToDepartment = user.department === targetDepartment;
          return hasValidData && belongsToDepartment;
        });

        if (data) {
          console.log(`Department filtering results for ${targetDepartment}:`, {
            totalUsersInDatabase: Object.keys(data).length,
            usersInTargetDepartment: userList.length,
            usersWithoutDepartment: allUsers.filter(u => !u.department).length,
            targetDepartment: targetDepartment
          });
        }

        console.log(`Filtered users for department ${targetDepartment}:`, userList);
        setUsers(userList);
      },
      (error) => {
        console.error('Error fetching users:', error);
        toast.error('Failed to load users.');
      }
    );

    return () => {
      unsubscribeUsers();
    };
  }, [departmentPath, selectedDepartment, userDepartment, isRegionManager, isSuperAdmin, isSuperUser, isRegionUser]);

  // Map employees to select options, filtering out those who already have user accounts
  const employeeOptions = useMemo(() => {
    console.log('Recalculating employeeOptions:', localEmployees.map(emp => ({ employeeId: emp.employeeId, name: emp.name, email: emp.email })));

    // Get list of emails that already have user accounts
    const existingUserEmails = new Set(users.map(user => user.email.toLowerCase()));

    const availableEmployees = localEmployees
      .filter((emp) => emp && emp.email && emp.email !== '')
      .filter((emp) => !existingUserEmails.has(emp.email.toLowerCase())) // Filter out existing users
      .map((emp) => ({
        employeeId: emp.employeeId,
        name: emp.name,
        email: emp.email,
      }))
      .sort((a, b) => a.name.localeCompare(b.name));

    console.log('Available employees (not yet users):', availableEmployees.length, 'out of', localEmployees.length);
    console.log('Existing user emails:', Array.from(existingUserEmails));

    return availableEmployees;
  }, [localEmployees, users]);

  const selectedEmail = employeeOptions.find((opt) => opt.name === selectedEmployee)?.email || '';

  // ✅ User CRUD Operations
  const handleAddUser = useCallback(async (isSubmitting, isInitializing) => {
    if (isSubmitting || isInitializing || isLoading || !selectedEmail) {
      if (!selectedEmail) toast.error('Please select an employee with a valid email.');
      return;
    }

    setIsLoading(true);
    try {
      const token = await auth.currentUser.getIdToken();
      const addUserUrl = process.env.REACT_APP_ADD_USER_FUNCTION_URL;

      if (!addUserUrl) {
        throw new Error('ADD_USER_FUNCTION_URL is not configured. Please check your environment variables.');
      }

      const response = await fetch(addUserUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: selectedEmail,
          displayName: selectedEmployee,
        }),
      });

      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.error || 'Failed to add user');
      }

      // Set all required user information
      const currentDept = isRegionManager || isSuperAdmin() || isSuperUser || isRegionUser ? selectedDepartment : userDepartment;
      if (result.uid) {
        // Set all user properties individually
        const userRef = ref(database, `users/${result.uid}`);
        const userData = {
          department: currentDept,
          role: 'user', // Default role
          managedDepartments: [], // Empty array for managedDepartments
          email: selectedEmail, // Ensure email is stored
          displayName: selectedEmployee, // Ensure displayName is stored
          uid: result.uid // Store the uid
        };

        await set(userRef, userData);
        console.log('User created with complete information:', {
          uid: result.uid,
          email: selectedEmail,
          displayName: selectedEmployee,
          department: currentDept,
          role: 'user',
          managedDepartments: []
        });
      }

      await sendPasswordResetEmail(auth, selectedEmail);
      toast.success(`User created for ${selectedEmployee}. A password reset email has been sent.`);
      setSelectedEmployee('');
    } catch (error) {
      if (error.message.includes('email-already-in-use')) {
        toast.error('This email is already registered. Please delete the existing account or contact support.');
      } else if (error.message.includes('too-many-requests')) {
        toast.error('Too many attempts. Please try again later.');
      } else {
        toast.error(`Failed to create user: ${error.message}`);
      }
      console.error('Error creating user:', error);
    } finally {
      setIsLoading(false);
    }
  }, [selectedEmail, selectedEmployee, isLoading, isRegionManager, isSuperAdmin, isSuperUser, isRegionUser, selectedDepartment, userDepartment]);

  const handleResetPassword = useCallback(
    async (email, uid, isSubmitting, isInitializing) => {
      if (isSubmitting || isInitializing || actionLoading[uid]) {
        return;
      }

      if (!window.confirm(`Send password reset email to ${email}?`)) return;

      setActionLoading((prev) => ({ ...prev, [uid]: true }));
      try {
        await sendPasswordResetEmail(auth, email);
        toast.success(`Password reset email sent to ${email}.`);
      } catch (error) {
        if (error.code === 'auth/too-many-requests') {
          toast.error('Too many attempts. Please try again later.');
        } else {
          toast.error('Failed to send password reset email.');
        }
        console.error('Error sending password reset:', error);
      } finally {
        setActionLoading((prev) => ({ ...prev, [uid]: false }));
      }
    },
    [actionLoading]
  );

  const handleDeleteUser = useCallback(
    async (uid, email, isSubmitting, isInitializing, canDeleteUser, getCurrentUserRole, getDeletionBlockReason) => {
      if (isSubmitting || isInitializing || actionLoading[uid]) {
        return;
      }

      if (!uid) {
        toast.error('Cannot delete user: Invalid user ID.');
        console.error('Invalid UID in handleDeleteUser:', { uid, email });
        return;
      }

      const currentUserRole = getCurrentUserRole();
      const targetUser = users.find(u => u.uid === uid);

      // Use permission checks from usePermissions hook
      if (!canDeleteUser(currentUserRole, targetUser, uid)) {
        const reason = getDeletionBlockReason(currentUserRole, targetUser, uid);
        toast.error(reason || 'Cannot delete this user.');
        return;
      }

      const deleteUrl = `${process.env.REACT_APP_DELETE_USER_FUNCTION_URL}?uid=${uid}`;
      if (!deleteUrl.includes('http')) {
        toast.error('DELETE_USER_FUNCTION_URL is not configured. Please check your environment variables.');
        console.error('Invalid DELETE_USER_FUNCTION_URL:', deleteUrl);
        return;
      }

      if (!window.confirm(`Delete user ${email}? This action cannot be undone.`)) return;

      setActionLoading((prev) => ({ ...prev, [uid]: true }));
      try {
        const token = await auth.currentUser.getIdToken();
        const response = await fetch(deleteUrl, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        const result = await response.json();
        if (!response.ok) {
          throw new Error(result.error || 'Failed to delete user');
        }

        console.log('Delete user response:', result);
        toast.success(`User ${email} deleted.`);
      } catch (error) {
        if (error.message.includes('Failed to fetch')) {
          toast.error('Failed to delete user: Network or CORS error. Check server configuration.');
        } else {
          toast.error(`Failed to delete user: ${error.message}`);
        }
        console.error('Error deleting user:', error, { uid, email, deleteUrl });
      } finally {
        setActionLoading((prev) => ({ ...prev, [uid]: false }));
      }
    },
    [actionLoading, users]
  );

  return {
    // State
    selectedEmployee,
    setSelectedEmployee,
    users,
    localEmployees,
    isAuthReady,
    duplicateEmails,
    actionLoading,
    isLoading,
    employeeOptions,
    selectedEmail,

    // Actions
    handleAddUser,
    handleResetPassword,
    handleDeleteUser
  };
};
