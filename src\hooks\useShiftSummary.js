const useShiftSummary = (localRosterData, daysInMonth) => {
  const shiftSummary = {
    Day: Array(daysInMonth).fill(0),
    Night: Array(daysInMonth).fill(0),
    N1: Array(daysInMonth).fill(0),
    N2: Array(daysInMonth).fill(0),
  };

  localRosterData.forEach(tech => {
    for (let day = 1; day <= daysInMonth; day++) {
      const shift = tech[day.toString()]; // No default value
      const dayIndex = day - 1;
      if (shift) { // Only count defined shifts
        if (shift === 'D1') shiftSummary.Day[dayIndex]++;
        if (['N1', 'N2'].includes(shift)) shiftSummary.Night[dayIndex]++;
        if (shift === 'N1') shiftSummary.N1[dayIndex]++;
        if (shift === 'N2') shiftSummary.N2[dayIndex]++;
      }
    }
  });

  const shiftTotals = {
    Day: shiftSummary.Day.reduce((a, b) => a + b, 0),
    Night: shiftSummary.Night.reduce((a, b) => a + b, 0),
    N1: shiftSummary.N1.reduce((a, b) => a + b, 0),
    N2: shiftSummary.N2.reduce((a, b) => a + b, 0),
  };

  return { shiftSummary, shiftTotals };
};

export default useShiftSummary;