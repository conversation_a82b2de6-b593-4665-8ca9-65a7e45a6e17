// src/contexts/useDataProvider.js
import { useEffect, useCallback, useRef } from 'react';
import { useDataDispatch, useRosterData } from './DataContext.jsx';
import {
  fetchRosterData,
  fetchAvailableYears,
  fetchAvailableMonths,
  fetchEmployeesData,
  fetchShiftsData,
  fetchContactInfo,
  fetchCalendarMarkers,
  setRosterYear,
  setRosterMonth,
  toggleRosterDraft,
  addEmployee,
  updateEmployee,
  deleteEmployee,
  updateShift,
  deleteShift,
  updateContactInfo,
  deleteContactInfo
} from './dataActions';

/**
 * Custom hook to initialize and manage data fetching
 * @returns {Object} Data fetching and management functions
 */
export const useDataProvider = () => {
  const dispatch = useDataDispatch();
  const { year, month, isDraftRoster } = useRosterData();
  const cleanupFunctions = useRef({});

  // Initialize data fetching
  useEffect(() => {
    // Fetch initial data
    try {
      fetchAvailableYears(dispatch, isDraftRoster);
      fetchEmployeesData(dispatch, false); // Use cache if available
      fetchShiftsData(dispatch, false); // Use cache if available
      fetchCalendarMarkers(dispatch, false); // Use cache if available
    } catch (err) {
      console.error('Error initializing data:', err);
    }

    // Set up subscriptions that need cleanup
    fetchContactInfo(dispatch, false) // Use cache if available
      .then(cleanup => {
        if (typeof cleanup === 'function') {
          cleanupFunctions.current.contactInfo = cleanup;
        } else {
          cleanupFunctions.current.contactInfo = () => {};
        }
      })
      .catch(err => {
        console.error('Error setting up contact info subscription:', err);
        cleanupFunctions.current.contactInfo = () => {};
      });

    // Cleanup function
    return () => {
      // Clean up any subscriptions
      Object.values(cleanupFunctions.current).forEach(cleanup => {
        if (typeof cleanup === 'function') {
          cleanup();
        }
      });
    };
  }, [dispatch, isDraftRoster]);

  // Function to fetch available years with an override for isDraftRoster
  const fetchAvailableYearsWithOverride = useCallback((overrideIsDraftRoster = null) => {
    try {
      fetchAvailableYears(dispatch, isDraftRoster, overrideIsDraftRoster);
    } catch (err) {
      console.error('Error fetching available years:', err);
    }
  }, [dispatch, isDraftRoster]);

  // Function to fetch available months with an override for isDraftRoster
  const fetchAvailableMonthsWithOverride = useCallback((year, overrideIsDraftRoster = null) => {
    try {
      fetchAvailableMonths(dispatch, year, isDraftRoster, overrideIsDraftRoster);
    } catch (err) {
      console.error('Error fetching available months:', err);
    }
  }, [dispatch, isDraftRoster]);

  // Fetch available months when year changes
  useEffect(() => {
    try {
      fetchAvailableMonths(dispatch, year, isDraftRoster);
    } catch (err) {
      console.error('Error fetching available months:', err);
    }
  }, [dispatch, year, isDraftRoster]);

  // Fetch roster data when year, month, or isDraftRoster changes
  useEffect(() => {
    // Clean up previous roster data subscription if it exists
    if (typeof cleanupFunctions.current.rosterData === 'function') {
      cleanupFunctions.current.rosterData();
    }

    // Set up new subscription
    fetchRosterData(dispatch, year, month, isDraftRoster)
      .then(cleanup => {
        if (typeof cleanup === 'function') {
          cleanupFunctions.current.rosterData = cleanup;
        } else {
          cleanupFunctions.current.rosterData = () => {};
        }
      })
      .catch(err => {
        console.error('Error setting up roster data subscription:', err);
        cleanupFunctions.current.rosterData = () => {};
      });

    // Cleanup function
    return () => {
      if (typeof cleanupFunctions.current.rosterData === 'function') {
        cleanupFunctions.current.rosterData();
      }
    };
  }, [dispatch, year, month, isDraftRoster]);

  // Function to fetch roster data with an override for isDraftRoster
  const fetchRosterDataWithOverride = useCallback((overrideIsDraftRoster = null) => {
    // Clean up previous subscription
    if (typeof cleanupFunctions.current.rosterData === 'function') {
      cleanupFunctions.current.rosterData();
    }

    // Set up new subscription with the override
    fetchRosterData(
      dispatch,
      year,
      month,
      isDraftRoster,
      overrideIsDraftRoster
    )
      .then(cleanup => {
        if (typeof cleanup === 'function') {
          cleanupFunctions.current.rosterData = cleanup;
        } else {
          cleanupFunctions.current.rosterData = () => {};
        }
      })
      .catch(err => {
        console.error('Error setting up roster data subscription:', err);
        cleanupFunctions.current.rosterData = () => {};
      });
  }, [dispatch, year, month, isDraftRoster]);

  // Roster actions
  const changeYear = useCallback((newYear) => {
    setRosterYear(dispatch, newYear);
  }, [dispatch]);

  const changeMonth = useCallback((newMonth) => {
    setRosterMonth(dispatch, newMonth);
  }, [dispatch]);

  const toggleDraftMode = useCallback(() => {
    toggleRosterDraft(dispatch);
  }, [dispatch]);

  const refetchRosterData = useCallback(() => {
    // Clean up previous subscription
    if (typeof cleanupFunctions.current.rosterData === 'function') {
      cleanupFunctions.current.rosterData();
    }

    // Set up new subscription
    fetchRosterData(dispatch, year, month, isDraftRoster)
      .then(cleanup => {
        if (typeof cleanup === 'function') {
          cleanupFunctions.current.rosterData = cleanup;
        } else {
          cleanupFunctions.current.rosterData = () => {};
        }
      })
      .catch(err => {
        console.error('Error setting up roster data subscription:', err);
        cleanupFunctions.current.rosterData = () => {};
      });
  }, [dispatch, year, month, isDraftRoster]);

  // Employee actions
  const addNewEmployee = useCallback((employeeData) => {
    return addEmployee(dispatch, employeeData);
  }, [dispatch]);

  const updateExistingEmployee = useCallback((employeeId, employeeData) => {
    return updateEmployee(dispatch, employeeId, employeeData);
  }, [dispatch]);

  const removeEmployee = useCallback((employeeId) => {
    return deleteEmployee(dispatch, employeeId);
  }, [dispatch]);

  const refetchEmployeesData = useCallback((forceRefresh = true) => {
    fetchEmployeesData(dispatch, forceRefresh);
  }, [dispatch]);

  // Shift actions
  const updateShiftData = useCallback((shiftName, shiftData) => {
    return updateShift(dispatch, shiftName, shiftData);
  }, [dispatch]);

  const removeShift = useCallback((shiftName) => {
    return deleteShift(dispatch, shiftName);
  }, [dispatch]);

  const refetchShiftsData = useCallback((forceRefresh = true) => {
    fetchShiftsData(dispatch, forceRefresh);
  }, [dispatch]);

  // Contact info actions
  const updateContactInfoData = useCallback((id, data) => {
    return updateContactInfo(dispatch, id, data);
  }, [dispatch]);

  const removeContactInfo = useCallback((id) => {
    return deleteContactInfo(dispatch, id);
  }, [dispatch]);

  const refetchContactInfo = useCallback((forceRefresh = true) => {
    // Clean up previous subscription
    if (typeof cleanupFunctions.current.contactInfo === 'function') {
      cleanupFunctions.current.contactInfo();
    }

    // Set up new subscription
    fetchContactInfo(dispatch, forceRefresh)
      .then(cleanup => {
        if (typeof cleanup === 'function') {
          cleanupFunctions.current.contactInfo = cleanup;
        } else {
          cleanupFunctions.current.contactInfo = () => {};
        }
      })
      .catch(err => {
        console.error('Error setting up contact info subscription:', err);
        cleanupFunctions.current.contactInfo = () => {};
      });
  }, [dispatch]);

  // Calendar markers actions
  const refetchCalendarMarkers = useCallback((forceRefresh = true) => {
    fetchCalendarMarkers(dispatch, forceRefresh);
  }, [dispatch]);

  // Cache management
  const clearCache = useCallback(() => {
    // Import dynamically to avoid circular dependencies
    const dataCache = require('../utils/cacheUtils').default;
    dataCache.clear();
    console.log('Data cache cleared');
  }, []);

  return {
    // Roster actions
    changeYear,
    changeMonth,
    toggleDraftMode,
    refetchRosterData,
    fetchRosterDataWithOverride,
    fetchAvailableYearsWithOverride,
    fetchAvailableMonthsWithOverride,

    // Employee actions
    addNewEmployee,
    updateExistingEmployee,
    removeEmployee,
    refetchEmployeesData,

    // Shift actions
    updateShiftData,
    removeShift,
    refetchShiftsData,

    // Contact info actions
    updateContactInfoData,
    removeContactInfo,
    refetchContactInfo,

    // Calendar markers actions
    refetchCalendarMarkers,

    // Cache management
    clearCache
  };
};

export default useDataProvider;
