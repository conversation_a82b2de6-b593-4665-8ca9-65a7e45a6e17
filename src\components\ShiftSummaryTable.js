import React from 'react';
import PropTypes from 'prop-types';

function ShiftSummaryTable({ shiftSummary, shiftTotals, daysInMonth }) {
  // Approximate width of RosterTable's non-shift columns (Name, Staff/SRC#, Position)
  const rosterTableOffset = 300; // 150px + 120px + 80px + padding/borders

  return (
    <div
      style={{
        overflowX: 'auto',
        marginBottom: '2rem',
        position: 'relative',
        maxWidth: `${rosterTableOffset + 40 * (daysInMonth + 1) + 60}px`, // Shift label + days + total
        marginLeft: `${rosterTableOffset}px`, // Align with RosterTable's shift columns
      }}
    >
      <table
        style={{
          borderCollapse: 'collapse',
          border: '1px solid #d1d5db',
          tableLayout: 'fixed', // Ensure consistent column widths
          width: 'auto', // Fit content
        }}
      >
        <thead>
          <tr style={{ backgroundColor: '#e5e7eb' }}>
            <th
              style={{
                border: '1px solid #d1d5db',
                padding: '0.5rem 0.5rem',
                width: '60px', // Compact for shift labels
                textAlign: 'left',
              }}
            >
              Shift
            </th>
            {Array.from({ length: daysInMonth }, (_, i) => (
              <th
                key={i}
                style={{
                  border: '1px solid #d1d5db',
                  padding: '0.25rem 0.5rem',
                  textAlign: 'center',
                  width: '40px', // Match RosterTable
                  minWidth: '40px',
                }}
              >
                {i + 1}
              </th>
            ))}
            <th
              style={{
                border: '1px solid #d1d5db',
                padding: '0.25rem 0.5rem',
                textAlign: 'center',
                width: '40px', // Match RosterTable
                minWidth: '40px',
              }}
            >
              Total
            </th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td
              style={{
                border: '1px solid #d1d5db',
                padding: '0.25rem 0.5rem',
                textAlign: 'left',
                backgroundColor: '#e5e7eb',
                width: '60px',
              }}
            >
              Day
            </td>
            {shiftSummary.Day.map((count, i) => (
              <td
                key={i}
                style={{
                  border: '1px solid #d1d5db',
                  padding: '0.25rem 0.5rem',
                  textAlign: 'center',
                  backgroundColor: count >= 3 ? '#ccffcc' : '#f7c8c8',
                  width: '40px',
                }}
              >
                {count}
              </td>
            ))}
            <td
              style={{
                border: '1px solid #d1d5db',
                padding: '0.25rem 0.5rem',
                textAlign: 'center',
                backgroundColor: '#e5e7eb',
                width: '40px',
              }}
            >
              {shiftTotals.Day}
            </td>
          </tr>
          <tr>
            <td
              style={{
                border: '1px solid #d1d5db',
                padding: '0.25rem 0.5rem',
                textAlign: 'left',
                backgroundColor: '#e5e7eb',
                width: '60px',
              }}
            >
              Night
            </td>
            {shiftSummary.Night.map((count, i) => (
              <td
                key={i}
                style={{
                  border: '1px solid #d1d5db',
                  padding: '0.25rem 0.5rem',
                  textAlign: 'center',
                  backgroundColor: count >= 4 ? '#ccffcc' : '#f7c8c8',
                  width: '40px',
                }}
              >
                {count}
              </td>
            ))}
            <td
              style={{
                border: '1px solid #d1d5db',
                padding: '0.25rem 0.5rem',
                textAlign: 'center',
                backgroundColor: '#e5e7eb',
                width: '40px',
              }}
            >
              {shiftTotals.Night}
            </td>
          </tr>
          <tr>
            <td
              style={{
                border: '1px solid #d1d5db',
                padding: '0.25rem 0.5rem',
                textAlign: 'left',
                backgroundColor: '#e5e7eb',
                width: '60px',
              }}
            >
              N1
            </td>
            {shiftSummary.N1.map((count, i) => (
              <td
                key={i}
                style={{
                  border: '1px solid #d1d5db',
                  padding: '0.25rem 0.5rem',
                  textAlign: 'center',
                  backgroundColor: count < 3 ? '#ccffcc' : 'transparent',
                  width: '40px',
                }}
              >
                {count}
              </td>
            ))}
            <td
              style={{
                border: '1px solid #d1d5db',
                padding: '0.25rem 0.5rem',
                textAlign: 'center',
                backgroundColor: '#e5e7eb',
                width: '40px',
              }}
            >
              {shiftTotals.N1}
            </td>
          </tr>
          <tr>
            <td
              style={{
                border: '1px solid #d1d5db',
                padding: '0.25rem 0.5rem',
                textAlign: 'left',
                backgroundColor: '#e5e7eb',
                width: '60px',
              }}
            >
              N2
            </td>
            {shiftSummary.N2.map((count, i) => (
              <td
                key={i}
                style={{
                  border: '1px solid #d1d5db',
                  padding: '0.25rem 1.1rem',
                  textAlign: 'center',
                  backgroundColor: count < 1 ? '#f7c8c8' : 'transparent',
                  width: '40px',
                }}
              >
                {count}
              </td>
            ))}
            <td
              style={{
                border: '1px solid #d1d5db',
                padding: '0.25rem 0.5rem',
                textAlign: 'center',
                backgroundColor: '#e5e7eb',
                width: '40px',
              }}
            >
              {shiftTotals.N2}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  );
}

ShiftSummaryTable.propTypes = {
  shiftSummary: PropTypes.shape({
    Day: PropTypes.arrayOf(PropTypes.number).isRequired,
    Night: PropTypes.arrayOf(PropTypes.number).isRequired,
    N1: PropTypes.arrayOf(PropTypes.number).isRequired,
    N2: PropTypes.arrayOf(PropTypes.number).isRequired,
  }).isRequired,
  shiftTotals: PropTypes.shape({
    Day: PropTypes.number.isRequired,
    Night: PropTypes.number.isRequired,
    N1: PropTypes.number.isRequired,
    N2: PropTypes.number.isRequired,
  }).isRequired,
  daysInMonth: PropTypes.number.isRequired,
};

export default ShiftSummaryTable;