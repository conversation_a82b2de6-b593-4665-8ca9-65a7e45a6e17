// src/hooks/usePublishRoster.js
import { useState, useContext } from 'react';
import updateRosterDataGen from '../updateRosterDataGen';
import { AuthContext } from '../components/AuthProvider';

/**
 * Custom hook to manage roster publishing functionality
 * @param {number} year - Current year
 * @param {number} month - Current month
 * @param {Array} localRosterData - Current roster data
 * @returns {Object} State and functions for publishing rosters
 */
const usePublishRoster = (year, month, localRosterData) => {
  // Get auth context values
  const authContext = useContext(AuthContext);

  const [isPublishModalOpen, setIsPublishModalOpen] = useState(false);
  const [publishResult, setPublishResult] = useState(null);
  const [isPublishing, setIsPublishing] = useState(false);

  /**
   * Handle publish changes - opens the publish modal
   */
  const handlePublishChanges = () => {
    if (localRosterData.length === 0) {
      setPublishResult({ success: false, error: 'No roster data to publish' });
      setIsPublishModalOpen(true);
      return;
    }
    setIsPublishModalOpen(true);
    setPublishResult(null);
  };

  /**
   * Confirm publishing changes - updates the roster data
   */
  const confirmPublishChanges = async () => {
    setIsPublishing(true);
    try {
      // Pass authContext and selectedDepartment to updateRosterDataGen
      await updateRosterDataGen(year, month, localRosterData, true, authContext, authContext.selectedDepartment);
      setPublishResult({ success: true });
    } catch (err) {
      setPublishResult({ success: false, error: err.message });
      console.error('Error publishing roster:', err);
      alert(`Error publishing roster: ${err.message}`);
    } finally {
      setIsPublishing(false);
    }
  };

  /**
   * Close the publish modal
   */
  const closePublishModal = () => {
    setIsPublishModalOpen(false);
    setPublishResult(null);
  };

  return {
    isPublishModalOpen,
    publishResult,
    isPublishing,
    handlePublishChanges,
    confirmPublishChanges,
    closePublishModal,
  };
};

export default usePublishRoster;
