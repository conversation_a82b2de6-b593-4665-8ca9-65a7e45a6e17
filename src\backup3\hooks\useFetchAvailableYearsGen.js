import { useState, useEffect, useMemo } from 'react';
import { getDatabase, ref, get } from 'firebase/database';

export default function useFetchAvailableYearsGen(isDraftRoster) {
  const [availableYears, setAvailableYears] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    console.log('useFetchAvailableYearsGen: Starting fetch', { isDraftRoster });
    const fetchYears = async () => {
      try {
        const db = getDatabase();
        const path = isDraftRoster ? 'rostersDraft' : 'rosters';
        console.log('useFetchAvailableYearsGen: Querying', path);
        const snapshot = await get(ref(db, path));
        console.log('useFetchAvailableYearsGen: Snapshot', snapshot.exists() ? snapshot.val() : null);
        const data = snapshot.exists() ? Object.keys(snapshot.val()).map(Number) : [];
        console.log('useFetchAvailableYearsGen: Processed years', data);
        setAvailableYears(data.sort((a, b) => a - b));
      } catch (err) {
        console.error('useFetchAvailableYearsGen: Error', err);
        setError(err);
      } finally {
        setLoading(false);
      }
    };
    fetchYears();
  }, [isDraftRoster]);

  return useMemo(() => ({
    availableYears,
    loading,
    error
  }), [availableYears, loading, error]);
}