// src/EmployeePage.js
import React, { useState, useCallback, useMemo, useContext } from 'react';
import EmployeeTable from './components/EmployeeTable';
import EmployeeModals from './components/EmployeeModals';
import AdminUserManager from './components/AdminUserManager';
import ContactInfoSection from './components/ContactInfoSection';
import ShiftsSection from './components/ShiftsSection';
import CalendarMarkersSection from './components/CalendarMarkersSection';
import UpdateEmployeeModal from './components/modals/UpdateEmployeeModal';
import DeleteEmployeeModal from './components/modals/DeleteEmployeeModal';
import { toast } from 'react-toastify';
import { previewEmployeeUpdate, previewEmployeeDelete } from './contexts/actions/employeeActions';
import { AuthContext } from './components/AuthProvider';

// Import data provider hooks
import {
  useEmployeeData,
  useShiftData,
  useContactInfo
} from './contexts/DataContext.jsx';
import useDataProvider from './contexts/useDataProvider';

const EmployeePage = () => {
  // Get auth context for selectedDepartment
  const { selectedDepartment } = useContext(AuthContext);

  // Use data from the DataProvider
  const {
    data: employees,
    loading: employeeLoading,
    error: employeeError
  } = useEmployeeData();

  const {
    data: contactInfo,
    loading: contactLoading,
    error: contactError
  } = useContactInfo();

  const {
    data: shifts,
    loading: shiftLoading,
    error: shiftError
  } = useShiftData();

  // Use data provider actions
  const {
    addNewEmployee: addEmployee,
    updateExistingEmployee: updateEmployee,
    removeEmployee: deleteEmployee,
    updateContactInfoData: updateContactInfo,
    updateContactInfoData: addContactInfo,
    removeContactInfo: deleteContactInfo,
    updateShiftData: addShift,
    updateShiftData: updateShift,
    removeShift: deleteShift
  } = useDataProvider();

  const [isAddEmployeeModalOpen, setIsAddEmployeeModalOpen] = useState(false);
  const [isEditEmployeeModalOpen, setIsEditEmployeeModalOpen] = useState(false);
  const [currentEmployee, setCurrentEmployee] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [positionFilter, setPositionFilter] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);

  // New modal states for confirmation modals
  const [isUpdateConfirmModalOpen, setIsUpdateConfirmModalOpen] = useState(false);
  const [isDeleteConfirmModalOpen, setIsDeleteConfirmModalOpen] = useState(false);
  const [updatePreviewData, setUpdatePreviewData] = useState(null);
  const [deletePreviewData, setDeletePreviewData] = useState(null);
  const [updateResult, setUpdateResult] = useState(null);
  const [deleteResult, setDeleteResult] = useState(null);
  const [pendingUpdateData, setPendingUpdateData] = useState(null);

  // Memoize employees to prevent unnecessary re-renders
  const memoizedEmployees = useMemo(() => employees, [employees]);

  // Combine loading and error states
  const loading = employeeLoading || contactLoading || shiftLoading;
  const error = employeeError || contactError || shiftError;

  // const handleInitializeRowIndex = useCallback(async () => {
  //   if (isSubmitting || isInitializing) return;
  //   setIsInitializing(true);
  //   try {
  //     await initializeRowIndexInRosters();
  //     toast.success('Row indices initialized successfully!');
  //   } catch (err) {
  //     toast.error(err.message);
  //   } finally {
  //     setIsInitializing(false);
  //   }
  // }, [isSubmitting, isInitializing, initializeRowIndexInRosters]);
// Log renders to track frequency

  const handleAddEmployee = useCallback(
    async (employeeData) => {
      setIsSubmitting(true);
      try {
        await addEmployee(employeeData);
        toast.success('Employee added.');
      } catch (err) {
        toast.error(err.message);
      } finally {
        setIsSubmitting(false);
      }
    },
    [addEmployee]
  );

  const handleUpdateEmployee = useCallback(
    async (employeeId, updatedData) => {
      try {
        // Get preview data and show confirmation modal
        const preview = await previewEmployeeUpdate(employeeId, updatedData, selectedDepartment);
        setUpdatePreviewData(preview);
        setPendingUpdateData({ employeeId, updatedData });
        setUpdateResult(null);
        setIsUpdateConfirmModalOpen(true);
        setIsEditEmployeeModalOpen(false); // Close edit modal
      } catch (err) {
        toast.error('Failed to preview update: ' + err.message);
      }
    },
    [selectedDepartment]
  );

  const confirmUpdateEmployee = useCallback(
    async () => {
      if (!pendingUpdateData) return;

      setIsSubmitting(true);
      try {
        await updateEmployee(pendingUpdateData.employeeId, pendingUpdateData.updatedData);
        setUpdateResult({ success: true });
      } catch (err) {
        setUpdateResult({ success: false, error: err.message });
      } finally {
        setIsSubmitting(false);
      }
    },
    [updateEmployee, pendingUpdateData]
  );

  const handleDeleteEmployee = useCallback(
    async (employeeId) => {
      try {
        // Get preview data and show confirmation modal
        const preview = await previewEmployeeDelete(employeeId, selectedDepartment);
        setDeletePreviewData(preview);
        setDeleteResult(null);
        setIsDeleteConfirmModalOpen(true);
      } catch (err) {
        toast.error('Failed to preview delete: ' + err.message);
      }
    },
    [selectedDepartment]
  );

  const confirmDeleteEmployee = useCallback(
    async () => {
      if (!deletePreviewData) return;

      setIsSubmitting(true);
      try {
        await deleteEmployee(deletePreviewData.employee.employeeId);
        setDeleteResult({ success: true });
      } catch (err) {
        setDeleteResult({ success: false, error: err.message });
      } finally {
        setIsSubmitting(false);
      }
    },
    [deleteEmployee, deletePreviewData]
  );

  const closeUpdateModal = useCallback(() => {
    setIsUpdateConfirmModalOpen(false);
    setUpdatePreviewData(null);
    setUpdateResult(null);
    setPendingUpdateData(null);
  }, []);

  const closeDeleteModal = useCallback(() => {
    setIsDeleteConfirmModalOpen(false);
    setDeletePreviewData(null);
    setDeleteResult(null);
  }, []);

  const handleUpdateContactInfo = useCallback(
    async (key, phone) => {
      setIsSubmitting(true);
      try {
        await updateContactInfo(key, phone);
        toast.success('Contact info updated.');
      } catch (err) {
        toast.error(err.message);
      } finally {
        setIsSubmitting(false);
      }
    },
    [updateContactInfo]
  );

  const handleAddContactInfo = useCallback(
    async (key, phone) => {
      setIsSubmitting(true);
      try {
        await addContactInfo(key, phone);
        toast.success('Contact info added.');
      } catch (err) {
        toast.error(err.message);
      } finally {
        setIsSubmitting(false);
      }
    },
    [addContactInfo]
  );

  const handleDeleteContactInfo = useCallback(
    async (key) => {
      setIsSubmitting(true);
      try {
        await deleteContactInfo(key);
        toast.success('Contact info deleted.');
      } catch (err) {
        toast.error(err.message);
      } finally {
        setIsSubmitting(false);
      }
    },
    [deleteContactInfo]
  );

  const handleAddShift = useCallback(
    async (shiftName, shiftData) => {
      setIsSubmitting(true);
      try {
        await addShift(shiftName, shiftData);
        toast.success('Shift added.');
      } catch (err) {
        toast.error(err.message);
      } finally {
        setIsSubmitting(false);
      }
    },
    [addShift]
  );

  const handleUpdateShift = useCallback(
    async (shiftName, shiftData) => {
      setIsSubmitting(true);
      try {
        await updateShift(shiftName, shiftData);
        toast.success('Shift updated.');
      } catch (err) {
        toast.error(err.message);
      } finally {
        setIsSubmitting(false);
      }
    },
    [updateShift]
  );

  const handleDeleteShift = useCallback(
    async (shiftName) => {
      setIsSubmitting(true);
      try {
        await deleteShift(shiftName);
        toast.success('Shift deleted.');
      } catch (err) {
        toast.error(err.message);
      } finally {
        setIsSubmitting(false);
      }
    },
    [deleteShift]
  );

  if (loading) return <div className="p-4">Loading...</div>;
  if (error) return <div className="p-4 text-red-500">Error: {error}</div>;

  return (
    <div className="p-6 pt-20">
      {(isSubmitting || isInitializing) && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-[1000]">
          <div className="animate-spin rounded-full h-10 w-10 border-t-4 border-blue-600"></div>
        </div>
      )}

      <h1 className="text-2xl font-bold mb-4">Settings</h1>
      <div className="mb-4">

        <button
          onClick={() => setIsAddEmployeeModalOpen(true)}
          className="ml-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400"
          disabled={isSubmitting || isInitializing}
        >
          Add Employee
        </button>
      </div>

      <EmployeeTable
        employees={memoizedEmployees}
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        positionFilter={positionFilter}
        setPositionFilter={setPositionFilter}
        onEditEmployee={(emp) => {
          setCurrentEmployee(emp);
          setIsEditEmployeeModalOpen(true);
        }}
        onDeleteEmployee={handleDeleteEmployee}
        isSubmitting={isSubmitting}
        isInitializing={isInitializing}
      />

      <AdminUserManager
        employees={memoizedEmployees}
        isSubmitting={isSubmitting}
        isInitializing={isInitializing}
      />

      <ContactInfoSection
        contactInfo={contactInfo}
        onUpdateContactInfo={handleUpdateContactInfo}
        onAddContactInfo={handleAddContactInfo}
        onDeleteContactInfo={handleDeleteContactInfo}
        isSubmitting={isSubmitting}
        isInitializing={isInitializing}
      />

      <ShiftsSection
        shifts={shifts}
        onAddShift={handleAddShift}
        onUpdateShift={handleUpdateShift}
        onDeleteShift={handleDeleteShift}
        isSubmitting={isSubmitting}
        isInitializing={isInitializing}
      />
     <CalendarMarkersSection
        isSubmitting={isSubmitting}
        isInitializing={isInitializing}
      />
      <EmployeeModals
        employees={memoizedEmployees}
        isAddModalOpen={isAddEmployeeModalOpen}
        setIsAddModalOpen={setIsAddEmployeeModalOpen}
        isEditModalOpen={isEditEmployeeModalOpen}
        setIsEditModalOpen={setIsEditEmployeeModalOpen}
        currentEmployee={currentEmployee}
        setCurrentEmployee={setCurrentEmployee}
        onAddEmployee={handleAddEmployee}
        onUpdateEmployee={handleUpdateEmployee}
        isSubmitting={isSubmitting}
        isInitializing={isInitializing}
      />

      {/* Update Employee Confirmation Modal */}
      <UpdateEmployeeModal
        isOpen={isUpdateConfirmModalOpen}
        previewData={updatePreviewData}
        updateResult={updateResult}
        isUpdating={isSubmitting}
        closeModal={closeUpdateModal}
        confirmUpdate={confirmUpdateEmployee}
      />

      {/* Delete Employee Confirmation Modal */}
      <DeleteEmployeeModal
        isOpen={isDeleteConfirmModalOpen}
        previewData={deletePreviewData}
        deleteResult={deleteResult}
        isDeleting={isSubmitting}
        closeModal={closeDeleteModal}
        confirmDelete={confirmDeleteEmployee}
      />
    </div>
  );
};

export default EmployeePage;