// src/components/modals/AddEmployeeModal.js
import React from 'react';

/**
 * AddEmployeeModal component - Modal for adding an employee to the roster
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {Object} props.selectedEmployee - The selected employee to add
 * @param {Object} props.addEmployeeResult - Result of adding an employee
 * @param {Array} props.affectedMonths - Months affected by adding the employee
 * @param {boolean} props.addEmployeeLoading - Whether adding an employee is in progress
 * @param {Function} props.closeModal - Function to close the modal
 * @param {Function} props.confirmAddEmployee - Function to confirm adding an employee
 * @param {number} props.year - Current year
 * @param {number} props.month - Current month
 * @returns {JSX.Element|null} AddEmployeeModal component or null if not open
 */
const AddEmployeeModal = ({
  isOpen,
  selectedEmployee,
  addEmployeeResult,
  affectedMonths,
  addEmployeeLoading,
  closeModal,
  confirmAddEmployee,
  year,
  month,
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h2 className="text-xl font-bold mb-4">Add Employee to Roster</h2>
        {!addEmployeeResult ? (
          <>
            <p className="text-sm text-gray-700 mb-4">
              You are about to add <strong>{selectedEmployee?.name}</strong> to the roster for the following months:
            </p>
            <ul className="list-disc pl-5 mb-4 text-sm text-gray-700">
              {affectedMonths.length <= 1 ? (
                <li>{new Date(year, month - 1).toLocaleString('default', { month: 'long' })} {year} (No future rosters available)</li>
              ) : (
                affectedMonths.map(({ year, month }) => (
                  <li key={`${year}-${month}`}>
                    {new Date(year, month - 1).toLocaleString('default', { month: 'long' })} {year}
                  </li>
                ))
              )}
            </ul>
            <p className="text-sm text-gray-700 mb-4">
              This will update the listed months. Do you want to proceed?
            </p>
            {addEmployeeLoading && (
              <div className="flex flex-col items-center justify-center mb-4">
                <p className="text-sm text-gray-600 mb-2">Adding employee...</p>
                <div
                  className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-600"
                  role="status"
                  aria-label="Loading"
                ></div>
              </div>
            )}
            <div className="flex justify-end space-x-2">
              <button
                onClick={closeModal}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                disabled={addEmployeeLoading}
              >
                Cancel
              </button>
              <button
                onClick={confirmAddEmployee}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                disabled={addEmployeeLoading}
              >
                Confirm
              </button>
            </div>
          </>
        ) : addEmployeeResult.success ? (
          <>
            <p className="text-sm text-green-600 mb-4">
              Employee <strong>{selectedEmployee?.name}</strong> was successfully added to the roster.
            </p>
            <div className="flex justify-end">
              <button
                onClick={closeModal}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Close
              </button>
            </div>
          </>
        ) : (
          <>
            <p className="text-sm text-red-600 mb-4">
              Failed to add employee: {addEmployeeResult.error}
            </p>
            <div className="flex justify-end space-x-2">
              <button
                onClick={closeModal}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
              >
                Cancel
              </button>
              <button
                onClick={confirmAddEmployee}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Retry
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default AddEmployeeModal;
