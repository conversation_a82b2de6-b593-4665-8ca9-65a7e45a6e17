# Create necessary directories
New-Item -Path "src/backup2/components/ui" -ItemType Directory -Force
New-Item -Path "src/backup2/components/RosterTable" -ItemType Directory -Force
New-Item -Path "src/backup2/hooks/RosterTable" -ItemType Directory -Force
New-Item -Path "src/backup2/utils" -ItemType Directory -Force

# Move component files
Move-Item -Path "src/components/ErrorBoundary.jsx" -Destination "src/backup2/components/" -Force -ErrorAction SilentlyContinue
Move-Item -Path "src/components/ShiftCell.jsx" -Destination "src/backup2/components/" -Force -ErrorAction SilentlyContinue
Move-Item -Path "src/components/ui/Button.jsx" -Destination "src/backup2/components/ui/" -Force -ErrorAction SilentlyContinue
Move-Item -Path "src/components/UserProfile.js" -Destination "src/backup2/components/" -Force -ErrorAction SilentlyContinue

# Move hooks files
Move-Item -Path "src/hooks/fetchAvailableMonths.js" -Destination "src/backup2/hooks/" -Force -ErrorAction SilentlyContinue
Move-Item -Path "src/hooks/useAvailableEmployees.js" -Destination "src/backup2/hooks/" -Force -ErrorAction SilentlyContinue

# Move RosterTable hooks
Move-Item -Path "src/hooks/RosterTable/useAutoScroll.js" -Destination "src/backup2/hooks/RosterTable/" -Force -ErrorAction SilentlyContinue
Move-Item -Path "src/hooks/RosterTable/useDragAndDrop.js" -Destination "src/backup2/hooks/RosterTable/" -Force -ErrorAction SilentlyContinue
Move-Item -Path "src/hooks/RosterTable/useRosterData.js" -Destination "src/backup2/hooks/RosterTable/" -Force -ErrorAction SilentlyContinue
Move-Item -Path "src/hooks/RosterTable/useRosterHistory.js" -Destination "src/backup2/hooks/RosterTable/" -Force -ErrorAction SilentlyContinue
Move-Item -Path "src/hooks/RosterTable/useRosterModal.js" -Destination "src/backup2/hooks/RosterTable/" -Force -ErrorAction SilentlyContinue
Move-Item -Path "src/hooks/RosterTable/useRosterMode.js" -Destination "src/backup2/hooks/RosterTable/" -Force -ErrorAction SilentlyContinue
Move-Item -Path "src/hooks/RosterTable/useSelection.js" -Destination "src/backup2/hooks/RosterTable/" -Force -ErrorAction SilentlyContinue

# Move root files
Move-Item -Path "src/NavbarUser.js" -Destination "src/backup2/" -Force -ErrorAction SilentlyContinue
Move-Item -Path "src/populateMissingData.js" -Destination "src/backup2/" -Force -ErrorAction SilentlyContinue
Move-Item -Path "src/RosterExport.js" -Destination "src/backup2/" -Force -ErrorAction SilentlyContinue
Move-Item -Path "src/RosterPage.jsx" -Destination "src/backup2/" -Force -ErrorAction SilentlyContinue
Move-Item -Path "src/RosterTable2.js" -Destination "src/backup2/" -Force -ErrorAction SilentlyContinue
Move-Item -Path "src/RosterTablnew.js" -Destination "src/backup2/" -Force -ErrorAction SilentlyContinue
Move-Item -Path "src/updateRosterDataxxx.js" -Destination "src/backup2/" -Force -ErrorAction SilentlyContinue
Move-Item -Path "src/useFetchRosterData.js" -Destination "src/backup2/" -Force -ErrorAction SilentlyContinue

# Move utils files
Move-Item -Path "src/utils/exportUtils.js" -Destination "src/backup2/utils/" -Force -ErrorAction SilentlyContinue

Write-Host "Files moved to backup2 directory successfully!"
