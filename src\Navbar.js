import React, { useContext, useState, useEffect, useRef } from 'react';
import { Link, NavLink, useNavigate, } from 'react-router-dom';
import { AuthContext } from './components/AuthProvider';
import { auth } from './firebaseConfig';
import { signOut } from 'firebase/auth';
import { toast } from 'react-toastify';
import './Navbar.css';
import { FaBars, FaTimes } from 'react-icons/fa';
import { getDatabase, ref, get } from 'firebase/database';
import DepartmentCreationModal from './components/DepartmentCreationModal';

function Navbar() {
  const {
    currentUser,
    userRole,
    isSuperAdmin,
    isRegionManager,
    isSuperUser,
    isRegionUser,
    canSwitchDepartments,
    canCreateDepartments,
    selectedDepartment,
    setSelectedDepartment,
    departments,
    managedDepartments,
    userDepartment,
    setUserDepartment,
    createNewDepartment
  } = useContext(AuthContext);
  const navigate = useNavigate();
 // const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isLargeScreen, setIsLargeScreen] = useState(window.innerWidth >= 769);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isAddDeptModalOpen, setIsAddDeptModalOpen] = useState(false);
  const dropdownRef = useRef(null);

  useEffect(() => {
    const handleResize = () => {
      setIsLargeScreen(window.innerWidth >= 769);
      if (window.innerWidth >= 769) setIsMobileMenuOpen(false);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Close dropdown on outside click (for super-admins)
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSignOut = async () => {
    try {
      await signOut(auth);
      toast.success('Signed out successfully!');
      navigate('/login');
    } catch (error) {
      console.error('Navbar: Sign out error:', error);
      toast.error('Failed to sign out.');
    }
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
    setIsDropdownOpen(false);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
    setIsDropdownOpen(false);
  };

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  const handleDepartmentChange = (deptId) => {
    console.log('Navbar: Changing department to', deptId);
    setSelectedDepartment(deptId);
    setUserDepartment(deptId);

    setIsDropdownOpen(false);
    closeMobileMenu();
  };

  const renderMenuItems = () => {
    const baseMenuItems = [
      { to: '/roster', label: 'Roster', showOnMobile: false },
      { to: '/mobile-roster', label: 'Mobile Roster' },
      { to: '/employee-search', label: 'Search' },
      { to: '/about', label: 'About' },
    ];

    const adminMenuItems = [
      { to: '/', label: 'Home', showOnMobile: false },
      { to: '/employees', label: 'Dashboard', showOnMobile: false },
      { to: '/employees-docs', label: 'Employee Docs', showOnMobile: false },
      ...baseMenuItems,
    ];

    const userMenuItems = [...baseMenuItems];

    let menuItems;
    if (userRole === 'admin' || isSuperAdmin() || isRegionManager) {
      menuItems = adminMenuItems;
    } else if (userRole === 'user' || isSuperUser || isRegionUser) {
      menuItems = userMenuItems;
    } else {
      return null;
    }

    return (
      <ul className="navbar-menu">
        {menuItems.map((item) => (
          (isLargeScreen || item.showOnMobile !== false) && (
            <li key={item.to}>
              <NavLink
                to={item.to}
                className={({ isActive }) => `navbar-link ${isActive ? 'active' : ''}`}
                aria-label={`${item.label} page`}
                onClick={closeMobileMenu}
              >
                {item.label}
              </NavLink>
            </li>
          )
        ))}
        {currentUser && (
          <li>
            <button onClick={handleSignOut} className="navbar-signout mobile-signout">
              Sign Out
            </button>
          </li>
        )}
        {!currentUser && (
          <li>
            <Link to="/login" className="navbar-signin mobile-signin" onClick={closeMobileMenu}>
              Sign In
            </Link>
          </li>
        )}
      </ul>
    );
  };

  const currentDeptName = departments.find(dept => dept.id === (canSwitchDepartments() ? selectedDepartment : userDepartment))?.name || 'Unknown Department';

  return (
    <nav className="navbar">
      <div className="navbar-container">
        <Link to="/" className="navbar-logo" aria-label="Go to home page">
          <img src="./hrm.png" alt="HRM Logo" className="navbar-icon" />
          {isLargeScreen ? 'HRM' : 'HRM'}
        </Link>
        {currentUser && (
          <>
            {canSwitchDepartments() ? (
              <div className="navbar-department" ref={dropdownRef}>
                <button
                  onClick={toggleDropdown}
                  className="navbar-department-button"
                  aria-expanded={isDropdownOpen}
                  aria-label="Select Location"
                >
                  <svg
                    className="navbar-department-icon"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth=""
                      color="blue"
                      d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      color="blue"
                      d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                  </svg>
                  <span>{currentDeptName}</span>
                  <svg
                    className={`navbar-department-chevron ${isDropdownOpen ? 'rotate-180' : ''}`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                {isDropdownOpen && (
                  <ul className="navbar-department-dropdown">
                    {(isSuperAdmin() || isSuperUser ? departments : departments.filter(dept => managedDepartments.includes(dept.id))).map(dept => (
                      <li
                        key={dept.id}
                        className="navbar-department-item"
                        onClick={() => handleDepartmentChange(dept.id)}
                        role="option"
                        aria-selected={selectedDepartment === dept.id}
                      >
                        {dept.name}
                      </li>
                    ))}
                    {/* Add Department Button - For super admin and region manager */}
                    {canCreateDepartments() && (
                      <li
                        className="navbar-department-item navbar-add-department"
                        onClick={() => setIsAddDeptModalOpen(true)}
                        role="option"
                      >
                        <svg
                          className="w-4 h-4 mr-2 inline-block"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                          />
                        </svg>
                        Add New Department
                      </li>
                    )}
                  </ul>
                )}
              </div>
            ) : (
              <div className="navbar-department-static">
                <svg
                  className="navbar-department-icon"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
                <span>{currentDeptName}</span>
              </div>
            )}

            {/* Role indicator below department selector */}
            {currentUser && (
              <div className="navbar-role-indicator">
                <span className={`navbar-role-label ${userRole}`}>
                  {userRole === 'super-admin' ? 'S-Admin' :
                   userRole === 'region-manager' ? 'R-Mgr' :
                   userRole === 'super-user' ? 'S-User' :
                   userRole === 'region-user' ? 'R-User' :
                   userRole === 'admin' ? 'Admin' :
                   'User'}
                </span>
              </div>
            )}
          </>
        )}
        <div className="mobile-menu-icon" onClick={toggleMobileMenu} aria-label="Toggle mobile navigation">
          {isMobileMenuOpen ? <FaTimes /> : <FaBars />}
        </div>
        <div className="navbar-menu-wrapper hidden md:flex">{renderMenuItems()}</div>
        {isMobileMenuOpen && (
          <div className="navbar-mobile-menu md:hidden">
            {renderMenuItems()}
          </div>
        )}
      </div>

      {/* Department Creation Modal */}
      <DepartmentCreationModal
        isOpen={isAddDeptModalOpen}
        onClose={() => setIsAddDeptModalOpen(false)}
        onConfirm={createNewDepartment}
        existingDepartments={departments}
      />
    </nav>
  );
}

export default Navbar;