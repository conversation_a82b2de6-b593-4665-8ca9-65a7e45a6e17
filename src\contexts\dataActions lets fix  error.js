// src/contexts/dataActions.js
import { getDatabase, ref, onValue, get, set, remove, update, push } from 'firebase/database';
import { getAuth } from 'firebase/auth';
import {
  FETCH_ROSTER_START,
  FETCH_ROSTER_SUCCESS,
  <PERSON>ETCH_ROSTER_ERROR,
  SET_ROSTER_YEAR,
  SET_ROSTER_MONTH,
  TOGGLE_ROSTER_DRAFT,
  SET_AVAILABLE_YEARS,
  SET_AVAILABLE_MONTHS,
  FETCH_EMPLOYEES_START,
  FETCH_EMPLOYEES_SUCCESS,
  FETCH_EMPLOYEES_ERROR,
  ADD_EMPLOYEE,
  UPDATE_EMPLOYEE,
  DELETE_EMPLOYEE,
  FETCH_SHIFTS_START,
  FETCH_SHIFTS_SUCCESS,
  FETCH_SHIFTS_ERROR,
  ADD_SHIFT,
  UPDATE_SHIFT,
  DELETE_SHIFT,
  <PERSON>ET<PERSON>_CONTACT_INFO_START,
  <PERSON><PERSON><PERSON>_CONTACT_INFO_SUCCESS,
  <PERSON><PERSON><PERSON>_CONTACT_INFO_ERROR,
  UPDATE_CONTACT_INFO,
  ADD_CONTACT_INFO,
  DELETE_CONTACT_INFO,
  FETCH_CALENDAR_MARKERS_START,
  FETCH_CALENDAR_MARKERS_SUCCESS,
  FETCH_CALENDAR_MARKERS_ERROR
} from './DataContext.jsx';
import dataCache from '../utils/cacheUtils';

// Helper function to get department path
const getDepartmentPath = async (isDepartmentSpecific = true, selectedDepartment = null) => {
  if (!isDepartmentSpecific) 
    {console.log('getDepartmentPath: Not department specific, returning empty path');
    return '';}

  const auth = getAuth();
  const user = auth.currentUser;

  if (!user) return '';

  const db = getDatabase();
  const userRef = ref(db, `users/${user.uid}`);
  const snapshot = await get(userRef);

  if (snapshot.exists()) {
    const userData = snapshot.val();

    // Super-admin uses selectedDepartment if provided
    if (userData.role === 'super-admin' && selectedDepartment) {
      console.log(`getDepartmentPath: Super-admin, using selectedDepartment: ${selectedDepartment}`);
      return `departments/${selectedDepartment}/`;
    }

    // Return the user's department path if available
    return userData.department ? `departments/${userData.department}/` : '';
  }
  console.log('last exit :getDepartmentPath: Not department specific, returning empty path');
  return '';
};

// ===== ROSTER ACTIONS =====

// Set roster year
export const setRosterYear = (dispatch, year) => {
  dispatch({ type: SET_ROSTER_YEAR, payload: year });
};

// Set roster month
export const setRosterMonth = (dispatch, month) => {
  dispatch({ type: SET_ROSTER_MONTH, payload: month });
};

// Toggle roster draft mode
export const toggleRosterDraft = (dispatch) => {
  dispatch({ type: TOGGLE_ROSTER_DRAFT });
};

// Fetch available years with optional override for isDraftRoster
export const fetchAvailableYears = async (dispatch, isDraftRoster, overrideIsDraftRoster = null, selectedDepartment = null) => {
  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);

    const db = getDatabase();
    // Use the override value if provided, otherwise use the context value
    const useDraftRoster = overrideIsDraftRoster !== null ? overrideIsDraftRoster : isDraftRoster;
    const path = useDraftRoster ? `${departmentPath}rostersDraft` : `${departmentPath}rosters`;
    const yearsRef = ref(db, path);

    onValue(
      yearsRef,
      (snapshot) => {
        try {
          if (snapshot.exists()) {
            const years = Object.keys(snapshot.val())
              .filter((year) => !isNaN(parseInt(year)))
              .map((year) => parseInt(year))
              .sort((a, b) => a - b);

            dispatch({ type: SET_AVAILABLE_YEARS, payload: years });
          } else {
            dispatch({ type: SET_AVAILABLE_YEARS, payload: [] });
          }
        } catch (err) {
          console.error('Error fetching available years:', err);
          dispatch({ type: SET_AVAILABLE_YEARS, payload: [] });
        }
      },
      (err) => {
        console.error('Error fetching available years:', err);
        dispatch({ type: SET_AVAILABLE_YEARS, payload: [] });
      }
    );
  } catch (err) {
    console.error('Error getting department path:', err);
    dispatch({ type: SET_AVAILABLE_YEARS, payload: [] });
  }
};

// Fetch available months for a year with optional override for isDraftRoster
export const fetchAvailableMonths = async (dispatch, year, isDraftRoster, overrideIsDraftRoster = null, selectedDepartment = null) => {
  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);

    const db = getDatabase();
    // Use the override value if provided, otherwise use the context value
    const useDraftRoster = overrideIsDraftRoster !== null ? overrideIsDraftRoster : isDraftRoster;
    const path = useDraftRoster ? `${departmentPath}rostersDraft/${year}` : `${departmentPath}rosters/${year}`;
    const monthsRef = ref(db, path);

    onValue(
      monthsRef,
      (snapshot) => {
        try {
          if (snapshot.exists()) {
            const months = Object.keys(snapshot.val())
              .filter((month) => !isNaN(parseInt(month)) && parseInt(month) >= 1 && parseInt(month) <= 12)
              .map((month) => parseInt(month))
              .sort((a, b) => a - b);

            dispatch({
              type: SET_AVAILABLE_MONTHS,
              payload: { year, months }
            });
          } else {
            dispatch({
              type: SET_AVAILABLE_MONTHS,
              payload: { year, months: [] }
            });
          }
        } catch (err) {
          console.error('Error fetching available months:', err);
          dispatch({
            type: SET_AVAILABLE_MONTHS,
            payload: { year, months: [] }
          });
        }
      },
      (err) => {
        console.error('Error fetching available months:', err);
        dispatch({
          type: SET_AVAILABLE_MONTHS,
          payload: { year, months: [] }
        });
      }
    );
  } catch (err) {
    console.error('Error getting department path:', err);
    dispatch({
      type: SET_AVAILABLE_MONTHS,
      payload: { year, months: [] }
    });
  }
};

// Fetch roster data with optional override for isDraftRoster
export const fetchRosterData = async (dispatch, year, month, isDraftRoster, overrideIsDraftRoster = null, selectedDepartment = null) => {
  dispatch({ type: FETCH_ROSTER_START });

  // Use the override value if provided, otherwise use the context value
  const useDraftRoster = overrideIsDraftRoster !== null ? overrideIsDraftRoster : isDraftRoster;

  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);

    const db = getDatabase();
    const path = useDraftRoster
      ? `${departmentPath}rostersDraft/${year}/${month}/employeeShifts`
      : `${departmentPath}rosters/${year}/${month}/employeeShifts`;

    const rosterRef = ref(db, path);

    const unsubscribe = onValue(
      rosterRef,
      (snapshot) => {
        try {
          if (snapshot.exists()) {
            const rawData = snapshot.val();

            // Format the data similar to useFetchRosterGen
            const formattedData = Object.entries(rawData)
              .map(([employeeId, employeeData]) => ({
                employeeId,
                Name: employeeData.name || 'Unknown',
                'Staff/SRC#': employeeData.srcNumber || '',
                Position: employeeData.position || 'Technician',
                rowIndex: employeeData.rowIndex || 0,
                ...employeeData.shifts,
                'Leave days': employeeData.leaveDays || 0,
                'Sick days': employeeData.sickDays || 0,
                'Work days': employeeData.workDays || 0,
                'Work Time': employeeData.workTime || '0:00',
                'Overtime days': employeeData.overtimeDays || 0,
                Overtime: employeeData.overtime || '0:00',
                Aircraft: employeeData.aircraft || '0:00',
                Office: employeeData.office || '0:00',
              }))
              .sort((a, b) => a.rowIndex - b.rowIndex);

            dispatch({ type: FETCH_ROSTER_SUCCESS, payload: formattedData });
          } else {
            dispatch({
              type: FETCH_ROSTER_ERROR,
              payload: new Error('No roster data found for the selected year and month')
            });
          }
        } catch (err) {
          dispatch({
            type: FETCH_ROSTER_ERROR,
            payload: new Error('Failed to fetch roster data: ' + err.message)
          });
        }
      },
      (err) => {
        dispatch({
          type: FETCH_ROSTER_ERROR,
          payload: new Error('Failed to fetch roster data: ' + err.message)
        });
      }
    );

    return () => unsubscribe();
  } catch (error) {
    dispatch({
      type: FETCH_ROSTER_ERROR,
      payload: new Error('Failed to fetch roster data: ' + error.message)
    });
  }
};

// ===== EMPLOYEE ACTIONS =====

// Fetch employees data
export const fetchEmployeesData = async (dispatch, forceRefresh = false, selectedDepartment = null) => {
  dispatch({ type: FETCH_EMPLOYEES_START });

  const cacheKey = `employees_data_${selectedDepartment || 'default'}`;

  // Check cache first unless forced refresh
  if (!forceRefresh) {
    const cachedData = dataCache.get(cacheKey);
    if (cachedData) {
      dispatch({ type: FETCH_EMPLOYEES_SUCCESS, payload: cachedData });
      return;
    }
  }

  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);

    const db = getDatabase();
    const employeesRef = ref(db, `${departmentPath}employees`);

    get(employeesRef)
      .then((snapshot) => {
        if (snapshot.exists()) {
          let employeeData = Object.values(snapshot.val());
          // Sort by rowIndex to maintain correct order
          employeeData = employeeData.sort((a, b) => a.rowIndex - b.rowIndex);

          // Cache the data for future use (30 minutes TTL)
          dataCache.set(cacheKey, employeeData, 30 * 60 * 1000);

          dispatch({ type: FETCH_EMPLOYEES_SUCCESS, payload: employeeData });
        } else {
          dispatch({ type: FETCH_EMPLOYEES_SUCCESS, payload: [] });
        }
      })
      .catch((err) => {
        dispatch({
          type: FETCH_EMPLOYEES_ERROR,
          payload: new Error('Failed to fetch employees data: ' + err.message)
        });
      });
  } catch (error) {
    dispatch({
      type: FETCH_EMPLOYEES_ERROR,
      payload: new Error('Failed to fetch employees data: ' + error.message)
    });
  }
};

// Add employee with optimistic update
export const addEmployee = async (dispatch, employeeData, selectedDepartment = null) => {
  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);

    // Optimistically update the UI immediately
    dispatch({ type: ADD_EMPLOYEE, payload: employeeData });

    // Invalidate the cache
    dataCache.delete(`employees_data_${selectedDepartment || 'default'}`);

    // Perform the actual database update
    const db = getDatabase();
    await set(ref(db, `${departmentPath}employees/${employeeData.employeeId}`), employeeData);

    console.log('Employee added successfully');
    return true;
  } catch (err) {
    console.error('Error adding employee:', err);

    // Fetch fresh data to revert the optimistic update
    fetchEmployeesData(dispatch, true, selectedDepartment);

    return false;
  }
};

// Update employee with optimistic update
export const updateEmployee = async (dispatch, employeeId, employeeData, selectedDepartment = null) => {
  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);

    // Optimistically update the UI immediately
    dispatch({ type: UPDATE_EMPLOYEE, payload: { employeeId, ...employeeData } });

    // Invalidate the cache
    dataCache.delete(`employees_data_${selectedDepartment || 'default'}`);

    // Perform the actual database update
    const db = getDatabase();
    await update(ref(db, `${departmentPath}employees/${employeeId}`), employeeData);

    console.log('Employee updated successfully');
    return true;
  } catch (err) {
    console.error('Error updating employee:', err);

    // Fetch fresh data to revert the optimistic update
    fetchEmployeesData(dispatch, true, selectedDepartment);

    return false;
  }
};

// Delete employee with optimistic update
export const deleteEmployee = async (dispatch, employeeId, selectedDepartment = null) => {
  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);

    // Optimistically update the UI immediately
    dispatch({ type: DELETE_EMPLOYEE, payload: employeeId });

    // Invalidate the cache
    dataCache.delete(`employees_data_${selectedDepartment || 'default'}`);

    // Perform the actual database update
    const db = getDatabase();
    await remove(ref(db, `${departmentPath}employees/${employeeId}`));

    console.log('Employee deleted successfully');
    return true;
  } catch (err) {
    console.error('Error deleting employee:', err);

    // Fetch fresh data to revert the optimistic update
    fetchEmployeesData(dispatch, true, selectedDepartment);

    return false;
  }
};

// ===== SHIFT ACTIONS =====

// Fetch shifts data with caching
export const fetchShiftsData = async (dispatch, forceRefresh = false, selectedDepartment = null) => {
  dispatch({ type: FETCH_SHIFTS_START });

  const cacheKey = `shifts_data_${selectedDepartment || 'default'}`;

  // Check cache first if not forcing a refresh
  if (!forceRefresh) {
    const cachedData = dataCache.get(cacheKey);
    if (cachedData) {
      console.log('Using cached shifts data');
      dispatch({ type: FETCH_SHIFTS_SUCCESS, payload: cachedData });
      return;
    }
  }

  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);

    const db = getDatabase();
    const shiftsRef = ref(db, `${departmentPath}shifts`);

    get(shiftsRef)
      .then((snapshot) => {
        if (snapshot.exists()) {
          const data = snapshot.val();

          // Extract shift types, hours, colors, etc.
          const types = Object.keys(data);
          const hours = {};
          const colors = {};
          const startTimes = {};
          const endTimes = {};

          Object.entries(data).forEach(([key, value]) => {
            hours[key] = value.hours || 0;
            colors[key] = value.color || '#D3D3D3'; // Default gray if color missing
            startTimes[key] = value.startTime || '';
            endTimes[key] = value.endTime || '';
          });

          const payload = { data, types, hours, colors, startTimes, endTimes };

          // Cache the data for future use (1 hour TTL)
          dataCache.set(cacheKey, payload, 60 * 60 * 1000);

          dispatch({ type: FETCH_SHIFTS_SUCCESS, payload });
        } else {
          const emptyPayload = { data: {}, types: [], hours: {}, colors: {}, startTimes: {}, endTimes: {} };
          dispatch({ type: FETCH_SHIFTS_SUCCESS, payload: emptyPayload });
        }
      })
      .catch((err) => {
        dispatch({
          type: FETCH_SHIFTS_ERROR,
          payload: new Error('Failed to fetch shifts data: ' + err.message)
        });
      });
  } catch (err) {
    console.error('Error getting department path:', err);
    dispatch({
      type: FETCH_SHIFTS_ERROR,
      payload: new Error('Failed to get department path: ' + err.message)
    });
  }
};

// Add or update shift with optimistic update
export const updateShift = async (dispatch, shiftName, shiftData, selectedDepartment = null) => {
  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);

    // Optimistically update the UI immediately
    dispatch({ type: UPDATE_SHIFT, payload: { name: shiftName, data: shiftData } });

    // Invalidate the cache
    dataCache.delete(`shifts_data_${selectedDepartment || 'default'}`);

    // Perform the actual database update
    const db = getDatabase();
    await set(ref(db, `${departmentPath}shifts/${shiftName}`), shiftData);

    console.log('Shift updated successfully');
    return true;
  } catch (err) {
    console.error('Error updating shift:', err);

    // Fetch fresh data to revert the optimistic update
    fetchShiftsData(dispatch, true, selectedDepartment);

    return false;
  }
};

// Delete shift with optimistic update
export const deleteShift = async (dispatch, shiftName, selectedDepartment = null) => {
  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);

    // Optimistically update the UI immediately
    dispatch({ type: DELETE_SHIFT, payload: shiftName });

    // Invalidate the cache
    dataCache.delete(`shifts_data_${selectedDepartment || 'default'}`);

    // Perform the actual database update
    const db = getDatabase();
    await remove(ref(db, `${departmentPath}shifts/${shiftName}`));

    console.log('Shift deleted successfully');
    return true;
  } catch (err) {
    console.error('Error deleting shift:', err);

    // Fetch fresh data to revert the optimistic update
    fetchShiftsData(dispatch, true, selectedDepartment);

    return false;
  }
};

// ===== CONTACT INFO ACTIONS =====

// Fetch contact info with caching
export const fetchContactInfo = async (dispatch, forceRefresh = false, selectedDepartment = null) => {
  dispatch({ type: FETCH_CONTACT_INFO_START });

  const cacheKey = `contact_info_${selectedDepartment || 'default'}`;

  // For real-time data, we still use onValue but we can cache the initial data
  if (!forceRefresh) {
    const cachedData = dataCache.get(cacheKey);
    if (cachedData) {
      console.log('Using cached contact info data');
      dispatch({ type: FETCH_CONTACT_INFO_SUCCESS, payload: cachedData });
    }
  }

  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);

    const db = getDatabase();
    const contactInfoRef = ref(db, `${departmentPath}contactInfo`);

    const unsubscribe = onValue(
      contactInfoRef,
      (snapshot) => {
        if (snapshot.exists()) {
          const data = snapshot.val();

          // Cache the data for future use (15 minutes TTL)
          dataCache.set(cacheKey, data, 15 * 60 * 1000);

          dispatch({ type: FETCH_CONTACT_INFO_SUCCESS, payload: data });
        } else {
          dispatch({ type: FETCH_CONTACT_INFO_SUCCESS, payload: {} });
        }
      },
      (err) => {
        dispatch({
          type: FETCH_CONTACT_INFO_ERROR,
          payload: new Error('Failed to fetch contact info: ' + err.message)
        });
      }
    );

    // Return unsubscribe function for cleanup
    return unsubscribe;
  } catch (err) {
    console.error('Error getting department path:', err);
    dispatch({
      type: FETCH_CONTACT_INFO_ERROR,
      payload: new Error('Failed to get department path: ' + err.message)
    });

    // Return a no-op function as a fallback
    return () => {};
  }
};

// Update contact info with optimistic update
export const updateContactInfo = async (dispatch, id, data, selectedDepartment = null) => {
  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);

    // Optimistically update the UI immediately
    dispatch({ type: UPDATE_CONTACT_INFO, payload: { id, data } });

    // Invalidate the cache
    dataCache.delete(`contact_info_${selectedDepartment || 'default'}`);

    // Perform the actual database update
    const db = getDatabase();
    await set(ref(db, `${departmentPath}contactInfo/${id}`), data);

    console.log('Contact info updated successfully');
    return true;
  } catch (err) {
    console.error('Error updating contact info:', err);

    // Since we're using real-time updates with onValue,
    // the UI will automatically revert to the correct state
    return false;
  }
};

// Delete contact info with optimistic update
export const deleteContactInfo = async (dispatch, id, selectedDepartment = ModalContent) => {
  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);

    // Optimistically update the UI immediately
    dispatch({ type: DELETE_CONTACT_INFO, payload: id });

    // Invalidate the cache
    dataCache.delete(`contact_info_${selectedDepartment || 'default'}`);

    // Perform the actual database update
    const db = getDatabase();
    await remove(ref(db, `${departmentPath}contactInfo/${id}`));

    console.log('Contact info deleted successfully');
    return true;
  } catch (err) {
    console.error('Error deleting contact info:', err);

    // Since we're using real-time updates with onValue,
    // the UI will automatically revert to the correct state
    return false;
  }
};

// ===== CALENDAR MARKERS ACTIONS =====

// Fetch calendar markers with caching
export const fetchCalendarMarkers = async (dispatch, forceRefresh = false, selectedDepartment = null) => {
  dispatch({ type: FETCH_CALENDAR_MARKERS_START });

  const cacheKey = `calendar_markers_${selectedDepartment || 'default'}`;

  // Check cache first if not forcing a refresh
  if (!forceRefresh) {
    const cachedData = dataCache.get(cacheKey);
    if (cachedData) {
      console.log('Using cached calendar markers data');
      dispatch({ type: FETCH_CALENDAR_MARKERS_SUCCESS, payload: cachedData });
      return;
    }
  }

  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);

    const db = getDatabase();
    const markersRef = ref(db, `${departmentPath}calendar_markers`);

    get(markersRef)
      .then((snapshot) => {
        console.log('Calendar markers snapshot exists:', snapshot.exists());
        if (snapshot.exists()) {
          const data = snapshot.val();
          console.log('Raw calendar markers data:', data);

          // Map the data to match our expected structure
          const payload = {
            weekdayMarkers: data.weekday_markers || {},
            holidayMarkers: data.holiday_markers || {},
            specialDayMarkers: data.special_day_markers || {}
          };

          console.log('Processed calendar markers payload:', payload);

          // Cache the data for future use (1 day TTL since calendar markers rarely change)
          dataCache.set(cacheKey, payload, 24 * 60 * 60 * 1000);

          dispatch({ type: FETCH_CALENDAR_MARKERS_SUCCESS, payload });
        } else {
          const emptyPayload = {
            weekdayMarkers: {},
            holidayMarkers: {},
            specialDayMarkers: {}
          };
          dispatch({ type: FETCH_CALENDAR_MARKERS_SUCCESS, payload: emptyPayload });
        }
      })
      .catch((err) => {
        dispatch({
          type: FETCH_CALENDAR_MARKERS_ERROR,
          payload: new Error('Failed to fetch calendar markers: ' + err.message)
        });
      });
  } catch (err) {
    console.error('Error getting department path:', err);
    dispatch({
      type: FETCH_CALENDAR_MARKERS_ERROR,
      payload: new Error('Failed to get department path: ' + err.message)
    });
  }
};

// Add calendar marker with optimistic update
export const addCalendarMarker = async (dispatch, markerType, markerData, selectedDepartment = null) => {
  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);
    const cacheKey = `calendar_markers_${selectedDepartment || 'default'}`;

    // Optimistically update the UI
    dispatch({
      type: FETCH_CALENDAR_MARKERS_SUCCESS,
      payload: {
        weekdayMarkers: markerType === 'weekday' ? { ...markerData } : {},
        holidayMarkers: markerType === 'holiday' ? { ...markerData } : {},
        specialDayMarkers: markerType === 'special_day' ? { ...markerData } : {}
      }
    });

    // Invalidate the cache
    dataCache.delete(cacheKey);

    // Perform the actual database update
    const db = getDatabase();
    const markerRef = ref(db, `${departmentPath}calendar_markers/${markerType}_markers`);
    const newMarkerRef = push(markerRef);
    await set(newMarkerRef, markerData);

    console.log('Calendar marker added successfully');
    return true;
  } catch (err) {
    console.error('Error adding calendar marker:', err);

    // Fetch fresh data to revert the optimistic update
    fetchCalendarMarkers(dispatch, true, selectedDepartment);

    return false;
  }
};

// Update calendar marker with optimistic update
export const updateCalendarMarker = async (dispatch, markerType, markerId, markerData, selectedDepartment = null) => {
  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);
    const cacheKey = `calendar_markers_${selectedDepartment || 'default'}`;

    // Optimistically update the UI
    dispatch({
      type: FETCH_CALENDAR_MARKERS_SUCCESS,
      payload: {
        weekdayMarkers: markerType === 'weekday' ? { [markerId]: markerData } : {},
        holidayMarkers: markerType === 'holiday' ? { [markerId]: markerData } : {},
        specialDayMarkers: markerType === 'special_day' ? { [markerId]: markerData } : {}
      }
    });

    // Invalidate the cache
    dataCache.delete(cacheKey);

    // Perform the actual database update
    const db = getDatabase();
    await set(ref(db, `${departmentPath}calendar_markers/${markerType}_markers/${markerId}`), markerData);

    console.log('Calendar marker updated successfully');
    return true;
  } catch (err) {
    console.error('Error updating calendar marker:', err);

    // Fetch fresh data to revert the optimistic update
    fetchCalendarMarkers(dispatch, true, selectedDepartment);

    return false;
  }
};

// Delete calendar marker with optimistic update
export const deleteCalendarMarker = async (dispatch, markerType, markerId, selectedDepartment = null) => {
  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);
    const cacheKey = `calendar_markers_${selectedDepartment || 'default'}`;

    // Optimistically update the UI
    dispatch({
      type: FETCH_CALENDAR_MARKERS_SUCCESS,
      payload: {
        weekdayMarkers: markerType === 'weekday' ? {} : {},
        holidayMarkers: markerType === 'holiday' ? {} : {},
        specialDayMarkers: markerType === 'special_day' ? {} : {}
      }
    });

    // Invalidate the cache
    dataCache.delete(cacheKey);

    // Perform the actual database update
    const db = getDatabase();
    await remove(ref(db, `${departmentPath}calendar_markers/${markerType}_markers/${markerId}`));

    console.log('Calendar marker deleted successfully');
    return true;
  } catch (err) {
    console.error('Error deleting calendar marker:', err);

    // Fetch fresh data to revert the optimistic update
    fetchCalendarMarkers(dispatch, true, selectedDepartment);

    return false;
  }
};

// Update roster data
export const updateRosterData = async (dispatch, year, month, rosterData, isPublish, selectedDepartment = null) => {
  try {
    // Get the department path
    const departmentPath = await getDepartmentPath(true, selectedDepartment);
    console.log('Department path for roster update:', departmentPath);
    
    const db = getDatabase();
    isPublish ? console.log('Publishing live roster') : console.log('Saving draft roster');
    
    const path = isPublish
      ? `${departmentPath}rosters/${year}/${month}`
      : `${departmentPath}rostersDraft/${year}/${month}`;
    console.log('Saving roster to path:', path);
    
    const rosterRef = ref(db, path);
    
    // Transform rosterData into the employeeShifts format expected by Firebase
    const employeeShifts = rosterData.reduce((acc, employee) => {
      const { employeeId, Name, 'Staff/SRC#': srcNumber, Position, 'Leave days': leaveDays, 
             'Sick days': sickDays, 'Work days': workDays, 'Work Time': workTime, 
             'Overtime days': overtimeDays, Overtime: overtime, Aircraft: aircraft, 
             Office: office, rowIndex, ...shifts } = employee;
      
      acc[employeeId] = {
        name: Name,
        srcNumber,
        position: Position,
        leaveDays,
        sickDays,
        workDays,
        workTime,
        overtimeDays,
        overtime,
        aircraft,
        office,
        rowIndex,
        shifts
      };
      return acc;
    }, {});
    
    console.log(`Saving roster data for ${year}/${month}`);
    const updatedAt = new Date().toISOString();
    
    await update(rosterRef, { employeeShifts, updatedAt });
    console.log('Roster data saved successfully');
    
    return true;
  } catch (error) {
    console.error('Error saving roster data:', error);
    throw error; // Let the caller handle the error
  }
};