import React, { useState, useEffect, useCallback, Component, useContext } from 'react';
import PropTypes from 'prop-types';
import { database } from '../firebaseConfig';
import { ref, set, get, onValue } from 'firebase/database';
import { toast } from 'react-toastify';
import { AuthContext } from './AuthProvider';
import RoleEditModal from './RoleEditModal';
import DepartmentAssignmentModal from './modals/DepartmentAssignmentModal';
import { usePermissions } from '../hooks/adminUserManager/usePermissions';
import { useUserData } from '../hooks/adminUserManager/useUserData';
import { useDepartmentManagement } from '../hooks/adminUserManager/useDepartmentManagement';
import { useSubscriberData } from '../hooks/adminUserManager/useSubscriberData';

/**
 * AdminUserManager Component
 *
 * A comprehensive user management interface for administrators with role-based access control.
 * This component has been refactored into multiple custom hooks for better maintainability:
 *
 * - usePermissions: Handles role-based permissions and access control
 * - useUserData: Manages user CRUD operations and employee data
 * - useDepartmentManagement: Handles department assignments for region-managers/users
 * - useSubscriberData: Manages Telegram/WhatsApp subscriber operations
 *
 * Features:
 * - Role-based user management (super-admin, region-manager, admin, etc.)
 * - Department-specific user filtering and management
 * - Modal-based role editing with validation
 * - Department assignment for region-managers and region-users
 * - Telegram/WhatsApp subscriber management
 * - Real-time data synchronization with Firebase
 *
 * REMINDER: Before moving to production, you MUST:
 * 1. Set Firebase Realtime Database rules to secure 'users', 'employees', 'config/maxAdmins', 'telegram_subscribers', and 'whatsapp_subscribers'.
 * 2. Create an admin account in the 'users' database with role: 'admin'.
 * 3. Deploy 'addUser' and 'deleteUser' Cloud Functions.
 */

// Error Boundary Component
class AdminUserManagerErrorBoundary extends Component {
  state = { hasError: false, error: null };

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-4 bg-red-100 text-red-700 rounded-md">
          <h3 className="font-bold">Something went wrong.</h3>
          <p>{this.state.error?.message || 'An unexpected error occurred.'}</p>
          <p>Please try refreshing the page or contact support.</p>
        </div>
      );
    }
    return this.props.children;
  }
}

const AdminUserManager = ({ employees: propEmployees, isSubmitting, isInitializing }) => {
  const { userDepartment, selectedDepartment, isSuperAdmin, isRegionManager, isSuperUser, isRegionUser } = useContext(AuthContext);

  const [maxAdmins, setMaxAdmins] = useState(2);
  const [departmentPath, setDepartmentPath] = useState('');

  // ✅ NEW: Modal state for role editing
  const [editingUser, setEditingUser] = useState(null);
  const [isRoleModalOpen, setIsRoleModalOpen] = useState(false);

  // ✅ REMOVED: Department management state moved to useDepartmentManagement hook

  // ✅ NEW: Use user data hook (must come first since permissions hook depends on users)
  const {
    selectedEmployee,
    setSelectedEmployee,
    users,
    localEmployees,
    isAuthReady,
    duplicateEmails,
    actionLoading,
    isLoading,
    employeeOptions,
    selectedEmail,
    handleAddUser: handleAddUserFromHook,
    handleResetPassword: handleResetPasswordFromHook,
    handleDeleteUser: handleDeleteUserFromHook
  } = useUserData(propEmployees, departmentPath);

  // ✅ NEW: Use permissions hook (depends on users from useUserData)
  const {
    getCurrentUserRole,
    getCurrentUserId,
    canModifyUser,
    canPromoteToRole,
    isProtectedRole,
    canDeleteUser,
    getDeletionBlockReason,
    hasRoleManagementAccess,
    getAllowedRoles
  } = usePermissions(users);

  // ✅ NEW: Use department management hook
  const {
    regionManagersAndUsers,
    setRegionManagersAndUsers,
    myRegionUsers,
    setMyRegionUsers,
    isDepartmentModalOpen,
    departmentModalUser,
    departmentModalType,
    selectedDepartmentForAssignment,
    setSelectedDepartmentForAssignment,
    getRegionManagersAndUsers,
    getMyRegionUsers,
    getAvailableDepartmentsForUser,
    handleAddDepartmentToUser: handleAddDepartmentToUserFromHook,
    handleRemoveDepartmentFromUser: handleRemoveDepartmentFromUserFromHook,
    openDepartmentModal,
    closeDepartmentModal
  } = useDepartmentManagement();

  // ✅ NEW: Use subscriber data hook
  const {
    telegramSubscribers,
    whatsappSubscribers,
    duplicateTelegramIds,
    duplicateWhatsappNumbers,
    subscriberActionLoading,
    handleToggleSubscription: handleToggleSubscriptionFromHook
  } = useSubscriberData(departmentPath);

  // Set department path
  useEffect(() => {
    const deptToUse = isRegionManager || isSuperAdmin() || isSuperUser || isRegionUser ? selectedDepartment : userDepartment;
    if (deptToUse) {
      const path = `departments/${deptToUse}/`;
      setDepartmentPath(path);
    } else {
      setDepartmentPath('');
    }
  }, [userDepartment, selectedDepartment, isRegionManager, isSuperAdmin, isSuperUser, isRegionUser]);

  // ✅ REMOVED: Auth state handling moved to useUserData hook

  // ✅ REMOVED: Employee data handling moved to useUserData hook

  // Fetch users, maxAdmins, telegram_subscribers, and whatsapp_subscribers
  useEffect(() => {
    console.log('Fetching data with department path:', departmentPath);

    // Only fetch department-specific data if we have a department path
    if (!departmentPath) {
      console.log('Waiting for department path to be set before fetching data');
      return () => {}; // Return empty cleanup function
    }

    // Determine which department to filter by
    const targetDepartment = isRegionManager || isSuperAdmin() || isSuperUser || isRegionUser
      ? selectedDepartment
      : userDepartment;

    if (!targetDepartment) {
      console.warn('No department selected or assigned, cannot fetch users');
      // Note: setUsers is handled by useUserData hook
      return () => {}; // Return empty cleanup function
    }

    const usersRef = ref(database, 'users');
    const unsubscribeUsers = onValue(
      usersRef,
      (snapshot) => {
        const data = snapshot.val();
        const allUsers = data
          ? Object.entries(data)
              .map(([uid, user]) => ({
                uid,
                email: user.email || 'Unknown',
                role: user.role || 'user',
                department: user.department || null,
              }))
          : [];

        // Filter users by department
        const userList = allUsers.filter((user) => {
          const hasValidData = user.uid && user.email !== 'Unknown';
          const belongsToDepartment = user.department === targetDepartment;
          return hasValidData && belongsToDepartment;
        });

        if (data) {
          console.log(`Department filtering results for ${targetDepartment}:`, {
            totalUsersInDatabase: Object.keys(data).length,
            usersInTargetDepartment: userList.length,
            usersWithoutDepartment: allUsers.filter(u => !u.department).length,
            targetDepartment: targetDepartment
          });
        }

        console.log(`Filtered users for department ${targetDepartment}:`, userList);
        // Note: setUsers is handled by useUserData hook - this useEffect should be moved there
      },
      (error) => {
        console.error('Error fetching users:', error);
        toast.error('Failed to load users.');
      }
    );

    const maxAdminsRef = ref(database, `${departmentPath}config/maxAdmins`);
    get(maxAdminsRef)
      .then((snapshot) => {
        if (snapshot.exists()) {
          console.log('Found maxAdmins in department config:', snapshot.val());
          setMaxAdmins(snapshot.val());
        } else {
          console.warn(`maxAdmins not set in department ${userDepartment}. Using default: 2`);
          toast.warn('Admin limit not configured. Contact support.');
        }
      })
      .catch((error) => {
        console.error('Error fetching maxAdmins:', error);
        toast.error('Failed to load admin limit.');
      });

    // ✅ REMOVED: Subscriber data fetching moved to useSubscriberData hook

    return () => {
      unsubscribeUsers();
      // ✅ REMOVED: Subscriber unsubscribe functions moved to useSubscriberData hook
    };
  }, [departmentPath, selectedDepartment, userDepartment, isRegionManager, isSuperAdmin, isSuperUser, isRegionUser]);

  // ✅ REMOVED: Employee options calculation moved to useUserData hook

  // ✅ REMOVED: Permission functions moved to usePermissions hook

  // ✅ NEW: Wrapper functions for user management (calling hook functions with correct parameters)
  const handleAddUser = useCallback(async () => {
    await handleAddUserFromHook(isSubmitting, isInitializing);
  }, [handleAddUserFromHook, isSubmitting, isInitializing]);

  const handleResetPassword = useCallback(async (email, uid) => {
    await handleResetPasswordFromHook(email, uid, isSubmitting, isInitializing);
  }, [handleResetPasswordFromHook, isSubmitting, isInitializing]);

  const handleDeleteUser = useCallback(async (uid, email) => {
    await handleDeleteUserFromHook(uid, email, isSubmitting, isInitializing, canDeleteUser, getCurrentUserRole, getDeletionBlockReason);
  }, [handleDeleteUserFromHook, isSubmitting, isInitializing, canDeleteUser, getCurrentUserRole, getDeletionBlockReason]);

  // ✅ REMOVED: Department management functions moved to useDepartmentManagement hook

  // ✅ REMOVED: More department management functions moved to hook

  // ✅ REMOVED: canAssignDepartment moved to hook

  // ✅ REMOVED: All remaining department management functions moved to hook

  // ✅ NEW: Wrapper functions for department management (calling hook functions)
  const handleAddDepartmentToUser = useCallback(async (userId, departmentId) => {
    return await handleAddDepartmentToUserFromHook(userId, departmentId);
  }, [handleAddDepartmentToUserFromHook]);

  const handleRemoveDepartmentFromUser = useCallback(async (userId, departmentId) => {
    return await handleRemoveDepartmentFromUserFromHook(userId, departmentId);
  }, [handleRemoveDepartmentFromUserFromHook]);

  // ✅ REMOVED: More permission functions moved to usePermissions hook

  // ✅ FIXED: Modal helper functions
  const getAvailableRolesForUser = (currentUserRole, targetUser) => {
    // Get all roles that the current user can assign
    const allPossibleRoles = getAllowedRoles(currentUserRole);

    console.log('Debug getAvailableRolesForUser:', {
      currentUserRole,
      targetUser: targetUser.role,
      allPossibleRoles
    });

    return allPossibleRoles
      .filter(role => role !== 'none' && role !== 'all roles') // Filter out display values
      .filter(role => role !== targetUser.role) // Exclude current role
      .filter(role => canPromoteToRole(currentUserRole, role)) // Double-check permission
      .map(role => ({
        value: role,
        label: getRoleDisplayName(role)
      }));
  };

  const getRoleDisplayName = (role) => {
    const roleDisplayNames = {
      'user': 'User',
      'admin': 'Administrator',
      'region-manager': 'Region Manager',
      'super-user': 'Super User',
      'region-user': 'Region User',
      'super-admin': 'Super Administrator'
    };
    return roleDisplayNames[role] || role;
  };

  const handleOpenRoleModal = (user) => {
    console.log('Opening role modal for user:', user);
    console.log('Current user role:', getCurrentUserRole());
    console.log('Available roles:', getAvailableRolesForUser(getCurrentUserRole(), user));
    setEditingUser(user);
    setIsRoleModalOpen(true);
  };

  const handleCloseRoleModal = () => {
    setEditingUser(null);
    setIsRoleModalOpen(false);
  };

  const handleModalRoleSave = async (uid, newRole) => {
    // Use the existing handleRoleChange logic but adapted for modal
    const currentUserRole = getCurrentUserRole();
    const currentUserId = getCurrentUserId();

    // Validation checks
    if (currentUserRole === 'user') {
      throw new Error('Users do not have access to role management.');
    }

    if (!canModifyUser(currentUserRole, editingUser.role)) {
      throw new Error(`You cannot modify users with ${editingUser.role} role.`);
    }

    if (uid === currentUserId && isProtectedRole(editingUser.role)) {
      throw new Error(`You cannot change your own role as ${editingUser.role}.`);
    }

    if (!canPromoteToRole(currentUserRole, newRole)) {
      throw new Error(`You don't have permission to assign ${newRole} role.`);
    }

    // Admin limit check
    if (newRole === 'admin') {
      const currentAdmins = users.filter((u) => u.role === 'admin').length;
      if (currentAdmins >= maxAdmins) {
        throw new Error(`Cannot create more admins. Maximum allowed: ${maxAdmins}. Current: ${currentAdmins}`);
      }
    }

    // Perform the role change
    await set(ref(database, `users/${uid}/role`), newRole);

    // Handle managed departments
    if (newRole === 'region-manager' || newRole === 'region-user') {
      await set(ref(database, `users/${uid}/managedDepartments`), []);
      toast.success(`Role changed to ${newRole}. Please configure managed departments separately.`);
    } else {
      if (editingUser.role === 'region-manager' || editingUser.role === 'region-user') {
        await set(ref(database, `users/${uid}/managedDepartments`), null);
      }
      toast.success(`Role changed to ${newRole}.`);
    }
  };

  // ✅ REMOVED: handleRoleChange replaced with modal-based role editing (handleModalRoleSave)

  // ✅ REMOVED: handleResetPassword moved to useUserData hook

  // ✅ REMOVED: handleDeleteUser moved to useUserData hook

  // ✅ NEW: Wrapper function for subscriber management (calling hook function)
  const handleToggleSubscription = useCallback(async (type, id, currentSubscribed) => {
    await handleToggleSubscriptionFromHook(type, id, currentSubscribed, isSubmitting, isInitializing);
  }, [handleToggleSubscriptionFromHook, isSubmitting, isInitializing]);

  // ✅ REMOVED: Department data loading moved to useDepartmentManagement hook to prevent infinite loops

  if (!isAuthReady) {
    return (
      <div className="mb-8">
        <h2 className="text-xl font-bold mb-4">Admin User Manager</h2>
        <p className="text-gray-500">Initializing authentication...</p>
      </div>
    );
  }

  return (
    <AdminUserManagerErrorBoundary>
      <div className="mb-8">
        <h2 className="text-xl font-bold mb-4">Admin User Manager</h2>
        <div className="mb-4">
          <p className="text-sm text-blue-600 font-medium mb-1">
            Managing users for department: {isRegionManager || isSuperAdmin() || isSuperUser || isRegionUser ? selectedDepartment : userDepartment || 'Unknown'}
          </p>

          <p className="text-sm text-gray-600">
            Your role: {getCurrentUserRole()} |
            {getCurrentUserRole() === 'user'
              ? 'No role management access'
              : `Can manage: ${getAllowedRoles(getCurrentUserRole()).join(', ')}`
            }
          </p>
          {getCurrentUserRole() === 'admin' && (
            <p className="text-sm text-green-600">
              ✅ As admin, you can create and manage other admins in your department
            </p>
          )}
          <p className="text-sm text-orange-600">
            🔒 You cannot change your own role or manage users above your level
          </p>
          <p className="text-sm text-red-600">
            🚫 Account deletion restrictions: Super-admin/Region-manager/Admin cannot delete their own accounts. Region-manager cannot delete super-admin accounts.
          </p>
          <p className="text-sm text-gray-700">
            Maximum Admins Allowed: {maxAdmins} (Current Admins: {users.filter((u) => u.role === 'admin').length})
          </p>
          {duplicateEmails.length > 0 && (
            <p className="text-sm text-red-600 mt-2">
              Warning: Duplicate emails detected: {duplicateEmails.join(', ')}. Please ensure unique emails for each employee.
            </p>
          )}
          {duplicateTelegramIds.length > 0 && (
            <p className="text-sm text-red-600 mt-2">
              Warning: Duplicate Telegram IDs detected: {duplicateTelegramIds.join(', ')}. Please ensure unique IDs.
            </p>
          )}
          {duplicateWhatsappNumbers.length > 0 && (
            <p className="text-sm text-red-600 mt-2">
              Warning: Duplicate WhatsApp numbers detected: {duplicateWhatsappNumbers.map(num => num.replace(/"/g, '')).join(', ')}. Please ensure unique numbers.
            </p>
          )}
        </div>
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <select
            value={selectedEmployee}
            onChange={(e) => setSelectedEmployee(e.target.value)}
            className="p-2 border rounded-md w-64"
            disabled={isSubmitting || isInitializing || isLoading || employeeOptions.length === 0}
            aria-label="Select employee to add as user"
          >
            <option value="">
              {employeeOptions.length === 0 ? 'No employees available' : 'Select Employee'}
            </option>
            {employeeOptions.map((emp) => (
              <option key={emp.employeeId} value={emp.name}>
                {emp.name} ({emp.email})
              </option>
            ))}
          </select>
          <button
            onClick={handleAddUser}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400"
            disabled={isSubmitting || isInitializing || isLoading || !selectedEmail || employeeOptions.length === 0}
            aria-label="Add selected employee as user"
          >
            {isLoading ? 'Adding...' : 'Add User'}
          </button>
        </div>
        {employeeOptions.length === 0 && localEmployees.length > 0 && (
          <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <p className="text-sm text-yellow-800">
              ℹ️ All employees with email addresses already have user accounts.
              No new users can be created at this time.
            </p>
          </div>
        )}

        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-2">Users</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border border-gray-300 p-2 text-left">Email</th>
                  <th className="border border-gray-300 p-2 text-left">Department</th>
                  <th className="border border-gray-300 p-2 text-left">Role</th>
                  <th className="border border-gray-300 p-2 text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {users.map((user) => (
                  <tr key={user.uid} className="hover:bg-gray-50">
                    <td className="border border-gray-300 p-2">{user.email}</td>
                    <td className="border border-gray-300 p-2">{user.department || 'Not Set'}</td>
                    <td className="border border-gray-300 p-2">{user.role}</td>
                    <td className="border border-gray-300 p-2 flex gap-2">
                      {hasRoleManagementAccess() ? (
                        <button
                          onClick={() => handleOpenRoleModal(user)}
                          className={`${
                            !canModifyUser(getCurrentUserRole(), user.role)
                              ? 'text-red-600 cursor-not-allowed'
                              : user.uid === getCurrentUserId() && isProtectedRole(user.role)
                                ? 'text-orange-600 cursor-not-allowed'
                                : 'text-blue-600 hover:underline'
                          } disabled:text-gray-400`}
                          disabled={
                            isSubmitting ||
                            isInitializing ||
                            !canModifyUser(getCurrentUserRole(), user.role) ||
                            (user.uid === getCurrentUserId() && isProtectedRole(user.role))
                          }
                          aria-label={`Edit role for ${user.email}`}
                          title={
                            !canModifyUser(getCurrentUserRole(), user.role)
                              ? `Cannot modify ${user.role} - insufficient privileges`
                              : user.uid === getCurrentUserId() && isProtectedRole(user.role)
                                ? 'Cannot change your own role for security'
                                : `Edit role for ${user.email}`
                          }
                        >
                          {!canModifyUser(getCurrentUserRole(), user.role)
                            ? '🚫 Restricted'
                            : user.uid === getCurrentUserId() && isProtectedRole(user.role)
                              ? '🔒 Protected'
                              : 'Edit Role'
                          }
                        </button>
                      ) : (
                        <span className="text-gray-400 text-sm">No Access</span>
                      )}
                      <button
                        onClick={() => handleResetPassword(user.email, user.uid)}
                        className="text-green-600 hover:underline disabled:text-gray-400"
                        disabled={isSubmitting || isInitializing || actionLoading[user.uid]}
                        aria-label={`Send password reset email to ${user.email}`}
                      >
                        {actionLoading[user.uid] ? 'Sending...' : 'Reset Password'}
                      </button>
                      <button
                        onClick={() => handleDeleteUser(user.uid, user.email)}
                        className={`${
                          !canDeleteUser(getCurrentUserRole(), user, user.uid)
                            ? 'text-red-400 cursor-not-allowed'
                            : 'text-red-600 hover:underline'
                        } disabled:text-gray-400`}
                        disabled={
                          isSubmitting ||
                          isInitializing ||
                          actionLoading[user.uid] ||
                          !canDeleteUser(getCurrentUserRole(), user, user.uid)
                        }
                        aria-label={`Delete user ${user.email}`}
                        title={
                          getDeletionBlockReason(getCurrentUserRole(), user, user.uid) || `Delete user ${user.email}`
                        }
                      >
                        {actionLoading[user.uid]
                          ? 'Deleting...'
                          : !canDeleteUser(getCurrentUserRole(), user, user.uid)
                            ? (user.uid === getCurrentUserId() ? '🔒 Protected' : '🚫 Restricted')
                            : 'Delete'
                        }
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            {users.length === 0 && (
              <p className="text-gray-500 mt-2">
                No users found for department: {isRegionManager || isSuperAdmin() || isSuperUser || isRegionUser ? selectedDepartment : userDepartment || 'Unknown'}
              </p>
            )}
          </div>
        </div>
          {/* ✅ NEW: Department Management Section for Super-Admin */}
        {isSuperAdmin() && (
          <div className="mb-8">
            <h3 className="text-lg font-semibold mb-4">🏢 Department Management (Super-Admin)</h3>
            <p className="text-sm text-blue-600 mb-4">
              📋 Manage department assignments for region-managers and region-users. Each department can only be assigned to one region-manager.
            </p>

            <div className="overflow-x-auto">
              <table className="min-w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-blue-50">
                    <th className="border border-gray-300 p-3 text-left">User Email</th>
                    <th className="border border-gray-300 p-3 text-left">Role</th>
                    <th className="border border-gray-300 p-3 text-left">Assigned Departments</th>
                    <th className="border border-gray-300 p-3 text-left">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {regionManagersAndUsers.map((user) => (
                    <tr key={user.uid} className="hover:bg-gray-50">
                      <td className="border border-gray-300 p-3">{user.email}</td>
                      <td className="border border-gray-300 p-3">
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          user.role === 'region-manager'
                            ? 'bg-purple-100 text-purple-800'
                            : 'bg-green-100 text-green-800'
                        }`}>
                          {user.role}
                        </span>
                      </td>
                      <td className="border border-gray-300 p-3">
                        <div className="flex flex-wrap gap-1">
                          {user.managedDepartments.length > 0 ? (
                            user.managedDepartments.map((dept) => (
                              <span
                                key={dept}
                                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                              >
                                {dept}
                                <button
                                  onClick={() => handleRemoveDepartmentFromUser(user.uid, dept)}
                                  className="ml-1 text-red-600 hover:text-red-800"
                                  title={`Remove ${dept} from ${user.email}`}
                                >
                                  ×
                                </button>
                              </span>
                            ))
                          ) : (
                            <span className="text-gray-500 text-sm">No departments assigned</span>
                          )}
                        </div>
                      </td>
                      <td className="border border-gray-300 p-3">
                        <button
                          onClick={() => openDepartmentModal(user, 'add')}
                          className="text-blue-600 hover:underline text-sm"
                          title={`Add department to ${user.email}`}
                        >
                          + Add Department
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {regionManagersAndUsers.length === 0 && (
                <p className="text-gray-500 mt-4 text-center">No region-managers or region-users found.</p>
              )}
            </div>
          </div>
        )}

        {/* ✅ NEW: Department Management Section for Region-Manager */}
        {isRegionManager && (
          <div className="mb-8">
            <h3 className="text-lg font-semibold mb-4">👥 My Region-Users Department Management</h3>
            <p className="text-sm text-orange-600 mb-4">
              📋 Manage department assignments for region-users in departments you control. You can only assign departments from your own managed departments.
            </p>

            <div className="overflow-x-auto">
              <table className="min-w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-orange-50">
                    <th className="border border-gray-300 p-3 text-left">User Email</th>
                    <th className="border border-gray-300 p-3 text-left">Role</th>
                    <th className="border border-gray-300 p-3 text-left">Assigned Departments</th>
                    <th className="border border-gray-300 p-3 text-left">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {myRegionUsers.map((user) => (
                    <tr key={user.uid} className="hover:bg-gray-50">
                      <td className="border border-gray-300 p-3">{user.email}</td>
                      <td className="border border-gray-300 p-3">
                        <span className="px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">
                          {user.role}
                        </span>
                      </td>
                      <td className="border border-gray-300 p-3">
                        <div className="flex flex-wrap gap-1">
                          {user.managedDepartments.length > 0 ? (
                            user.managedDepartments.map((dept) => (
                              <span
                                key={dept}
                                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800"
                              >
                                {dept}
                                <button
                                  onClick={() => handleRemoveDepartmentFromUser(user.uid, dept)}
                                  className="ml-1 text-red-600 hover:text-red-800"
                                  title={`Remove ${dept} from ${user.email}`}
                                >
                                  ×
                                </button>
                              </span>
                            ))
                          ) : (
                            <span className="text-gray-500 text-sm">No departments assigned</span>
                          )}
                        </div>
                      </td>
                      <td className="border border-gray-300 p-3">
                        <button
                          onClick={() => openDepartmentModal(user, 'add')}
                          className="text-orange-600 hover:underline text-sm"
                          title={`Add department to ${user.email}`}
                        >
                          + Add Department
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {myRegionUsers.length === 0 && (
                <p className="text-gray-500 mt-4 text-center">No region-users found in departments you control.</p>
              )}
            </div>
          </div>
        )}
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-2">Telegram Subscribers</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border border-gray-300 p-2 text-left">Telegram ID</th>
                  <th className="border border-gray-300 p-2 text-left">Status</th>
                  <th className="border border-gray-300 p-2 text-left">Subscribed</th>
                  <th className="border border-gray-300 p-2 text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {telegramSubscribers.map((sub) => (
                  <tr key={sub.id} className="hover:bg-gray-50">
                    <td className="border border-gray-300 p-2">{sub.id}</td>
                    <td className="border border-gray-300 p-2">{sub.status}</td>
                    <td className="border border-gray-300 p-2">{sub.subscribed ? 'Yes' : 'No'}</td>
                    <td className="border border-gray-300 p-2">
                      <button
                        onClick={() => handleToggleSubscription('telegram', sub.id, sub.subscribed)}
                        className="text-blue-600 hover:underline disabled:text-gray-400"
                        disabled={isSubmitting || isInitializing || subscriberActionLoading[sub.id]}
                        aria-label={`${sub.subscribed ? 'Unsubscribe' : 'Resubscribe'} Telegram ID ${sub.id}`}
                      >
                        {subscriberActionLoading[sub.id] ? 'Updating...' : sub.subscribed ? 'Unsubscribe' : 'Resubscribe'}
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            {telegramSubscribers.length === 0 && <p className="text-gray-500 mt-2">No Telegram subscribers found.</p>}
          </div>
        </div>
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-2">WhatsApp Subscribe</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border border-gray-300 p-2 text-left">Phone Number</th>
                  <th className="border border-gray-300 p-2 text-left">Status</th>
                  <th className="border border-gray-300 p-2 text-left">Subscribed</th>
                  <th className="border border-gray-300 p-2 text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {whatsappSubscribers.map((sub) => (
                  <tr key={sub.number} className="hover:bg-gray-50">
                    <td className="border border-gray-300 p-2">{sub.number.replace(/"/g, '')}</td>
                    <td className="border border-gray-300 p-2">{sub.status}</td>
                    <td className="border border-gray-300 p-2">{sub.subscribed ? 'Yes' : 'No'}</td>
                    <td className="border border-gray-300 p-2">
                      <button
                        onClick={() => handleToggleSubscription('whatsapp', sub.number, sub.subscribed)}
                        className="text-blue-600 hover:underline disabled:text-gray-400"
                        disabled={isSubmitting || isInitializing || subscriberActionLoading[sub.number]}
                        aria-label={`${sub.subscribed ? 'Unsubscribe' : 'Resubscribe'} WhatsApp number ${sub.number.replace(/"/g, '')}`}
                      >
                        {subscriberActionLoading[sub.number] ? 'Updating...' : sub.subscribed ? 'Unsubscribe' : 'Resubscribe'}
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            {whatsappSubscribers.length === 0 && <p className="text-gray-500 mt-2">No WhatsApp subscribers found.</p>}
          </div>
        </div>



        {/* ✅ NEW: Role Edit Modal */}
        <RoleEditModal
          isOpen={isRoleModalOpen}
          onClose={handleCloseRoleModal}
          user={editingUser}
          availableRoles={editingUser ? getAvailableRolesForUser(getCurrentUserRole(), editingUser) : []}
          onSave={handleModalRoleSave}
          currentUserRole={getCurrentUserRole()}
          maxAdmins={maxAdmins}
          currentAdminCount={users.filter((u) => u.role === 'admin').length}
        />

        {/* ✅ NEW: Department Assignment Modal */}
        <DepartmentAssignmentModal
          isOpen={isDepartmentModalOpen}
          onClose={closeDepartmentModal}
          user={departmentModalUser}
          modalType={departmentModalType}
          selectedDepartment={selectedDepartmentForAssignment}
          onDepartmentChange={setSelectedDepartmentForAssignment}
          onAssign={handleAddDepartmentToUser}
          onRemove={handleRemoveDepartmentFromUser}
          getAvailableDepartments={getAvailableDepartmentsForUser}
          currentUserRole={getCurrentUserRole()}
        />
      </div>
    </AdminUserManagerErrorBoundary>
  );
};

AdminUserManager.propTypes = {
  employees: PropTypes.arrayOf(
    PropTypes.shape({
      employeeId: PropTypes.string.isRequired,
      rowIndex: PropTypes.number,
      name: PropTypes.string,
      srcNumber: PropTypes.string,
      position: PropTypes.string,
      phone: PropTypes.string,
      email: PropTypes.string,
      createdAt: PropTypes.string,
      updatedAt: PropTypes.string,
    })
  ),
  isSubmitting: PropTypes.bool.isRequired,
  isInitializing: PropTypes.bool.isRequired,
};

export default React.memo(AdminUserManager);