// src/components/modals/DeleteRowModal.js
import React from 'react';

/**
 * DeleteRowModal component - Modal for deleting the last row
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {Array} props.localRosterData - Current roster data
 * @param {Object} props.deleteResult - Result of deleting a row
 * @param {Array} props.affectedDeleteMonths - Months affected by deleting the row
 * @param {boolean} props.isDeleting - Whether deleting a row is in progress
 * @param {Function} props.closeModal - Function to close the modal
 * @param {Function} props.confirmDeleteLastRow - Function to confirm deleting the last row
 * @param {number} props.year - Current year
 * @param {number} props.month - Current month
 * @returns {JSX.Element|null} DeleteRowModal component or null if not open
 */
const DeleteRowModal = ({
  isOpen,
  localRosterData,
  deleteResult,
  affectedDeleteMonths,
  isDeleting,
  closeModal,
  confirmDeleteLastRow,
  year,
  month,
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h2 className="text-xl font-bold mb-4">Delete Last Row</h2>
        {!deleteResult ? (
          <>
            <p className="text-sm text-gray-700 mb-4">
              You are about to delete the last row for{" "}
              <strong>{localRosterData[localRosterData.length - 1]?.Name}</strong>{" "}
              from the following rosters:
            </p>
            <ul className="list-disc pl-5 mb-4 text-sm text-gray-700">
              {affectedDeleteMonths.length === 1 ? (
                <li>{new Date(year, month - 1).toLocaleString('default', { month: 'long' })} {year} (No future rosters available)</li>
              ) : (
                affectedDeleteMonths.map(({ year: rosterYear, month: rosterMonth }) => (
                  <li key={`${rosterYear}-${rosterMonth}`}>
                    {new Date(rosterYear, rosterMonth - 1).toLocaleString('default', { month: 'long' })} {rosterYear}
                  </li>
                ))
              )}
            </ul>
            <p className="text-sm text-gray-700 mb-4">
              This action cannot be undone. Do you want to proceed?
            </p>
            {isDeleting && (
              <div className="flex justify-center mb-4">
                <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-600"></div>
              </div>
            )}
            <div className="flex justify-end space-x-2">
              <button
                onClick={closeModal}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                disabled={isDeleting}
              >
                Cancel
              </button>
              <button
                onClick={confirmDeleteLastRow}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
                disabled={isDeleting}
              >
                Delete
              </button>
            </div>
          </>
        ) : deleteResult.success ? (
          <>
            <p className="text-sm text-green-600 mb-4">
              The last row was successfully deleted from the following rosters:
            </p>
            <ul className="list-disc pl-5 mb-4 text-sm text-green-600">
              {affectedDeleteMonths.map(({ year: rosterYear, month: rosterMonth }) => (
                <li key={`${rosterYear}-${rosterMonth}`}>
                  {new Date(rosterYear, rosterMonth - 1).toLocaleString('default', { month: 'long' })} {rosterYear}
                </li>
              ))}
            </ul>
            <div className="flex justify-end">
              <button
                onClick={closeModal}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Close
              </button>
            </div>
          </>
        ) : (
          <>
            <p className="text-sm text-red-600 mb-4">
              Failed to delete the last row: {deleteResult.error}
            </p>
            <div className="flex justify-end space-x-2">
              <button
                onClick={closeModal}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
              >
                Cancel
              </button>
              <button
                onClick={confirmDeleteLastRow}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Retry
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default DeleteRowModal;
