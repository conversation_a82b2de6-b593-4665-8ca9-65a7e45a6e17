import { getDatabase, ref, update } from 'firebase/database';
import getDepartmentPath from './utils/getDepartmentPath';

// Helper function to validate write access using AuthProvider context
const validateWriteAccess = (authContext, selectedDepartment) => {
  const { currentUser, userRole, userDepartment, hasWriteAccessToDepartment } = authContext;

  if (!currentUser) {
    throw new Error('User not authenticated');
  }

  // Determine target department
  let targetDepartment;
  if (userRole === 'super-admin' || userRole === 'region-manager' ||
      userRole === 'super-user' || userRole === 'region-user') {
    if (!selectedDepartment) {
      throw new Error('Selected department is required for this user role');
    }
    targetDepartment = selectedDepartment;
  } else {
    // admin/user use their assigned department
    targetDepartment = userDepartment;
    if (!targetDepartment) {
      throw new Error('User department not found');
    }
  }

  // Use AuthProvider's hasWriteAccessToDepartment function
  if (!hasWriteAccessToDepartment(targetDepartment)) {
    if (userRole === 'super-user' || userRole === 'region-user') {
      throw new Error('Access denied: Your role does not have write permissions');
    } else if (userRole === 'region-manager') {
      throw new Error(`Access denied: You don't have write access to department ${targetDepartment}`);
    } else if (userRole === 'admin') {
      throw new Error(`Access denied: You can only write to your assigned department ${userDepartment}`);
    } else {
      throw new Error('Access denied: Insufficient permissions');
    }
  }

  return targetDepartment;
};

const updateRosterDataGen = async (year, month, rosterData, isPublish, authContext, selectedDepartment = null) => {
  try {
    // Validate write access and get target department using AuthProvider context
    const targetDepartment = validateWriteAccess(authContext, selectedDepartment);
    console.log('Target department validated:', targetDepartment);

    // Get department path based on user role and selected department
    // For super-admin, region-manager, super-user, region-user: use selectedDepartment
    // For admin/user: use their assigned department
    const departmentPath = await getDepartmentPath(true, selectedDepartment);
    console.log('Department path:', departmentPath);
    console.log('Selected department:', selectedDepartment);

    // Validate department path
    if (!departmentPath) {
      throw new Error('Department path could not be determined');
    }

    const db = getDatabase();
    const rosterType = isPublish ? 'live Roster' : 'Draft Roster';
    console.log(`Updating ${rosterType} for department: ${targetDepartment}`);

    const path = isPublish
        ? `${departmentPath}rosters/${year}/${month}`
        : `${departmentPath}rostersDraft/${year}/${month}`;
    console.log('Saving to path:', path);

    const newRosterRef = ref(db, path);

   // const rosterRef = ref(db, `rostersDraft/${year}/${month}`);

    // Transform rosterData back into the employeeShifts format expected by Firebase
    const employeeShifts = rosterData.reduce((acc, employee) => {
      const { employeeId, Name, 'Staff/SRC#': srcNumber, Position, 'Leave days': leaveDays, 'Sick days': sickDays, 'Work days': workDays, 'Work Time': workTime, 'Overtime days': overtimeDays, Overtime: overtime, Aircraft: aircraft, Office: office, rowIndex, ...shifts } = employee;
      acc[employeeId] = {
        name: Name,
        srcNumber,
        position: Position,
        leaveDays,
        sickDays,
        workDays,
        workTime,
        overtimeDays,
        overtime,
        aircraft,
        office,
        rowIndex, // Include rowIndex in the saved data
        shifts // The remaining keys are the shifts (e.g., "1": "D1", "2": "N1", etc.)
      };
      return acc;
    }, {});

    console.log(`Saving ${rosterType} data for ${year}/${month} to department ${targetDepartment}:`, employeeShifts);
    const updatedAt = new Date().toISOString();
    const updatedBy = authContext.currentUser?.uid || 'unknown';

    try {
      await update(newRosterRef, {
        employeeShifts,
        updatedAt,
        updatedBy,
        department: targetDepartment,
        isPublished: isPublish
      });
      console.log(`${rosterType} data saved successfully to department ${targetDepartment}`);
      return {
        success: true,
        department: targetDepartment,
        path: path,
        isPublished: isPublish,
        updatedAt
      };
    } catch (updateError) {
      console.error('Error updating roster data:', updateError);
      throw new Error(`Failed to update ${rosterType.toLowerCase()}: ${updateError.message}`);
    }
  } catch (error) {
    console.error('Error saving roster data:', error);
    throw error; // Let the caller handle the error
  }
};

export default updateRosterDataGen;