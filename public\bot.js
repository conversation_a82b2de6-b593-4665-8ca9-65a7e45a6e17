const { Telegraf } = require('telegraf');
const { initializeApp } = require('firebase-admin/app');
const { getDatabase } = require('firebase-admin/database');

initializeApp();

const telegramBotToken = '**********************************************';
const bot = new Telegraf(telegramBotToken);
const database = getDatabase();

// Handle /start command to add chat ID to database
bot.start((ctx) => {
  const chatId = ctx.chat.id.toString();
  const chatIdsRef = database.ref('/chatIds');
  
  chatIdsRef.child(chatId).set(true, (error) => {
    if (error) {
      console.error("Error adding chat ID to database:", error);
    } else {
      console.log("Added chat ID:", chatId);
      ctx.reply('Welcome! You will now receive roster updates.');
    }
  });
});

bot.launch();

console.log('Bot is running...');

// Enable graceful stop
process.once('SIGINT', () => bot.stop('SIGINT'));
process.once('SIGTERM', () => bot.stop('SIGTERM'));