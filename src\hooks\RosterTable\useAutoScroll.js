// src/hooks/RosterTable/useAutoScroll.js
import { useRef, useEffect } from 'react';

/**
 * Custom hook to handle auto-scrolling during drag operations
 * 
 * @param {boolean} dragging - Whether dragging is in progress
 * @param {string} mode - Current mode ('update', 'drag', 'dialog', 'view')
 * @param {number} autoScrollMargin - Margin from edge to trigger auto-scroll (px)
 * @param {number} autoScrollSpeed - Scroll speed (px per mousemove)
 * @returns {Object} Table container ref
 */
const useAutoScroll = (dragging, mode, autoScrollMargin = 40, autoScrollSpeed = 30) => {
  const tableContainerRef = useRef(null);
  const autoScrollIntervalRef = useRef(null);
  const mousePosRef = useRef({ x: 0, y: 0 });

  // Listen for mousemove during selection drag
  useEffect(() => {
    if (!dragging || mode !== 'drag') return;
    
    const handleMouseMove = (e) => {
      if (!tableContainerRef.current) return;
      
      const rect = tableContainerRef.current.getBoundingClientRect();
      mousePosRef.current = { x: e.clientX, y: e.clientY };
      
      let scrollX = 0;
      let scrollY = 0;
      
      if (e.clientX - rect.left < autoScrollMargin) scrollX = -autoScrollSpeed;
      else if (rect.right - e.clientX < autoScrollMargin) scrollX = autoScrollSpeed;
      
      if (e.clientY - rect.top < autoScrollMargin) scrollY = -autoScrollSpeed;
      else if (rect.bottom - e.clientY < autoScrollMargin) scrollY = autoScrollSpeed;
      
      if (scrollX !== 0 || scrollY !== 0) {
        if (!autoScrollIntervalRef.current) {
          autoScrollIntervalRef.current = setInterval(() => {
            if (!tableContainerRef.current) return;
            tableContainerRef.current.scrollBy(scrollX, scrollY);
          }, 30);
        }
      } else {
        if (autoScrollIntervalRef.current) {
          clearInterval(autoScrollIntervalRef.current);
          autoScrollIntervalRef.current = null;
        }
      }
    };
    
    document.addEventListener('mousemove', handleMouseMove);
    
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      if (autoScrollIntervalRef.current) {
        clearInterval(autoScrollIntervalRef.current);
        autoScrollIntervalRef.current = null;
      }
    };
  }, [dragging, mode, autoScrollMargin, autoScrollSpeed]);

  return tableContainerRef;
};

export default useAutoScroll;
