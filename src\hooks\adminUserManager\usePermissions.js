import { useContext } from 'react';
import { auth } from '../../firebaseConfig';
import { AuthContext } from '../../components/AuthProvider';

/**
 * Custom hook for handling user permissions and role management
 * Extracted from AdminUserManager.js to improve maintainability
 */
export const usePermissions = (users = []) => {
  const { isSuperAdmin, isRegionManager, isSuperUser, isRegionUser } = useContext(AuthContext);

  // ✅ Get current user's role
  const getCurrentUserRole = () => {
    if (isSuperAdmin()) return 'super-admin';
    if (isRegionManager) return 'region-manager';
    if (isSuperUser) return 'super-user';
    if (isRegionUser) return 'region-user';

    // Find current user in users list to check if admin
    const currentUser = auth.currentUser;
    if (currentUser) {
      const userRecord = users.find(u => u.email === currentUser.email);
      if (userRecord?.role === 'admin') return 'admin';
    }

    return 'user';
  };

  // ✅ Get current user's ID
  const getCurrentUserId = () => {
    return auth.currentUser?.uid || null;
  };

  // ✅ Get role hierarchy level
  const getRoleLevel = (role) => {
    const levels = {
      'super-admin': 4,      // Highest - can manage all
      'region-manager': 3,   // Can manage admin, user
      'admin': 2,           // Can manage admin, user
      'user': 1             // Cannot manage anyone
    };
    return levels[role] || 0;
  };

  // ✅ Check if current user can modify target user
  const canModifyUser = (currentUserRole, targetUserRole) => {
    const currentLevel = getRoleLevel(currentUserRole);
    const targetLevel = getRoleLevel(targetUserRole);

    // Special case: super-admin can manage all EXCEPT other super-admins
    if (currentUserRole === 'super-admin') {
      return targetUserRole !== 'super-admin';
    }

    // Special case: admin can manage other admins (same level) and users (below)
    if (currentUserRole === 'admin' && targetUserRole === 'admin') {
      return true;
    }

    // For all other cases: can only manage users BELOW their level
    return currentLevel > targetLevel;
  };

  // ✅ Check if current user can promote to specific role
  const canPromoteToRole = (currentUserRole, targetRole) => {
    const roleHierarchy = {
      'super-admin': ['user', 'admin', 'region-manager', 'super-user', 'region-user'],
      'region-manager': ['user', 'admin', 'region-user'],
      'admin': ['user', 'admin'],
      'user': []
    };

    return roleHierarchy[currentUserRole]?.includes(targetRole) || false;
  };

  // ✅ Check if role is protected (cannot change own role)
  const isProtectedRole = (role) => {
    return ['super-admin', 'region-manager', 'admin'].includes(role);
  };

  // ✅ Check if target user is current user's own account
  const isOwnAccount = (targetUserId) => {
    return getCurrentUserId() === targetUserId;
  };

  // ✅ Check if user deletion should be blocked
  const canDeleteUser = (currentUserRole, targetUser, targetUserId) => {
    const currentUserId = getCurrentUserId();

    // Block self-deletion for protected roles
    if (targetUserId === currentUserId && ['super-admin', 'region-manager', 'admin'].includes(targetUser.role)) {
      return false;
    }

    // Block region-manager from deleting super-admin
    if (currentUserRole === 'region-manager' && targetUser.role === 'super-admin') {
      return false;
    }

    // Block admin from deleting super-admin or region-manager
    if (currentUserRole === 'admin' && ['super-admin', 'region-manager'].includes(targetUser.role)) {
      return false;
    }

    // Block users from deleting anyone
    if (currentUserRole === 'user') {
      return false;
    }

    return true;
  };

  // ✅ Get reason why deletion is blocked
  const getDeletionBlockReason = (currentUserRole, targetUser, targetUserId) => {
    const currentUserId = getCurrentUserId();

    if (targetUserId === currentUserId && ['super-admin', 'region-manager', 'admin'].includes(targetUser.role)) {
      return 'Cannot delete your own account for security';
    }

    if (currentUserRole === 'region-manager' && targetUser.role === 'super-admin') {
      return 'Cannot delete super-admin - insufficient privileges';
    }

    if (currentUserRole === 'admin' && ['super-admin', 'region-manager'].includes(targetUser.role)) {
      return `Cannot delete ${targetUser.role} - insufficient privileges`;
    }

    if (currentUserRole === 'user') {
      return 'Users cannot delete accounts';
    }

    return null;
  };

  // ✅ Check if user has role management access
  const hasRoleManagementAccess = () => {
    const currentUserRole = getCurrentUserRole();
    return ['super-admin', 'region-manager', 'admin'].includes(currentUserRole);
  };

  // ✅ Get allowed roles for current user
  const getAllowedRoles = (currentUserRole) => {
    const permissions = {
      'super-admin': ['region-manager', 'super-user', 'region-user', 'admin', 'user'],
      'region-manager': ['region-user', 'admin', 'user'],
      'admin': ['admin', 'user'],
      'user': ['none']
    };
    return permissions[currentUserRole] || ['none'];
  };

  // ✅ Get next allowed role in cycle
  const getNextAllowedRole = (currentUserRole, targetUserCurrentRole, targetUserId) => {
    // Users have no role management access
    if (currentUserRole === 'user') {
      return null;
    }

    // Prevent protected roles from changing their own roles
    if (isOwnAccount(targetUserId) && isProtectedRole(targetUserCurrentRole)) {
      return null;
    }

    const allowedRoles = {
      'super-admin': {
        'user': 'admin',
        'admin': 'region-manager',
        'region-manager': 'super-user',
        'super-user': 'region-user',
        'region-user': 'user'
      },
      'region-manager': {
        'user': 'admin',
        'admin': 'region-user',
        'region-user': 'user'
      },
      'admin': {
        'user': 'admin',
        'admin': 'user'
      }
    };

    return allowedRoles[currentUserRole]?.[targetUserCurrentRole] || null;
  };

  return {
    getCurrentUserRole,
    getCurrentUserId,
    getRoleLevel,
    canModifyUser,
    canPromoteToRole,
    isProtectedRole,
    isOwnAccount,
    canDeleteUser,
    getDeletionBlockReason,
    hasRoleManagementAccess,
    getAllowedRoles,
    getNextAllowedRole
  };
};
