const { initializeApp } = require('firebase-admin/app');
const { getAuth } = require('firebase-admin/auth');
const { getDatabase } = require('firebase-admin/database');
const { onValueUpdated } = require("firebase-functions/v2/database");
const { onRequest } = require('firebase-functions/v2/https');
const logger = require("firebase-functions/logger");
const { Telegraf } = require('telegraf');

initializeApp();

const telegramBotToken = '**********************************************';
const chatIds = ['7741742088', '6231238168'];

const bot = new Telegraf(telegramBotToken);

exports.rosterUpdateNotification = onValueUpdated(
  {
    ref: '/rosters/{pushId}',
    region: "us-central1",
  },
  async (event) => {
    logger.info("Roster data changed:", event);

    const afterData = event.data.after.val();
    const beforeData = event.data.before.val();

    if (JSON.stringify(afterData) === JSON.stringify(beforeData)) {
      logger.info("No significant data change, skipping notification.");
      return;
    }

    const message = "New roster update!";

    try {
      await Promise.all(chatIds.map(chatId => bot.telegram.sendMessage(chatId, message)));
      logger.info("Telegram notifications sent successfully to:", chatIds);
    } catch (error) {
      logger.error("Error sending Telegram notifications:", error);
    }
  }
);

exports.addUser = onRequest(
  {
    enforceAppCheck: false,
    region: "us-central1",
  },
  async (req, res) => {
    try {
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(403).send({ error: "Unauthorized: Missing or invalid token." });
        return;
      }

      const idToken = authHeader.split('Bearer ')[1];
      const decodedToken = await getAuth().verifyIdToken(idToken);

      const { email, displayName } = req.body;
      if (!email || !displayName) {
        res.status(400).send({ error: 'Email and display name are required.' });
        return;
      }

      const userRecord = await getAuth().createUser({
        email,
        displayName,
        emailVerified: false,
        disabled: false,
      });

      const database = getDatabase();
      const userRef = database.ref(`users/${userRecord.uid}`);
      await userRef.set({
        email,
        role: 'user',
        uid: userRecord.uid,
        displayName,
      });

      res.status(200).send({
        message: `User with email ${email} created successfully.`,
        uid: userRecord.uid,
      });
    } catch (error) {
      logger.error("Error adding user:", error);
      res.status(500).send({ error: `Failed to add user: ${error.message}` });
    }
  }
);

exports.deleteUser = onRequest(
  {
    enforceAppCheck: false,
    region: "us-central1",
  },
  async (req, res) => {
    try {
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(403).send({ error: "Unauthorized: Missing or invalid token." });
        return;
      }

      const idToken = authHeader.split('Bearer ')[1];
      const decodedToken = await getAuth().verifyIdToken(idToken);

      const uid = req.query.uid || req.body.uid;
      if (!uid) {
        res.status(400).send({ error: 'User ID is required.' });
        return;
      }

      await getAuth().deleteUser(uid);

      const database = getDatabase();
      const userRef = database.ref(`users/${uid}`);
      await userRef.remove();

      res.status(200).send({ message: `User with UID ${uid} deleted successfully.` });
    } catch (error) {
      logger.error("Error deleting user:", error);
      res.status(500).send({ error: `Failed to delete user: ${error.message}` });
    }
  }
);