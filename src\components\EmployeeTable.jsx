// src\components\EmployeeTable.jsx
import React, { useMemo, useCallback } from 'react';
import PropTypes from 'prop-types';
import { toast } from 'react-toastify';

const EmployeeTable = ({
  employees,
  searchTerm,
  setSearchTerm,
  positionFilter,
  setPositionFilter,
  onEditEmployee,
  onDeleteEmployee,
  isSubmitting,
  isInitializing,
}) => {
  const positions = useMemo(() => {
    const uniquePositions = Array.from(new Set(employees.map((emp) => emp.position || '')));
    return ['All', ...uniquePositions.sort()];
  }, [employees]);

  const filteredEmployees = useMemo(() => {
    let filtered = [...employees];
    if (searchTerm) {
      filtered = filtered.filter(
        (emp) =>
          (emp.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
          (emp.srcNumber || '').toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    if (positionFilter && positionFilter !== 'All') {
      filtered = filtered.filter((emp) => (emp.position || '') === positionFilter);
    }
    return filtered;
  }, [employees, searchTerm, positionFilter]);

  const handleDelete = useCallback(
    async (employeeId) => {
      if (!window.confirm(`Are you sure you want to delete employee ${employeeId}?`)) return;
      try {
        await onDeleteEmployee(employeeId);
        toast.success('Employee deleted successfully!');
      } catch (err) {
        toast.error(err.message || 'Failed to delete employee.');
      }
    },
    [onDeleteEmployee]
  );

  return (
    <div className="mb-8">
      <div className="flex flex-col sm:flex-row gap-4 mb-4">
        <input
          type="text"
          placeholder="Search by name or SRC number..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="p-2 border rounded-md flex-1"
          disabled={isSubmitting || isInitializing}
          aria-label="Search employees by name or SRC number"
        />
        <select
          value={positionFilter}
          onChange={(e) => setPositionFilter(e.target.value)}
          className="p-2 border rounded-md"
          disabled={isSubmitting || isInitializing}
          aria-label="Filter employees by position"
        >
          {positions.map((pos) => (
            <option key={pos} value={pos}>
              {pos || 'No Position'}
            </option>
          ))}
        </select>
      </div>
      <div className="overflow-x-auto">
        <table className="min-w-full border-collapse border border-gray-300">
          <thead>
            <tr className="bg-gray-100">
              <th className="border border-gray-300 p-2 text-left" style={{ minWidth: '80px' }}>Row Index</th>
              <th className="border border-gray-300 p-2 text-left" style={{ minWidth: '150px' }}>Name</th>
              <th className="border border-gray-300 p-2 text-left" style={{ minWidth: '100px' }}>Employee ID</th>
              <th className="border border-gray-300 p-2 text-left" style={{ minWidth: '100px' }}>SRC Number</th>
              <th className="border border-gray-300 p-2 text-left" style={{ minWidth: '100px' }}>Position</th>
              <th className="border border-gray-300 p-2 text-left" style={{ minWidth: '120px' }}>Phone</th>
              <th className="border border-gray-300 p-2 text-left" style={{ minWidth: '200px' }}>Email</th>
              <th className="border border-gray-300 p-2 text-left" style={{ minWidth: '100px' }}>Details</th>
              <th className="border border-gray-300 p-2 text-left" style={{ minWidth: '120px' }}>Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredEmployees.map((emp) => (
              <tr key={emp.employeeId} className="hover:bg-gray-50 align-middle">
                <td className="border border-gray-300 p-2 whitespace-normal align-middle">{emp.rowIndex || '-'}</td>
                <td className="border border-gray-300 p-2 whitespace-normal align-middle">{emp.name || '-'}</td>
                <td className="border border-gray-300 p-2 whitespace-normal align-middle">{emp.employeeId || '-'}</td>
                <td className="border border-gray-300 p-2 whitespace-normal align-middle">{emp.srcNumber || '-'}</td>
                <td className="border border-gray-300 p-2 whitespace-normal align-middle">{emp.position || '-'}</td>
                <td className="border border-gray-300 p-2 whitespace-normal align-middle">{emp.phone || '-'}</td>
                <td className="border border-gray-300 p-2 whitespace-normal align-middle" style={{ wordBreak: 'break-all' }}>{emp.email || '-'}</td>
                <td className="border border-gray-300 p-2 whitespace-normal align-middle">
                  <span
                    className="text-blue-600 hover:underline cursor-pointer"
                    title={`Created: ${emp.createdAt ? new Date(emp.createdAt).toLocaleString() : '-'}\nUpdated: ${
                      emp.updatedAt ? new Date(emp.updatedAt).toLocaleString() : '-'
                    }`}
                    aria-label={`View details for ${emp.name || 'employee'}`}
                  >
                    View Details
                  </span>
                </td>
                <td className="border border-gray-300 p-2 align-middle min-h-full">
                  <div className="flex gap-2 items-center h-full">
                    <button
                      onClick={() => onEditEmployee(emp)}
                      className="text-blue-600 hover:underline disabled:text-gray-400"
                      disabled={isSubmitting || isInitializing}
                      aria-label={`Edit employee ${emp.name || emp.employeeId}`}
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => handleDelete(emp.employeeId)}
                      className="text-red-600 hover:underline disabled:text-gray-400"
                      disabled={isSubmitting || isInitializing}
                      aria-label={`Delete employee ${emp.name || emp.employeeId}`}
                    >
                      Delete
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        {filteredEmployees.length === 0 && (
          <p className="text-gray-500 mt-2">No employees found.</p>
        )}
      </div>
    </div>
  );
};

EmployeeTable.propTypes = {
  employees: PropTypes.arrayOf(
    PropTypes.shape({
      employeeId: PropTypes.string.isRequired,
      rowIndex: PropTypes.number,
      name: PropTypes.string,
      srcNumber: PropTypes.string,
      position: PropTypes.string,
      phone: PropTypes.string,
      email: PropTypes.string,
      createdAt: PropTypes.string,
      updatedAt: PropTypes.string,
    })
  ).isRequired,
  searchTerm: PropTypes.string.isRequired,
  setSearchTerm: PropTypes.func.isRequired,
  positionFilter: PropTypes.string.isRequired,
  setPositionFilter: PropTypes.func.isRequired,
  onEditEmployee: PropTypes.func.isRequired,
  onDeleteEmployee: PropTypes.func.isRequired,
  isSubmitting: PropTypes.bool.isRequired,
  isInitializing: PropTypes.bool.isRequired,
};

export default React.memo(EmployeeTable);