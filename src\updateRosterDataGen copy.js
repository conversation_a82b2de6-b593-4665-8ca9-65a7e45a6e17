import { getDatabase, ref, update, get } from 'firebase/database';

const getDepartmentPath = async () => {
  const auth = getDatabase().app.auth();
  const user = auth.currentUser;

  if (!user) return '';

  const db = getDatabase();
  const userRef = ref(db, `users/${user.uid}`);
  const snapshot = await get(userRef);

  if (snapshot.exists()) {
    const userData = snapshot.val();
    if (userData.role === 'super-admin') {
      // Super admin can access all departments
      return '';
    }
    return userData.department ? `departments/${userData.department}/` : '';
  }

  return '';
};

const updateRosterDataGen = async (year, month, rosterData, isPublish) => {
  try {
    const departmentPath = await getDepartmentPath();
    const db = getDatabase();
    isPublish ? console.log('live Roster') : console.log('Draft Roster')

    const newRosterRef = ref(db, isPublish
        ? `${departmentPath}rosters/${year}/${month}`
        : `${departmentPath}rostersDraft/${year}/${month}`
      );

   // const rosterRef = ref(db, `rostersDraft/${year}/${month}`);

    // Transform rosterData back into the employeeShifts format expected by Firebase
    const employeeShifts = rosterData.reduce((acc, employee) => {
      const { employeeId, Name, 'Staff/SRC#': srcNumber, Position, 'Leave days': leaveDays, 'Sick days': sickDays, 'Work days': workDays, 'Work Time': workTime, 'Overtime days': overtimeDays, Overtime: overtime, Aircraft: aircraft, Office: office, rowIndex, ...shifts } = employee;
      acc[employeeId] = {
        name: Name,
        srcNumber,
        position: Position,
        leaveDays,
        sickDays,
        workDays,
        workTime,
        overtimeDays,
        overtime,
        aircraft,
        office,
        rowIndex, // Include rowIndex in the saved data
        shifts // The remaining keys are the shifts (e.g., "1": "D1", "2": "N1", etc.)
      };
      return acc;
    }, {});

    console.log(`Saving roster data for ${year}/${month}:`, employeeShifts);
    const updatedAt = new Date().toISOString();
    await update(newRosterRef, { employeeShifts, updatedAt });
  } catch (error) {
    console.error('Error saving roster data:', error);
    throw error; // Let the caller handle the error
  }
};

export default updateRosterDataGen;