import React, { forwardRef } from "react";
import { cn } from "../../lib/utils";

const Badge = forwardRef(({ className, variant = "default", ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
      variant === "default" && "bg-primary text-primary-foreground border-primary",
      variant === "secondary" && "bg-secondary text-secondary-foreground border-secondary",
      variant === "destructive" && "bg-destructive text-destructive-foreground border-destructive",
      variant === "outline" && "text-foreground",
      className
    )}
    {...props}
  />
));

export default Badge;