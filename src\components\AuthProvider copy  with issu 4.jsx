import React, { createContext, useState, useEffect } from 'react';
import { auth } from '../firebaseConfig';
import { onAuthStateChanged } from 'firebase/auth';
import { getDatabase, ref, get } from 'firebase/database';

export const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [userRole, setUserRole] = useState(null);
  const [selectedDepartment, setSelectedDepartment] = useState(null);
  const [userDepartment, setUserDepartment] = useState(null); // Restored as requested
  const [loading, setLoading] = useState(true);
  const [departments, setDepartments] = useState([]);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        setCurrentUser(user);
        const db = getDatabase();
        const userRef = ref(db, `users/${user.uid}`);
        const snapshot = await get(userRef);

        if (snapshot.exists()) {
          const userData = snapshot.val();
          const role = userData.role;

          setUserRole(role);

          if (role === 'super-admin') {
            // Fetch all departments for super-admin
            const departmentsRef = ref(db, 'departments');
            const deptSnapshot = await get(departmentsRef);
            if (deptSnapshot.exists()) {
              const deptData = deptSnapshot.val();
              const deptList = Object.entries(deptData).map(([deptId, dept]) => ({
                id: deptId,
                name: dept.department_name || deptId,
              }));
              setDepartments(deptList);
              setSelectedDepartment(deptList.length > 0 ? deptList[0].id : null); // Default to first department
              setUserDepartment(null); // No specific department for super-admins
            } else {
              console.error('AuthProvider: No departments found');
              setSelectedDepartment(null);
              setUserDepartment(null);
            }
          } else {
            // Regular users/admins get their assigned department
            const userDept = userData.department || null;
            setUserDepartment(userDept);
            setSelectedDepartment(userDept); // Align with user's department
            // No need to fetch departments for non-super-admins
          }
        } else {
          setUserRole(null);
          setSelectedDepartment(null);
          setUserDepartment(null);
        }
      } else {
        setCurrentUser(null);
        setUserRole(null);
        setSelectedDepartment(null);
        setUserDepartment(null);
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const isSuperAdmin = () => userRole === 'super-admin';

  // Function to check access to a department
  const hasAccessToDepartment = (departmentId) => {
    if (isSuperAdmin()) {
      return selectedDepartment ? selectedDepartment === departmentId : true;
    }
    if (userRole === 'admin' || userRole === 'user') {
      return userDepartment === departmentId;
    }
    return false;
  };

  // Function to check write access to a department
  const hasWriteAccessToDepartment = (deptId) => {
    if (!currentUser || !deptId) return false;

    if (isSuperAdmin()) {
      // Super-admins can write to any department
      return true;
    } else {
      // Regular users/admins can only write to their assigned department
      return userDepartment === deptId;
    }
  };

  const value = {
    currentUser,
    userRole,
    isSuperAdmin,
    selectedDepartment,
    setSelectedDepartment,
    userDepartment,
    setUserDepartment,
    departments,
    loading, // Restore loading state
    hasAccessToDepartment, // Restore this function
    hasWriteAccessToDepartment,
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};