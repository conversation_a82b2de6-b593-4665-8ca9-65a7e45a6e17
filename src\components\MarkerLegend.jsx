import React from 'react';
import PropTypes from 'prop-types';

const filterMarkersByMonthYear1 = (markers, year, month) => {
  return Object.entries(markers).reduce((acc, [id, marker]) => {
    //const startDate = new Date(marker.startDate.year, marker.startDate.month - 1, marker.startDate.day);
    //const endDate = new Date(marker.endDate.year, marker.endDate.month - 1, marker.endDate.day);
    const markerYear = marker.startDate.year;
    const markerMonth = marker.startDate.month;
    const markerEndMonth = marker.endDate.month;
    const markerEndYear = marker.endDate.year;

    if (
      (markerYear === year && markerMonth <= month && (markerEndYear > year || markerEndMonth >= month)) ||
      (markerEndYear === year && markerEndMonth >= month) ||
      (marker.recurring && markerMonth === month)
    ) {
      acc[id] = marker;
    }
    return acc;
  }, {});
};
const filterMarkersByMonthYear = (markers, year, month) => {
  return Object.entries(markers).reduce((acc, [id, marker]) => {
    const startDate = new Date(marker.startDate.year, marker.startDate.month - 1, marker.startDate.day);
    const endDate = new Date(marker.endDate.year, marker.endDate.month - 1, marker.endDate.day);
    const monthStart = new Date(year, month - 1, 1); // First day of the specified month
    const monthEnd = new Date(year, month, 0); // Last day of the specified month

    const isWithinRange =
      startDate <= monthEnd && // Marker starts on or before the end of the month
      endDate >= monthStart; // Marker ends on or after the start of the month

    const isRecurringMatch = marker.recurring && marker.startDate.month === month;

    if (isWithinRange || isRecurringMatch) {
      acc[id] = marker;
    }

    return acc;
  }, {});
};

const groupWeekdayMarkers = (weekdayMarkers) => {
  const dayAbbrevs = {
    Monday: 'Mon',
    Tuesday: 'Tue',
    Wednesday: 'Wed',
    Thursday: 'Thu',
    Friday: 'Fri',
    Saturday: 'Sat',
    Sunday: 'Sun',
  };

  const grouped = {};

  Object.entries(weekdayMarkers).forEach(([id, marker]) => {
    const key = marker.color; // Group by color
    const abbrevName = dayAbbrevs[marker.name] || marker.name; // Abbreviate or keep original if not found
    if (!grouped[key]) {
      grouped[key] = { names: [], color: marker.color, ids: [] };
    }
    grouped[key].names.push(abbrevName);
    grouped[key].ids.push(id);
  });

  return Object.entries(grouped).map(([color, { names, ids }], index) => ({
    id: `grouped-${index}-${ids.join('-')}`, // Unique key for React
    name: names.sort().join('/'), // e.g., "Fri/Sat"
    color,
  }));
};

const MarkerLegend = ({ weekdayMarkers, holidayMarkers, specialDayMarkers, year, month }) => {
  const filteredHolidayMarkers = filterMarkersByMonthYear(holidayMarkers, year, month);
  const filteredSpecialDayMarkers = filterMarkersByMonthYear(specialDayMarkers, year, month);
  const groupedWeekdayMarkers = groupWeekdayMarkers(weekdayMarkers);

  return (
    <div
      style={{
        marginTop: '0.5rem',
        padding: '0.75rem',
        backgroundColor: '#f3f4f6',
        borderRadius: '0.5rem',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        maxWidth: '500px',
        width: '100%',
        boxSizing: 'border-box',
      }}
    >
      <h3 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>Calendar Markers</h3>
      {(groupedWeekdayMarkers.length === 0 &&
        Object.keys(filteredHolidayMarkers).length === 0 &&
        Object.keys(filteredSpecialDayMarkers).length === 0) ? (
        <p style={{ fontSize: '0.75rem', color: '#6b7280' }}>No markers defined for this month.</p>
      ) : (
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(2, minmax(0, 1fr))',
            gap: '0.25rem',
            fontSize: '0.75rem',
            lineHeight: '1.25',
          }}
        >
          {groupedWeekdayMarkers.map((marker) => (
            <div key={marker.id} style={{ display: 'flex', alignItems: 'center' }}>
              <div
                style={{
                  width: '0.75rem',
                  height: '0.75rem',
                  marginRight: '0.25rem',
                  backgroundColor: marker.color,
                  flexShrink: 0,
                }}
              ></div>
              <span style={{ whiteSpace: 'nowrap' }}>Weekday: {marker.name}</span>
            </div>
          ))}
          {Object.entries(filteredHolidayMarkers).map(([id, marker]) => (
            <div key={id} style={{ display: 'flex', alignItems: 'center' }}>
              <div
                style={{
                  width: '0.75rem',
                  height: '0.75rem',
                  marginRight: '0.25rem',
                  backgroundColor: marker.color,
                  flexShrink: 0,
                }}
              ></div>
              <span style={{ whiteSpace: 'nowrap' }}>Holiday: {marker.name}</span>
            </div>
          ))}
          {Object.entries(filteredSpecialDayMarkers).map(([id, marker]) => (
            <div key={id} style={{ display: 'flex', alignItems: 'center' }}>
              <div
                style={{
                  width: '0.75rem',
                  height: '0.75rem',
                  marginRight: '0.25rem',
                  backgroundColor: marker.color,
                  flexShrink: 0,
                }}
              ></div>
              <span style={{ whiteSpace: 'nowrap' }}>Special: {marker.name}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

MarkerLegend.propTypes = {
  weekdayMarkers: PropTypes.object.isRequired,
  holidayMarkers: PropTypes.object.isRequired,
  specialDayMarkers: PropTypes.object.isRequired,
  year: PropTypes.number.isRequired,
  month: PropTypes.number.isRequired,
};

export default MarkerLegend;