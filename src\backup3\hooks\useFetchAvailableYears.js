//src/hooks/useFetchAvailableRosters.js
import { useState, useEffect } from 'react';
import { getDatabase, ref, get } from 'firebase/database';
//import { database } from '../firebaseConfig';

const useFetchAvailableYears = () => {
  const [availableYears, setAvailableYears] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchAvailableYears = async () => {
      setLoading(true);
      setError(null);

      try {
        const db = getDatabase();
        const rostersRef = ref(db, 'rosters');
        const snapshot = await get(rostersRef);

        if (snapshot.exists()) {
          const data = snapshot.val();
          const years = Object.keys(data)
            .filter(key => !isNaN(key) && Number.isInteger(parseInt(key)))
            .map(key => parseInt(key))
            .sort((a, b) => a - b); // Sort years numerically
          setAvailableYears(years);
        } else {
          setAvailableYears([]);
        }
      } catch (err) {
        setError(err);
        console.error('Error fetching available years:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchAvailableYears();
  }, []);

  return { availableYears, loading, error };
};

export default useFetchAvailableYears;