// src/contexts/useDataProvider.js
import { useEffect, useCallback, useRef, useContext } from 'react';
import { AuthContext } from '../components/AuthProvider';
import { useDataDispatch, useRosterData } from './DataContext.jsx';
import {
  fetchRosterData,
  fetchAvailableYears,
  fetchAvailableMonths,
  fetchEmployeesData,
  fetchShiftsData,
  fetchContactInfo,
  fetchCalendarMarkers,
  setRosterYear,
  setRosterMonth,
  toggleRosterDraft,
  addEmployee,
  updateEmployee,
  deleteEmployee,
  updateShift,
  deleteShift,
  updateContactInfo,
  deleteContactInfo
} from './dataActions';

/**
 * Custom hook to initialize and manage data fetching
 * @returns {Object} Data fetching and management functions
 */
export const useDataProvider = () => {
  const { selectedDepartment, isSuperAdmin } = useContext(AuthContext);
  const dispatch = useDataDispatch();
  const { year, month, isDraftRoster } = useRosterData();
  const cleanupFunctions = useRef({});

  // Initialize data fetching
  useEffect(() => {
    const initializeData = async () => {
      try {
        // Fetch initial data, passing selectedDepartment for super-admins
        await Promise.all([
          fetchAvailableYears(dispatch, isDraftRoster, null, isSuperAdmin() ? selectedDepartment : null),
          fetchEmployeesData(dispatch, false, isSuperAdmin() ? selectedDepartment : null),
          fetchShiftsData(dispatch, false, isSuperAdmin() ? selectedDepartment : null),
          fetchCalendarMarkers(dispatch, false, isSuperAdmin() ? selectedDepartment : null)
        ]);

        // Set up contact info subscription
        const cleanup = await fetchContactInfo(dispatch, false, isSuperAdmin() ? selectedDepartment : null);
        cleanupFunctions.current.contactInfo = typeof cleanup === 'function' ? cleanup : () => {};
      } catch (err) {
        console.error('Error initializing data:', err);
        cleanupFunctions.current.contactInfo = () => {};
      }
    };

    initializeData();

    // Cleanup function
    return () => {
      Object.values(cleanupFunctions.current).forEach(cleanup => {
        if (typeof cleanup === 'function') {
          cleanup();
        }
      });
    };
  }, [dispatch, isDraftRoster, selectedDepartment, isSuperAdmin]);

  // Refetch data when selectedDepartment changes for super-admins
  useEffect(() => {
    if (isSuperAdmin() && selectedDepartment) {
      const refetchData = async () => {
        try {
          // Clear existing subscriptions
          if (cleanupFunctions.current.rosterData) {
            cleanupFunctions.current.rosterData();
          }
          if (cleanupFunctions.current.contactInfo) {
            cleanupFunctions.current.contactInfo();
          }

          // Refetch all data with new department
          await Promise.all([
            fetchAvailableYears(dispatch, isDraftRoster, null, selectedDepartment),
            fetchAvailableMonths(dispatch, year, isDraftRoster, null, selectedDepartment),
            fetchRosterData(dispatch, year, month, isDraftRoster, null, selectedDepartment),
            fetchEmployeesData(dispatch, true, selectedDepartment),
            fetchShiftsData(dispatch, true, selectedDepartment),
            fetchCalendarMarkers(dispatch, true, selectedDepartment)
          ]);

          // Re-establish contact info subscription
          const cleanup = await fetchContactInfo(dispatch, true, selectedDepartment);
          cleanupFunctions.current.contactInfo = typeof cleanup === 'function' ? cleanup : () => {};
        } catch (err) {
          console.error('Error refetching data for new department:', err);
        }
      };

      refetchData();
    }
  }, [selectedDepartment, dispatch, isDraftRoster, year, month, isSuperAdmin]);

  // Function to fetch available years with an override for isDraftRoster
  const fetchAvailableYearsWithOverride = useCallback((overrideIsDraftRoster = null) => {
    try {
      fetchAvailableYears(dispatch, isDraftRoster, overrideIsDraftRoster, isSuperAdmin() ? selectedDepartment : null);
    } catch (err) {
      console.error('Error fetching available years:', err);
    }
  }, [dispatch, isDraftRoster, selectedDepartment, isSuperAdmin]);

  // Function to fetch available months with an override for isDraftRoster
  const fetchAvailableMonthsWithOverride = useCallback((year, overrideIsDraftRoster = null) => {
    try {
      fetchAvailableMonths(dispatch, year, isDraftRoster, overrideIsDraftRoster, isSuperAdmin() ? selectedDepartment : null);
    } catch (err) {
      console.error('Error fetching available months:', err);
    }
  }, [dispatch, isDraftRoster, selectedDepartment, isSuperAdmin]);

  // Fetch available months when year changes
  useEffect(() => {
    try {
      fetchAvailableMonths(dispatch, year, isDraftRoster, null, isSuperAdmin() ? selectedDepartment : null);
    } catch (err) {
      console.error('Error fetching available months:', err);
    }
  }, [dispatch, year, isDraftRoster, selectedDepartment, isSuperAdmin]);

  // Fetch roster data when year, month, or isDraftRoster changes
  useEffect(() => {
    // Clean up previous roster data subscription if it exists
    if (typeof cleanupFunctions.current.rosterData === 'function') {
      cleanupFunctions.current.rosterData();
    }

    // Set up new subscription
    fetchRosterData(dispatch, year, month, isDraftRoster, null, isSuperAdmin() ? selectedDepartment : null)
      .then(cleanup => {
        cleanupFunctions.current.rosterData = typeof cleanup === 'function' ? cleanup : () => {};
      })
      .catch(err => {
        console.error('Error setting up roster data subscription:', err);
        cleanupFunctions.current.rosterData = () => {};
      });

    // Cleanup function
    return () => {
      if (typeof cleanupFunctions.current.rosterData === 'function') {
        cleanupFunctions.current.rosterData();
      }
    };
  }, [dispatch, year, month, isDraftRoster, selectedDepartment, isSuperAdmin]);

  // Function to fetch roster data with an override for isDraftRoster
  const fetchRosterDataWithOverride = useCallback((overrideIsDraftRoster = null) => {
    // Clean up previous subscription
    if (typeof cleanupFunctions.current.rosterData === 'function') {
      cleanupFunctions.current.rosterData();
    }

    // Set up new subscription with the override
    fetchRosterData(
      dispatch,
      year,
      month,
      isDraftRoster,
      overrideIsDraftRoster,
      isSuperAdmin() ? selectedDepartment : null
    )
      .then(cleanup => {
        cleanupFunctions.current.rosterData = typeof cleanup === 'function' ? cleanup : () => {};
      })
      .catch(err => {
        console.error('Error setting up roster data subscription:', err);
        cleanupFunctions.current.rosterData = () => {};
      });
  }, [dispatch, year, month, isDraftRoster, selectedDepartment, isSuperAdmin]);

  // Roster actions
  const changeYear = useCallback((newYear) => {
    setRosterYear(dispatch, newYear);
  }, [dispatch]);

  const changeMonth = useCallback((newMonth) => {
    setRosterMonth(dispatch, newMonth);
  }, [dispatch]);

  const toggleDraftMode = useCallback(() => {
    toggleRosterDraft(dispatch);
  }, [dispatch]);

  const refetchRosterData = useCallback(() => {
    // Clean up previous subscription
    if (typeof cleanupFunctions.current.rosterData === 'function') {
      cleanupFunctions.current.rosterData();
    }

    // Set up new subscription
    fetchRosterData(dispatch, year, month, isDraftRoster, null, isSuperAdmin() ? selectedDepartment : null)
      .then(cleanup => {
        cleanupFunctions.current.rosterData = typeof cleanup === 'function' ? cleanup : () => {};
      })
      .catch(err => {
        console.error('Error setting up roster data subscription:', err);
        cleanupFunctions.current.rosterData = () => {};
      });
  }, [dispatch, year, month, isDraftRoster, selectedDepartment, isSuperAdmin]);

  // Employee actions
  const addNewEmployee = useCallback((employeeData) => {
    return addEmployee(dispatch, employeeData, isSuperAdmin() ? selectedDepartment : null);
  }, [dispatch, selectedDepartment, isSuperAdmin]);

  const updateExistingEmployee = useCallback((employeeId, employeeData) => {
    return updateEmployee(dispatch, employeeId, employeeData, isSuperAdmin() ? selectedDepartment : null);
  }, [dispatch, selectedDepartment, isSuperAdmin]);

  const removeEmployee = useCallback((employeeId) => {
    return deleteEmployee(dispatch, employeeId, isSuperAdmin() ? selectedDepartment : null);
  }, [dispatch, selectedDepartment, isSuperAdmin]);

  const refetchEmployeesData = useCallback((forceRefresh = true) => {
    fetchEmployeesData(dispatch, forceRefresh, isSuperAdmin() ? selectedDepartment : null);
  }, [dispatch, selectedDepartment, isSuperAdmin]);

  // Shift actions
  const updateShiftData = useCallback((shiftName, shiftData) => {
    return updateShift(dispatch, shiftName, shiftData, isSuperAdmin() ? selectedDepartment : null);
  }, [dispatch, selectedDepartment, isSuperAdmin]);

  const removeShift = useCallback((shiftName) => {
    return deleteShift(dispatch, shiftName, isSuperAdmin() ? selectedDepartment : null);
  }, [dispatch, selectedDepartment, isSuperAdmin]);

  const refetchShiftsData = useCallback((forceRefresh = true) => {
    fetchShiftsData(dispatch, forceRefresh, isSuperAdmin() ? selectedDepartment : null);
  }, [dispatch, selectedDepartment, isSuperAdmin]);

  // Contact info actions
  const updateContactInfoData = useCallback((id, data) => {
    return updateContactInfo(dispatch, id, data, isSuperAdmin() ? selectedDepartment : null);
  }, [dispatch, selectedDepartment, isSuperAdmin]);

  const removeContactInfo = useCallback((id) => {
    return deleteContactInfo(dispatch, id, isSuperAdmin() ? selectedDepartment : null);
  }, [dispatch, selectedDepartment, isSuperAdmin]);

  const refetchContactInfo = useCallback((forceRefresh = true) => {
    // Clean up previous subscription
    if (typeof cleanupFunctions.current.contactInfo === 'function') {
      cleanupFunctions.current.contactInfo();
    }

    // Set up new subscription
    fetchContactInfo(dispatch, forceRefresh, isSuperAdmin() ? selectedDepartment : null)
      .then(cleanup => {
        cleanupFunctions.current.contactInfo = typeof cleanup === 'function' ? cleanup : () => {};
      })
      .catch(err => {
        console.error('Error setting up contact info subscription:', err);
        cleanupFunctions.current.contactInfo = () => {};
      });
  }, [dispatch, selectedDepartment, isSuperAdmin]);

  // Calendar markers actions
  const refetchCalendarMarkers = useCallback((forceRefresh = true) => {
    fetchCalendarMarkers(dispatch, forceRefresh, isSuperAdmin() ? selectedDepartment : null);
  }, [dispatch, selectedDepartment, isSuperAdmin]);

  // Cache management
  const clearCache = useCallback(() => {
    // Import dynamically to avoid circular dependencies
    const dataCache = require('../utils/cacheUtils').default;
    dataCache.clear();
    console.log('Data cache cleared');
  }, []);

  return {
    // Roster actions
    changeYear,
    changeMonth,
    toggleDraftMode,
    refetchRosterData,
    fetchRosterDataWithOverride,
    fetchAvailableYearsWithOverride,
    fetchAvailableMonthsWithOverride,

    // Employee actions
    addNewEmployee,
    updateExistingEmployee,
    removeEmployee,
    refetchEmployeesData,

    // Shift actions
    updateShiftData,
    removeShift,
    refetchShiftsData,

    // Contact info actions
    updateContactInfoData,
    removeContactInfo,
    refetchContactInfo,

    // Calendar markers actions
    refetchCalendarMarkers,

    // Cache management
    clearCache
  };
};

export default useDataProvider;