import { useState, useCallback, useContext } from 'react';
import { ref, set, get } from 'firebase/database';
import { auth, database } from '../../firebaseConfig';
import { toast } from 'react-toastify';
import { AuthContext } from '../../components/AuthProvider';

/**
 * Custom hook for handling department management operations
 * Extracted from AdminUserManager.js to improve maintainability
 */
export const useDepartmentManagement = () => {
  const { userDepartment, selectedDepartment, isSuperAdmin, isRegionManager } = useContext(AuthContext);

  // State management
  const [regionManagersAndUsers, setRegionManagersAndUsers] = useState([]);
  const [myRegionUsers, setMyRegionUsers] = useState([]);

  // Modal state for department assignment
  const [isDepartmentModalOpen, setIsDepartmentModalOpen] = useState(false);
  const [departmentModalUser, setDepartmentModalUser] = useState(null);
  const [departmentModalType, setDepartmentModalType] = useState('add');
  const [selectedDepartmentForAssignment, setSelectedDepartmentForAssignment] = useState('');

  // ✅ Helper function to get current user ID
  const getCurrentUserId = () => {
    return auth.currentUser?.uid || null;
  };

  // ✅ Fetch all region-managers and region-users (for super-admin)
  const getRegionManagersAndUsers = useCallback(async () => {
    try {
      const usersRef = ref(database, 'users');
      const snapshot = await get(usersRef);

      if (!snapshot.exists()) return [];

      const data = snapshot.val();
      const regionUsers = Object.entries(data)
        .map(([uid, user]) => ({
          uid,
          email: user.email || 'Unknown',
          role: user.role || 'user',
          department: user.department || null,
          managedDepartments: user.managedDepartments || [],
          displayName: user.displayName || user.email || 'Unknown',
          createdBy: user.createdBy || null
        }))
        .filter(user =>
          user.role === 'region-manager' || user.role === 'region-user'
        )
        .sort((a, b) => a.email.localeCompare(b.email));

      console.log('Region-managers and region-users:', regionUsers);
      return regionUsers;
    } catch (error) {
      console.error('Error fetching region managers and users:', error);
      toast.error('Failed to load region managers and users.');
      return [];
    }
  }, []);

  // ✅ Fetch region-users in departments controlled by current region-manager
  const getMyRegionUsers = useCallback(async () => {
    try {
      const currentUserId = getCurrentUserId();
      if (!currentUserId) return [];

      // First, get current region-manager's managed departments
      const currentUserRef = ref(database, `users/${currentUserId}`);
      const currentUserSnapshot = await get(currentUserRef);

      if (!currentUserSnapshot.exists()) {
        console.warn('Current user data not found');
        return [];
      }

      const currentUserData = currentUserSnapshot.val();
      const myManagedDepartments = currentUserData.managedDepartments || [];

      console.log('Region-manager managed departments:', myManagedDepartments);

      if (myManagedDepartments.length === 0) {
        console.log('Region-manager has no managed departments');
        return [];
      }

      // Now get all users and filter region-users in controlled departments
      const usersRef = ref(database, 'users');
      const snapshot = await get(usersRef);

      if (!snapshot.exists()) return [];

      const data = snapshot.val();
      const myRegionUsers = Object.entries(data)
        .map(([uid, user]) => ({
          uid,
          email: user.email || 'Unknown',
          role: user.role || 'user',
          department: user.department || null,
          managedDepartments: user.managedDepartments || [],
          displayName: user.displayName || user.email || 'Unknown',
          createdBy: user.createdBy || null
        }))
        .filter(user =>
          user.role === 'region-user' &&
          user.department &&
          myManagedDepartments.includes(user.department)
        )
        .sort((a, b) => a.email.localeCompare(b.email));

      console.log(`Found ${myRegionUsers.length} region-users in controlled departments:`,
        myRegionUsers.map(u => `${u.email} (${u.department})`));

      return myRegionUsers;
    } catch (error) {
      console.error('Error fetching my region users:', error);
      toast.error('Failed to load your region users.');
      return [];
    }
  }, []);

  // ✅ Get all departments from database
  const getAllDepartments = useCallback(async () => {
    try {
      const departmentsRef = ref(database, 'departments');
      const snapshot = await get(departmentsRef);

      if (!snapshot.exists()) {
        console.warn('No departments found in database');
        return [];
      }

      const data = snapshot.val();
      const departments = Object.entries(data).map(([id, dept]) => ({
        id,
        name: dept.name || id,
        ...dept
      }));

      console.log('All departments:', departments);
      return departments;
    } catch (error) {
      console.error('Error fetching departments:', error);
      toast.error('Failed to load departments.');
      return [];
    }
  }, []);

  // ✅ Get department conflicts (check if department is already assigned to another region-manager)
  const getDepartmentConflicts = useCallback(async (departmentId, excludeUserId = null) => {
    try {
      const usersRef = ref(database, 'users');
      const snapshot = await get(usersRef);

      if (!snapshot.exists()) return [];

      const data = snapshot.val();
      const conflicts = Object.entries(data)
        .filter(([uid, user]) => {
          if (excludeUserId && uid === excludeUserId) return false;
          return user.role === 'region-manager' &&
                 user.managedDepartments &&
                 user.managedDepartments.includes(departmentId);
        })
        .map(([uid, user]) => ({
          uid,
          email: user.email || 'Unknown',
          role: user.role
        }));

      return conflicts;
    } catch (error) {
      console.error('Error checking department conflicts:', error);
      return [];
    }
  }, []);

  // ✅ Check if current user can assign specific department
  const canAssignDepartment = useCallback(async (departmentId, targetUserId, currentUserRole) => {
    // Super-admin can assign any department (with conflict checking)
    if (currentUserRole === 'super-admin') {
      const conflicts = await getDepartmentConflicts(departmentId, targetUserId);
      if (conflicts.length > 0) {
        console.log(`Department ${departmentId} conflict with:`, conflicts);
        return false;
      }
      return true;
    }

    // Region-manager can only assign from their own managed departments
    if (currentUserRole === 'region-manager') {
      // Get current region-manager's managed departments
      const currentUserId = getCurrentUserId();

      // Try to get from regionManagersAndUsers first (for super-admin view)
      let currentUserData = regionManagersAndUsers.find(u => u.uid === currentUserId);

      // If not found, get from users array (for region-manager view)
      if (!currentUserData) {
        // Fetch directly from database
        try {
          const userRef = ref(database, `users/${currentUserId}`);
          const snapshot = await get(userRef);
          if (snapshot.exists()) {
            currentUserData = {
              uid: currentUserId,
              ...snapshot.val()
            };
            console.log('canAssignDepartment: Fetched from database:', currentUserData);
          }
        } catch (error) {
          console.error('canAssignDepartment: Error fetching from database:', error);
        }
      }

      console.log('canAssignDepartment: User data found:', {
        currentUserData: currentUserData ? {
          uid: currentUserData.uid,
          email: currentUserData.email,
          role: currentUserData.role,
          managedDepartments: currentUserData.managedDepartments
        } : null
      });

      // If still not found or no managed departments
      if (!currentUserData) {
        console.error('canAssignDepartment: Current user data not found');
        return false;
      }

      if (!currentUserData.managedDepartments || !Array.isArray(currentUserData.managedDepartments)) {
        console.warn('canAssignDepartment: No managedDepartments found for region-manager:', {
          uid: currentUserData.uid,
          email: currentUserData.email,
          managedDepartments: currentUserData.managedDepartments
        });
        return false;
      }

      const myManagedDepartments = currentUserData.managedDepartments;
      const canAssign = myManagedDepartments.includes(departmentId);

      console.log('canAssignDepartment: Final check:', {
        departmentId,
        myManagedDepartments,
        canAssign
      });

      return canAssign;
    }

    // Other roles cannot assign departments
    return false;
  }, [getDepartmentConflicts]);

  // ✅ Get available departments for assignment to a user
  const getAvailableDepartmentsForUser = useCallback(async (targetUserId, currentUserRole) => {
    console.log('getAvailableDepartmentsForUser called:', { targetUserId, currentUserRole });

    const allDepartments = await getAllDepartments();
    console.log('All departments:', allDepartments);

    const targetUser = regionManagersAndUsers.find(u => u.uid === targetUserId) ||
                      myRegionUsers.find(u => u.uid === targetUserId);

    console.log('Target user found:', targetUser);

    if (!targetUser) {
      console.warn('Target user not found');
      return [];
    }

    const currentlyAssigned = targetUser.managedDepartments || [];
    console.log('Currently assigned departments:', currentlyAssigned);

    const availableDepartments = [];

    for (const dept of allDepartments) {
      // Don't show already assigned departments
      if (currentlyAssigned.includes(dept.id)) {
        console.log(`Department ${dept.id} already assigned, skipping`);
        continue;
      }

      // Check if current user can assign this department
      const canAssign = await canAssignDepartment(dept.id, targetUserId, currentUserRole);
      console.log(`Can assign ${dept.id}:`, canAssign);

      if (canAssign) {
        availableDepartments.push(dept);
      }
    }

    console.log('Available departments for assignment:', availableDepartments);
    return availableDepartments;
  }, [getAllDepartments, canAssignDepartment]);

  // ✅ Add department to user
  const handleAddDepartmentToUser = useCallback(async (userId, departmentId) => {
    try {
      const currentUserRole = isSuperAdmin() ? 'super-admin' : isRegionManager ? 'region-manager' : 'user';

      // Validate permission
      if (!(await canAssignDepartment(departmentId, userId, currentUserRole))) {
        if (currentUserRole === 'super-admin') {
          const conflicts = await getDepartmentConflicts(departmentId, userId);
          if (conflicts.length > 0) {
            toast.error(`Cannot assign ${departmentId}: Already assigned to ${conflicts[0].email} (${conflicts[0].role})`);
          } else {
            toast.error('Cannot assign this department.');
          }
        } else {
          toast.error('You can only assign departments from your managed departments.');
        }
        return false;
      }

      // Get current user's managed departments
      const userRef = ref(database, `users/${userId}`);
      const snapshot = await get(userRef);

      if (!snapshot.exists()) {
        toast.error('User not found.');
        return false;
      }

      const userData = snapshot.val();
      const currentManagedDepartments = userData.managedDepartments || [];

      // Add new department if not already present
      if (!currentManagedDepartments.includes(departmentId)) {
        const updatedDepartments = [...currentManagedDepartments, departmentId];
        await set(ref(database, `users/${userId}/managedDepartments`), updatedDepartments);

        toast.success(`Department ${departmentId} assigned successfully.`);

        // Refresh data
        if (currentUserRole === 'super-admin') {
          const data = await getRegionManagersAndUsers();
          setRegionManagersAndUsers(data);
        } else if (currentUserRole === 'region-manager') {
          const data = await getMyRegionUsers();
          setMyRegionUsers(data);
        }

        return true;
      } else {
        toast.info('Department already assigned to this user.');
        return false;
      }
    } catch (error) {
      console.error('Error adding department to user:', error);
      toast.error('Failed to assign department.');
      return false;
    }
  }, [isSuperAdmin, isRegionManager, canAssignDepartment, getDepartmentConflicts, getRegionManagersAndUsers, getMyRegionUsers]);

  // ✅ Remove department from user
  const handleRemoveDepartmentFromUser = useCallback(async (userId, departmentId) => {
    try {
      const currentUserRole = isSuperAdmin() ? 'super-admin' : isRegionManager ? 'region-manager' : 'user';

      // Get current user's managed departments
      const userRef = ref(database, `users/${userId}`);
      const snapshot = await get(userRef);

      if (!snapshot.exists()) {
        toast.error('User not found.');
        return false;
      }

      const userData = snapshot.val();
      const currentManagedDepartments = userData.managedDepartments || [];

      // Remove department if present
      if (currentManagedDepartments.includes(departmentId)) {
        const updatedDepartments = currentManagedDepartments.filter(dept => dept !== departmentId);
        await set(ref(database, `users/${userId}/managedDepartments`), updatedDepartments);

        toast.success(`Department ${departmentId} removed successfully.`);

        // Refresh data
        if (currentUserRole === 'super-admin') {
          const data = await getRegionManagersAndUsers();
          setRegionManagersAndUsers(data);
        } else if (currentUserRole === 'region-manager') {
          const data = await getMyRegionUsers();
          setMyRegionUsers(data);
        }

        return true;
      } else {
        toast.info('Department not assigned to this user.');
        return false;
      }
    } catch (error) {
      console.error('Error removing department from user:', error);
      toast.error('Failed to remove department.');
      return false;
    }
  }, [isSuperAdmin, isRegionManager, getRegionManagersAndUsers, getMyRegionUsers]);

  // ✅ Modal management functions
  const openDepartmentModal = useCallback((user, type) => {
    setDepartmentModalUser(user);
    setDepartmentModalType(type);
    setSelectedDepartmentForAssignment('');
    setIsDepartmentModalOpen(true);
  }, []);

  const closeDepartmentModal = useCallback(() => {
    setIsDepartmentModalOpen(false);
    setDepartmentModalUser(null);
    setDepartmentModalType('add');
    setSelectedDepartmentForAssignment('');
  }, []);

  return {
    // State
    regionManagersAndUsers,
    setRegionManagersAndUsers,
    myRegionUsers,
    setMyRegionUsers,
    isDepartmentModalOpen,
    departmentModalUser,
    departmentModalType,
    selectedDepartmentForAssignment,
    setSelectedDepartmentForAssignment,

    // Data fetching functions
    getRegionManagersAndUsers,
    getMyRegionUsers,
    getAllDepartments,
    getDepartmentConflicts,
    getAvailableDepartmentsForUser,

    // Permission functions
    canAssignDepartment,

    // CRUD operations
    handleAddDepartmentToUser,
    handleRemoveDepartmentFromUser,

    // Modal functions
    openDepartmentModal,
    closeDepartmentModal
  };
};
