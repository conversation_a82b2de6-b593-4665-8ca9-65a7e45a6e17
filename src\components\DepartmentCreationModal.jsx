import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { toast } from 'react-toastify';

const DepartmentCreationModal = ({ isOpen, onClose, onConfirm, existingDepartments }) => {
  const [departmentName, setDepartmentName] = useState('');
  const [departmentId, setDepartmentId] = useState('');
  const [error, setError] = useState('');
  
  useEffect(() => {
    if (departmentName) {
      // Generate department ID from name (lowercase, replace spaces with underscores)
      const generatedId = `dept_${departmentName.toLowerCase().replace(/\s+/g, '_')}`;
      setDepartmentId(generatedId);
    } else {
      setDepartmentId('');
    }
  }, [departmentName]);
  
  const validateDepartment = () => {
    if (!departmentName.trim()) {
      setError('Department name cannot be empty');
      return false;
    }
    
    // Check if department name already exists
    if (existingDepartments.some(dept => 
      dept.name.toLowerCase() === departmentName.toLowerCase() ||
      dept.id.toLowerCase() === departmentId.toLowerCase()
    )) {
      setError('Department with this name already exists');
      return false;
    }
    
    setError('');
    return true;
  };
  
  const handleSubmit = () => {
    if (validateDepartment()) {
      onConfirm({
        id: departmentId,
        name: departmentName
      });
      setDepartmentName('');
      onClose();
    }
  };
  
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h2 className="text-xl font-bold mb-4">Add New Department</h2>
        
        <div className="mb-4">
          <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="departmentName">
            Department Name
          </label>
          <input
            id="departmentName"
            type="text"
            value={departmentName}
            onChange={(e) => setDepartmentName(e.target.value)}
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            placeholder="Enter department name"
          />
          {error && <p className="text-red-500 text-xs italic mt-1">{error}</p>}
        </div>
        
        <div className="mb-4">
          <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="departmentId">
            Department ID (auto-generated)
          </label>
          <input
            id="departmentId"
            type="text"
            value={departmentId}
            disabled
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 bg-gray-100 leading-tight"
          />
        </div>
        
        <div className="flex items-center justify-end">
          <button
            onClick={onClose}
            className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          >
            Add Department
          </button>
        </div>
      </div>
    </div>
  );
};

DepartmentCreationModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onConfirm: PropTypes.func.isRequired,
  existingDepartments: PropTypes.array.isRequired
};

export default DepartmentCreationModal;
