// src/components/PerformanceMonitor.jsx
import React, { useState, useEffect } from 'react';
import performanceMonitor from '../utils/performanceMonitor';

const PerformanceMonitor = () => {
  const [report, setReport] = useState({});
  const [expanded, setExpanded] = useState(false);
  const [enabled, setEnabled] = useState(true);
  const [logLevel, setLogLevel] = useState('warn');
  const [refreshInterval, setRefreshInterval] = useState(5000);

  // Update report periodically
  useEffect(() => {
    const updateReport = () => {
      setReport(performanceMonitor.getReport());
    };

    // Initial update
    updateReport();

    // Set up interval
    const interval = setInterval(updateReport, refreshInterval);

    // Clean up
    return () => clearInterval(interval);
  }, [refreshInterval]);

  // Update performance monitor settings when they change
  useEffect(() => {
    performanceMonitor.setEnabled(enabled);
    performanceMonitor.setLogLevel(logLevel);
  }, [enabled, logLevel]);

  const handleClearMeasurements = () => {
    performanceMonitor.clearMeasurements();
    setReport({});
  };

  const handleLogLevelChange = (e) => {
    setLogLevel(e.target.value);
  };

  const handleRefreshIntervalChange = (e) => {
    setRefreshInterval(parseInt(e.target.value, 10));
  };

  return (
    <div className="fixed bottom-4 left-4 bg-white shadow-lg rounded-lg p-4 z-50 max-w-md">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Performance Monitor</h3>
        <button 
          onClick={() => setExpanded(!expanded)}
          className="text-blue-500 hover:text-blue-700"
        >
          {expanded ? 'Collapse' : 'Expand'}
        </button>
      </div>
      
      <div className="mt-2 flex items-center gap-4">
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={enabled}
            onChange={() => setEnabled(!enabled)}
            className="mr-2"
          />
          Enabled
        </label>
        
        <select
          value={logLevel}
          onChange={handleLogLevelChange}
          className="border rounded px-2 py-1 text-sm"
        >
          <option value="none">No Logging</option>
          <option value="log">Log</option>
          <option value="warn">Warn</option>
          <option value="error">Error</option>
        </select>
      </div>
      
      {expanded && (
        <div className="mt-4">
          <div className="flex justify-between mb-2">
            <h4 className="font-medium">Measurements</h4>
            <div className="flex gap-2">
              <select
                value={refreshInterval}
                onChange={handleRefreshIntervalChange}
                className="border rounded px-2 py-1 text-xs"
              >
                <option value="1000">Refresh: 1s</option>
                <option value="2000">Refresh: 2s</option>
                <option value="5000">Refresh: 5s</option>
                <option value="10000">Refresh: 10s</option>
              </select>
              <button 
                onClick={handleClearMeasurements}
                className="text-xs bg-red-500 hover:bg-red-600 text-white px-2 py-1 rounded"
              >
                Clear
              </button>
            </div>
          </div>
          
          {Object.keys(report).length > 0 ? (
            <div className="max-h-60 overflow-y-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="bg-gray-100">
                    <th className="text-left p-1">Operation</th>
                    <th className="text-right p-1">Count</th>
                    <th className="text-right p-1">Avg (ms)</th>
                    <th className="text-right p-1">Min (ms)</th>
                    <th className="text-right p-1">Max (ms)</th>
                  </tr>
                </thead>
                <tbody>
                  {Object.entries(report).map(([operation, metrics]) => (
                    <tr key={operation} className="border-b">
                      <td className="p-1">{operation}</td>
                      <td className="text-right p-1">{metrics.count}</td>
                      <td className="text-right p-1">{metrics.averageDuration.toFixed(2)}</td>
                      <td className="text-right p-1">{metrics.minDuration.toFixed(2)}</td>
                      <td className="text-right p-1">{metrics.maxDuration.toFixed(2)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <p className="text-sm text-gray-500">No measurements recorded</p>
          )}
        </div>
      )}
    </div>
  );
};

export default PerformanceMonitor;
