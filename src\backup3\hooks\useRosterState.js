// src/hooks/useRosterState.js
import { useState } from 'react';

/**
 * Custom hook to manage roster state (year, month, draft status)
 * @returns {Object} State and functions for managing roster state
 */
const useRosterState = () => {
  const currentDate = new Date();
  const [year, setYear] = useState(currentDate.getFullYear());
  const [month, setMonth] = useState(currentDate.getMonth() + 1);
  const [isRosterDraft, setIsRosterDraft] = useState(true);

  // Toggle between draft and live roster view
  const toggleRosterView = async (refetchCallback) => {
    setIsRosterDraft(!isRosterDraft);
    if (refetchCallback) {
      await refetchCallback(); // Refetch data after toggling
    }
  };

  return {
    year,
    setYear,
    month,
    setMonth,
    isRosterDraft,
    setIsRosterDraft,
    toggleRosterView,
  };
};

export default useRosterState;
