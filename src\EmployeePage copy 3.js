// src/EmployeePage.js
import React, { useState, useCallback, useMemo } from 'react';
import EmployeeTable from './components/EmployeeTable';
import EmployeeModals from './components/EmployeeModals';
import AdminUserManager from './components/AdminUserManager';
import ContactInfoSection from './components/ContactInfoSection';
import ShiftsSection from './components/ShiftsSection';
import CalendarMarkersSection from './components/CalendarMarkersSectionNew1.jsx';
import { toast } from 'react-toastify';

// Import data provider hooks
import {
  useEmployeeData,
  useShiftData,
  useContactInfo,
} from './contexts/DataContext.jsx';
import useDataProvider from './contexts/useDataProvider';

const EmployeePage = () => {
  // Use data from the DataProvider
  const {
    data: employees,
    loading: employeeLoading,
    error: employeeError
  } = useEmployeeData();

  const {
    data: contactInfo,
    loading: contactLoading,
    error: contactError
  } = useContactInfo();

  const {
    data: shifts,
    loading: shiftLoading,
    error: shiftError
  } = useShiftData();

  // Use data provider actions
  const {
    addNewEmployee: addEmployee,
    updateExistingEmployee: updateEmployee,
    removeEmployee: deleteEmployee,
    updateContactInfoData: updateContactInfo,
    updateContactInfoData: addContactInfo,
    removeContactInfo: deleteContactInfo,
    updateShiftData: addShift,
    updateShiftData: updateShift,
    removeShift: deleteShift,
    addNewCalendarMarker,
    updateExistingCalendarMarker,
    deleteExistingCalendarMarker,
  } = useDataProvider();

  const [isAddEmployeeModalOpen, setIsAddEmployeeModalOpen] = useState(false);
  const [isEditEmployeeModalOpen, setIsEditEmployeeModalOpen] = useState(false);
  const [currentEmployee, setCurrentEmployee] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [positionFilter, setPositionFilter] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);

  // Memoize employees to prevent unnecessary re-renders
  const memoizedEmployees = useMemo(() => employees, [employees]);

  // Combine loading and error states
  const loading = employeeLoading || contactLoading || shiftLoading;
  const error = employeeError || contactError || shiftError;

  // const handleInitializeRowIndex = useCallback(async () => {
  //   if (isSubmitting || isInitializing) return;
  //   setIsInitializing(true);
  //   try {
  //     await initializeRowIndexInRosters();
  //     toast.success('Row indices initialized successfully!');
  //   } catch (err) {
  //     toast.error(err.message);
  //   } finally {
  //     setIsInitializing(false);
  //   }
  // }, [isSubmitting, isInitializing, initializeRowIndexInRosters]);
// Log renders to track frequency

  const handleAddEmployee = useCallback(
    async (employeeData) => {
      setIsSubmitting(true);
      try {
        await addEmployee(employeeData);
        toast.success('Employee added.');
      } catch (err) {
        toast.error(err.message);
      } finally {
        setIsSubmitting(false);
      }
    },
    [addEmployee]
  );

  const handleUpdateEmployee = useCallback(
    async (employeeId, updatedData) => {
      setIsSubmitting(true);
      try {
        await updateEmployee(employeeId, updatedData);
        toast.success('Employee updated.');
      } catch (err) {
        toast.error(err.message);
      } finally {
        setIsSubmitting(false);
      }
    },
    [updateEmployee]
  );

  const handleDeleteEmployee = useCallback(
    async (employeeId) => {
      setIsSubmitting(true);
      try {
        await deleteEmployee(employeeId);
        toast.success('Employee deleted.');
      } catch (err) {
        toast.error(err.message);
      } finally {
        setIsSubmitting(false);
      }
    },
    [deleteEmployee]
  );

  const handleUpdateContactInfo = useCallback(
    async (key, phone) => {
      setIsSubmitting(true);
      try {
        await updateContactInfo(key, phone);
        toast.success('Contact info updated.');
      } catch (err) {
        toast.error(err.message);
      } finally {
        setIsSubmitting(false);
      }
    },
    [updateContactInfo]
  );

  const handleAddContactInfo = useCallback(
    async (key, phone) => {
      setIsSubmitting(true);
      try {
        await addContactInfo(key, phone);
        toast.success('Contact info added.');
      } catch (err) {
        toast.error(err.message);
      } finally {
        setIsSubmitting(false);
      }
    },
    [addContactInfo]
  );

  const handleDeleteContactInfo = useCallback(
    async (key) => {
      setIsSubmitting(true);
      try {
        await deleteContactInfo(key);
        toast.success('Contact info deleted.');
      } catch (err) {
        toast.error(err.message);
      } finally {
        setIsSubmitting(false);
      }
    },
    [deleteContactInfo]
  );

  const handleAddShift = useCallback(
    async (shiftName, shiftData) => {
      setIsSubmitting(true);
      try {
        await addShift(shiftName, shiftData);
        toast.success('Shift added.');
      } catch (err) {
        toast.error(err.message);
      } finally {
        setIsSubmitting(false);
      }
    },
    [addShift]
  );

  const handleUpdateShift = useCallback(
    async (shiftName, shiftData) => {
      setIsSubmitting(true);
      try {
        await updateShift(shiftName, shiftData);
        toast.success('Shift updated.');
      } catch (err) {
        toast.error(err.message);
      } finally {
        setIsSubmitting(false);
      }
    },
    [updateShift]
  );

  const handleDeleteShift = useCallback(
    async (shiftName) => {
      setIsSubmitting(true);
      try {
        await deleteShift(shiftName);
        toast.success('Shift deleted.');
      } catch (err) {
        toast.error(err.message);
      } finally {
        setIsSubmitting(false);
      }
    },
    [deleteShift]
  );

  const handleAddCalendarMarker = useCallback(
    async (markerType, markerData, allMarkers) => {
      setIsSubmitting(true);
      try {
        // Basic validation (detailed validation is done in action file)
        if (!markerData.name || !markerData.name.trim()) {
          throw new Error('Marker name is required.');
        }

        if (!/^#[0-9A-F]{6}$/i.test(markerData.color)) {
          throw new Error('Invalid color format. Use a hex color (e.g., #FF0000).');
        }

        const timestamp = new Date().toISOString();
        const newMarker = {
          ...markerData,
          createdAt: timestamp,
          updatedAt: timestamp,
        };

        const success = await addNewCalendarMarker(markerType, newMarker);
        if (success) {
          toast.success('Marker added successfully!');
          return true;
        } else {
          throw new Error('Failed to add marker.');
        }
      } catch (err) {
        toast.error(err.message || 'Failed to add marker.');
        return false;
      } finally {
        setIsSubmitting(false);
      }
    },
    [addNewCalendarMarker]
  );

  const handleUpdateCalendarMarker = useCallback(
    async (markerType, markerId, markerData, currentMarker) => {
      setIsSubmitting(true);
      try {
        // Basic validation (detailed validation is done in action file)
        if (!/^#[0-9A-F]{6}$/i.test(markerData.color)) {
          throw new Error('Invalid color format. Use a hex color (e.g., #FF0000).');
        }

        const timestamp = new Date().toISOString();
        const updatedMarker = {
          name: currentMarker.name,
          color: markerData.color,
          createdAt: currentMarker.createdAt,
          updatedAt: timestamp,
          ...(markerType === 'weekday' ? { weekday: markerData.weekday } : {
            startDate: markerData.startDate,
            endDate: markerData.endDate,
            ...(markerType === 'holiday' ? { recurring: markerData.recurring } : {}),
          }),
        };

        const success = await updateExistingCalendarMarker(markerType, markerId, updatedMarker);
        if (success) {
          toast.success('Marker updated successfully!');
          return true;
        } else {
          throw new Error('Failed to update marker.');
        }
      } catch (err) {
        toast.error(err.message || 'Failed to update marker.');
        return false;
      } finally {
        setIsSubmitting(false);
      }
    },
    [updateExistingCalendarMarker]
  );

  const handleDeleteCalendarMarker = useCallback(
    async (markerId, markerType, markerName) => {
      setIsSubmitting(true);
      try {
        if (!window.confirm(`Are you sure you want to delete marker ${markerName}?`)) {
          return false;
        }

        const success = await deleteExistingCalendarMarker(markerId, markerType);
        if (success) {
          toast.success('Marker deleted successfully!');
          return true;
        } else {
          throw new Error('Failed to delete marker.');
        }
      } catch (err) {
        toast.error(err.message || 'Failed to delete marker.');
        return false;
      } finally {
        setIsSubmitting(false);
      }
    },
    [deleteExistingCalendarMarker]
  );

  if (loading) return <div className="p-4">Loading...</div>;
  if (error) return <div className="p-4 text-red-500">Error: {error}</div>;

  return (
    <div className="p-6 pt-20">
      {(isSubmitting || isInitializing) && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-[1000]">
          <div className="animate-spin rounded-full h-10 w-10 border-t-4 border-blue-600"></div>
        </div>
      )}

      <h1 className="text-2xl font-bold mb-4">Settings</h1>
      <div className="mb-4">

        <button
          onClick={() => setIsAddEmployeeModalOpen(true)}
          className="ml-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400"
          disabled={isSubmitting || isInitializing}
        >
          Add Employee
        </button>
      </div>

      <EmployeeTable
        employees={memoizedEmployees}
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        positionFilter={positionFilter}
        setPositionFilter={setPositionFilter}
        onEditEmployee={(emp) => {
          setCurrentEmployee(emp);
          setIsEditEmployeeModalOpen(true);
        }}
        onDeleteEmployee={handleDeleteEmployee}
        isSubmitting={isSubmitting}
        isInitializing={isInitializing}
      />

      <AdminUserManager
        employees={memoizedEmployees}
        isSubmitting={isSubmitting}
        isInitializing={isInitializing}
      />

      <ContactInfoSection
        contactInfo={contactInfo}
        onUpdateContactInfo={handleUpdateContactInfo}
        onAddContactInfo={handleAddContactInfo}
        onDeleteContactInfo={handleDeleteContactInfo}
        isSubmitting={isSubmitting}
        isInitializing={isInitializing}
      />

      <ShiftsSection
        shifts={shifts}
        onAddShift={handleAddShift}
        onUpdateShift={handleUpdateShift}
        onDeleteShift={handleDeleteShift}
        isSubmitting={isSubmitting}
        isInitializing={isInitializing}
      />
     <CalendarMarkersSection
     onAddCalendarMarker={handleAddCalendarMarker}
     onUpdateCalendarMarker={handleUpdateCalendarMarker}
     onDeleteCalendarMarker={handleDeleteCalendarMarker}
        isSubmitting={isSubmitting}
        isInitializing={isInitializing}
      />
      <EmployeeModals
        employees={memoizedEmployees}
        isAddModalOpen={isAddEmployeeModalOpen}
        setIsAddModalOpen={setIsAddEmployeeModalOpen}
        isEditModalOpen={isEditEmployeeModalOpen}
        setIsEditModalOpen={setIsEditEmployeeModalOpen}
        currentEmployee={currentEmployee}
        setCurrentEmployee={setCurrentEmployee}
        onAddEmployee={handleAddEmployee}
        onUpdateEmployee={handleUpdateEmployee}
        isSubmitting={isSubmitting}
        isInitializing={isInitializing}
      />
    </div>
  );
};

export default EmployeePage;