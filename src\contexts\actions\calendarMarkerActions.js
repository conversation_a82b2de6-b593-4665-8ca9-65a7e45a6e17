// src\contexts\actions\calendarMarkerActions.js
import { getDatabase, ref, set, get ,push} from 'firebase/database';
import getDepartmentPath from '../../utils/getDepartmentPath';
import {
  FETCH_CALENDAR_MARKERS_START,
  FETCH_CALENDAR_MARKERS_SUCCESS,
  FETCH_CALENDAR_MARKERS_ERROR
} from '../DataContext';
import dataCache from '../../utils/cacheUtils';

// ===== CALENDAR MARKERS ACTIONS =====

// Fetch calendar markers with caching
export const fetchCalendarMarkers = async (dispatch, forceRefresh = false, selectedDepartment = null) => {
  dispatch({ type: FETCH_CALENDAR_MARKERS_START });

  const cacheKey = `calendar_markers_${selectedDepartment || 'default'}`;

  // Check cache first if not forcing a refresh
  if (!forceRefresh) {
    const cachedData = dataCache.get(cacheKey);
    if (cachedData) {
      console.log('Using cached calendar markers data');
      dispatch({ type: <PERSON>ET<PERSON>_CALENDAR_MARKERS_SUCCESS, payload: cachedData });
      return;
    }
  }

  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);

    const db = getDatabase();
    // Use the correct path to match the existing data structure
    const markersRef = ref(db, `${departmentPath}calendar_markers`);

    get(markersRef)
      .then((snapshot) => {
        console.log('Calendar markers snapshot exists:', snapshot.exists());
        if (snapshot.exists()) {
          const data = snapshot.val();
          console.log('Raw calendar markers data:', data);

          // Map the data to match our expected structure
          const payload = {
            weekdayMarkers: data.weekday_markers || {},
            holidayMarkers: data.holiday_markers || {},
            specialDayMarkers: data.special_day_markers || {}
          };

          console.log('Processed calendar markers payload:', payload);

          // Cache the data for future use (1 day TTL since calendar markers rarely change)
          dataCache.set(cacheKey, payload, 24 * 60 * 60 * 1000);

          dispatch({ type: FETCH_CALENDAR_MARKERS_SUCCESS, payload });
        } else {
          const emptyPayload = {
            weekdayMarkers: {},
            holidayMarkers: {},
            specialDayMarkers: {}
          };
          dispatch({ type: FETCH_CALENDAR_MARKERS_SUCCESS, payload: emptyPayload });
        }
      })
      .catch((err) => {
        dispatch({
          type: FETCH_CALENDAR_MARKERS_ERROR,
          payload: new Error('Failed to fetch calendar markers: ' + err.message)
        });
      });
  } catch (err) {
    console.error('Error getting department path:', err);
    dispatch({
      type: FETCH_CALENDAR_MARKERS_ERROR,
      payload: new Error('Failed to get department path: ' + err.message)
    });
  }
};
export const addCalendarMarker = async (dispatch, markerType, markerData, selectedDepartment = null) => {
  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);
    const db = getDatabase();
    const basePath = `${departmentPath}calendar_markers`;
    const key = `${markerType}_markers`;
    const markerRef = push(ref(db, `${basePath}/${key}`));
    await set(markerRef, markerData);

    // Optimistic UI update
    dispatch({
      type: FETCH_CALENDAR_MARKERS_SUCCESS,
      payload: {
        [key]: {
          ...state.calendarMarkers[key],
          [markerRef.key]: markerData
        },
        weekdayMarkers: state.calendarMarkers.weekdayMarkers,
        holidayMarkers: state.calendarMarkers.holidayMarkers,
        specialDayMarkers: state.calendarMarkers.specialDayMarkers
      }
    });

    return markerRef.key;
  } catch (err) {
    console.error('Error adding calendar marker:', err);
    dispatch({ type: FETCH_CALENDAR_MARKERS_ERROR, payload: err.message });
    throw err;
  }
};
export const updateCalendarMarker = async (dispatch, markerType, markerId, markerData, selectedDepartment = null) => {
  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);
    const db = getDatabase();
    const basePath = `${departmentPath}calendar_markers`;
    const key = `${markerType}_markers`;
    const markerRef = ref(db, `${basePath}/${key}/${markerId}`);
    await set(markerRef, markerData);

    // Optimistic UI update
    dispatch({
      type: FETCH_CALENDAR_MARKERS_SUCCESS,
      payload: {
        [key]: {
          ...state.calendarMarkers[key],
          [markerId]: markerData
        },
        weekdayMarkers: state.calendarMarkers.weekdayMarkers,
        holidayMarkers: state.calendarMarkers.holidayMarkers,
        specialDayMarkers: state.calendarMarkers.specialDayMarkers
      }
    });
  } catch (err) {
    console.error('Error updating calendar marker:', err);
    dispatch({ type: FETCH_CALENDAR_MARKERS_ERROR, payload: err.message });
    throw err;
  }
};
export const deleteCalendarMarker = async (dispatch, markerType, markerId, selectedDepartment = null) => {
  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);
    const db = getDatabase();
    const basePath = `${departmentPath}calendar_markers`;
    const key = `${markerType}_markers`;
    const markerRef = ref(db, `${basePath}/${key}/${markerId}`);
    await remove(markerRef);

    // Optimistic UI update
    const updatedMarkers = { ...state.calendarMarkers[key] };
    delete updatedMarkers[markerId];

    dispatch({
      type: FETCH_CALENDAR_MARKERS_SUCCESS,
      payload: {
        [key]: updatedMarkers,
        weekdayMarkers: state.calendarMarkers.weekdayMarkers,
        holidayMarkers: state.calendarMarkers.holidayMarkers,
        specialDayMarkers: state.calendarMarkers.specialDayMarkers
      }
    });
  } catch (err) {
    console.error('Error deleting calendar marker:', err);
    dispatch({ type: FETCH_CALENDAR_MARKERS_ERROR, payload: err.message });
    throw err;
  }
};