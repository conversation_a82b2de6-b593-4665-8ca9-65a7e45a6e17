//src\RosterTable.js
import React from 'react';
import { RosterTableContainer } from './components/RosterTable';

const RosterTable = ({ rosterData, setRosterData, shiftTypes, shiftHours, shiftColors, daysOfWeek, year, month,
  weekdayMarkers,
  holidayMarkers,
  isRosterDraft=true,
  specialDayMarkers,
  changeYear,
  changeMonth }) =>
     {
  return (
    <RosterTableContainer
      rosterData={rosterData}
      setRosterData={setRosterData}
      shiftTypes={shiftTypes}
      shiftHours={shiftHours}
      shiftColors={shiftColors}
      daysOfWeek={daysOfWeek}
      year={year}
      month={month}
      weekdayMarkers={weekdayMarkers}
      holidayMarkers={holidayMarkers}
      isRosterDraft={isRosterDraft}
      specialDayMarkers={specialDayMarkers}
      changeYear={changeYear}
      changeMonth={changeMonth}
    />
  );
};

export default RosterTable;