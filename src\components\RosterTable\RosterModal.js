// src/components/RosterTable/RosterModal.js
import React from 'react';

/**
 * RosterModal component - Modal for dialog mode
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.isModalOpen - Whether the modal is open
 * @param {string} props.modalTechId - Selected technician ID
 * @param {number} props.modalStartDay - Start day
 * @param {number} props.modalEndDay - End day
 * @param {Object} props.modalShifts - Shifts for each day
 * @param {Function} props.setModalTechId - Function to set technician ID
 * @param {Function} props.setModalStartDay - Function to set start day
 * @param {Function} props.setModalEndDay - Function to set end day
 * @param {Function} props.setModalShifts - Function to set shifts
 * @param {Function} props.closeModal - Function to close the modal
 * @param {Function} props.handleModalSubmit - Function to handle modal submission
 * @param {Array} props.rosterData - The roster data
 * @param {Array} props.shiftTypes - Available shift types
 * @param {number} props.daysInMonth - Number of days in the month
 * @returns {JSX.Element|null} RosterModal component or null if not open
 */
const RosterModal = ({
  isModalOpen,
  modalTechId,
  modalStartDay,
  modalEndDay,
  modalShifts,
  setModalTechId,
  setModalStartDay,
  setModalEndDay,
  setModalShifts,
  closeModal,
  handleModalSubmit,
  rosterData,
  shiftTypes,
  daysInMonth
}) => {
  if (!isModalOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h2 className="text-xl font-bold mb-4">Update Shifts</h2>
        <div className="space-y-4">
          {/* Technician Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Technician</label>
            <select
              value={modalTechId}
              onChange={(e) => {
                const techId = e.target.value;
                setModalTechId(techId);

                if (techId) {
                  const tech = rosterData.find(t => t.employeeId === techId);
                  if (tech) {
                    const start = parseInt(modalStartDay);
                    const end = parseInt(modalEndDay);

                    // Reset shifts when changing technician
                    const newShifts = {};
                    for (let day = start; day <= end && day <= daysInMonth; day++) {
                      const dayStr = day.toString();
                      newShifts[dayStr] = tech[dayStr] || '';
                    }
                    setModalShifts(newShifts);
                  }
                } else {
                  // Clear shifts if no technician selected
                  setModalShifts({});
                }
              }}
              className="w-full p-2 border rounded-md"
            >
              <option value="">Select Technician</option>
              {rosterData.map(tech => (
                <option key={tech.employeeId} value={tech.employeeId}>
                  {tech['Name']} ({tech.employeeId})
                </option>
              ))}
            </select>
          </div>

          {/* Date Range Selection */}
          <div className="flex space-x-4">
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-1">Start Day</label>
              <select
                value={modalStartDay}
                onChange={(e) => {
                  const start = parseInt(e.target.value);
                  let end = parseInt(modalEndDay);
                  if (start > end) end = start;
                  setModalStartDay(start);
                  setModalEndDay(end);
                }}
                className="w-full p-2 border rounded-md"
              >
                {Array.from({ length: daysInMonth }, (_, i) => i + 1).map(day => (
                  <option key={day} value={day}>{day}</option>
                ))}
              </select>
            </div>
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-1">End Day</label>
              <select
                value={modalEndDay}
                onChange={(e) => setModalEndDay(parseInt(e.target.value))}
                className="w-full p-2 border rounded-md"
              >
                {Array.from({ length: daysInMonth }, (_, i) => i + 1)
                  .filter(day => day >= modalStartDay)
                  .map(day => (
                    <option key={day} value={day}>{day}</option>
                  ))}
              </select>
            </div>
          </div>

          {/* Shift Selection for Each Day */}
          {modalTechId && modalStartDay <= modalEndDay && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Shifts</label>
              {Array.from({ length: Math.min(modalEndDay - modalStartDay + 1, 7) }, (_, i) => {
                const day = (modalStartDay + i).toString();
                return (
                  <div key={day} className="flex items-center space-x-2 mb-2">
                    <span className="w-12 text-sm">Day {day}</span>
                    <select
                      value={modalShifts[day] || ''}
                      onChange={(e) => {
                        const newValue = e.target.value;
                        setModalShifts(prevShifts => ({
                          ...prevShifts,
                          [day]: newValue
                        }));
                      }}
                      className="w-24 p-2 border rounded-md text-sm"
                    >
                      <option value="">-- Select Shift --</option>
                      {shiftTypes.map(shift => (
                        <option key={shift} value={shift}>{shift}</option>
                      ))}
                    </select>
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* Modal Buttons */}
        <div className="mt-6 flex justify-end space-x-2">
          <button
            onClick={closeModal}
            className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
          >
            Cancel
          </button>
          <button
            onClick={handleModalSubmit}
            disabled={!modalTechId}
            className={`px-4 py-2 rounded-md text-white ${modalTechId ? 'bg-blue-600 hover:bg-blue-700' : 'bg-gray-400 cursor-not-allowed'}`}
          >
            Update
          </button>
        </div>
      </div>
    </div>
  );
};

export default RosterModal;
