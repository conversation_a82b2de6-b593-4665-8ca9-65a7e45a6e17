// src/components/RosterTable/RosterControls.js
import React from 'react';

/**
 * RosterControls component - Displays mode selection and action buttons
 *
 * @param {Object} props - Component props
 * @param {string} props.mode - Current mode ('update', 'drag', 'dialog', 'view', 'employeeReorder', 'crossMonthDrag')
 * @param {Function} props.setMode - Function to set mode
 * @param {boolean} props.isRosterDraft - Whether the roster is in draft mode
 * @param {boolean} props.hasSelection - Whether there is an active selection
 * @param {Function} props.handleClearSelection - Function to clear selection
 * @param {number} props.historyIndex - Current history index
 * @param {Function} props.handleUndo - Function to undo changes
 * @param {Function} props.handleSave - Function to save changes
 * @param {boolean} props.isSaving - Whether saving is in progress
 * @param {Array} props.changedCells - Cells that have been changed
 * @param {boolean} props.hasWriteAccess - Whether the user has write access to the department
 * @returns {JSX.Element} RosterControls component
 */
const RosterControls = ({
  mode,
  setMode,
  isRosterDraft,
  hasSelection,
  handleClearSelection,
  historyIndex,
  handleUndo,
  handleSave,
  isSaving,
  changedCells,
  hasWriteAccess
}) => {
  return (
    <>
      {/* Mode Selection Radio Buttons */}
      <div style={{ marginBottom: '1rem', display: 'flex', gap: '1rem', alignItems: 'center' }}>
        <label style={{
          display: 'flex',
          alignItems: 'center',
          cursor: (isRosterDraft && hasWriteAccess) ? 'pointer' : 'not-allowed',
          opacity: (isRosterDraft && hasWriteAccess) ? 1 : 0.6
        }}>
          <input
            type="radio"
            name="mode"
            value="update"
            checked={mode === 'update'}
            onChange={() => (isRosterDraft && hasWriteAccess) && setMode('update')}
            style={{ marginRight: '0.5rem' }}
            disabled={!(isRosterDraft && hasWriteAccess)}
          />
          Update Mode
        </label>
        <label style={{
          display: 'flex',
          alignItems: 'center',
          cursor: (isRosterDraft && hasWriteAccess) ? 'pointer' : 'not-allowed',
          opacity: (isRosterDraft && hasWriteAccess) ? 1 : 0.6
        }}>
          <input
            type="radio"
            name="mode"
            value="drag"
            checked={mode === 'drag'}
            onChange={() => (isRosterDraft && hasWriteAccess) && setMode('drag')}
            style={{ marginRight: '0.5rem' }}
            disabled={!(isRosterDraft && hasWriteAccess)}
          />
          Drag Mode
        </label>
        <label style={{
          display: 'flex',
          alignItems: 'center',
          cursor: (isRosterDraft && hasWriteAccess) ? 'pointer' : 'not-allowed',
          opacity: (isRosterDraft && hasWriteAccess) ? 1 : 0.6
        }}>
          <input
            type="radio"
            name="mode"
            value="dialog"
            checked={mode === 'dialog'}
            onChange={() => (isRosterDraft && hasWriteAccess) && setMode('dialog')}
            style={{ marginRight: '0.5rem' }}
            disabled={!(isRosterDraft && hasWriteAccess)}
          />
          Dialog Mode
        </label>

        <label style={{
          display: 'flex',
          alignItems: 'center',
          cursor: (isRosterDraft && hasWriteAccess) ? 'pointer' : 'not-allowed',
          opacity: (isRosterDraft && hasWriteAccess) ? 1 : 0.6
        }}>
          <input
            type="radio"
            name="mode"
            value="employeeReorder"
            checked={mode === 'employeeReorder'}
            onChange={() => (isRosterDraft && hasWriteAccess) && setMode('employeeReorder')}
            style={{ marginRight: '0.5rem' }}
            disabled={!(isRosterDraft && hasWriteAccess)}
          />
          Employee Reorder Mode
        </label>

        <label style={{
          display: 'flex',
          alignItems: 'center',
          cursor: (isRosterDraft && hasWriteAccess) ? 'pointer' : 'not-allowed',
          opacity: (isRosterDraft && hasWriteAccess) ? 1 : 0.6
        }}>
          <input
            type="radio"
            name="mode"
            value="crossMonthDrag"
            checked={mode === 'crossMonthDrag'}
            onChange={() => (isRosterDraft && hasWriteAccess) && setMode('crossMonthDrag')}
            style={{ marginRight: '0.5rem' }}
            disabled={!(isRosterDraft && hasWriteAccess)}
          />
          📅 Cross-Month Drag Mode
        </label>

        {(!isRosterDraft || !hasWriteAccess) && (
          <div style={{
            padding: '0.5rem 1rem',
            backgroundColor: '#f3f4f6',
            borderRadius: '0.375rem',
            color: '#4b5563',
            fontWeight: 'bold',
            marginLeft: 'auto'
          }}>
            {!isRosterDraft ? 'View Only Mode - Editing disabled' : 'No Write Access - Editing disabled'}
          </div>
        )}
      </div>

      {/* Cross-Month Drag Mode Instructions */}
      {mode === 'crossMonthDrag' && (
        <div style={{
          marginBottom: '1rem',
          padding: '0.75rem',
          backgroundColor: '#e0f2fe',
          borderRadius: '0.375rem',
          border: '1px solid #0284c7',
          fontSize: '0.875rem',
          color: '#0c4a6e'
        }}>
          <div style={{ fontWeight: 'bold', marginBottom: '0.25rem' }}>
            📅 Cross-Month Drag Mode Active
          </div>
          <div>
            • Select cells and drag to month navigation to copy to another month<br/>
            • Drag across month boundaries for Excel-like sheet-to-sheet copying<br/>
            • Rectangular selections will maintain their shape in the destination month
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div style={{ marginBottom: '1.5rem', display: 'flex', gap: '1rem' }}>
        {(mode === 'drag' || mode === 'crossMonthDrag') && hasSelection && hasWriteAccess && (
          <button
            onClick={handleClearSelection}
            style={{
              padding: '0.5rem 1rem',
              backgroundColor: '#6b7280',
              color: 'white',
              borderRadius: '0.375rem',
              cursor: 'pointer'
            }}
          >
            Clear Selection
          </button>
        )}
        {historyIndex > 0 && hasWriteAccess && (
          <button
            onClick={handleUndo}
            style={{
              padding: '0.5rem 1rem',
              backgroundColor: '#ef4444',
              color: 'white',
              borderRadius: '0.375rem',
              cursor: 'pointer'
            }}
          >
            Undo Change
          </button>
        )}
        {historyIndex > 0 && hasWriteAccess && (
          <button
            onClick={handleSave}
            disabled={isSaving || changedCells.length === 0}
            style={{
              padding: '0.5rem 1rem',
              backgroundColor: '#3b82f6',
              color: 'white',
              borderRadius: '0.375rem',
              cursor: changedCells.length > 0 ? 'pointer' : 'not-allowed',
              opacity: isSaving || changedCells.length === 0 ? 0.5 : 1
            }}
          >
            {isSaving ? 'Saving...' : 'Save Changes'}
          </button>
        )}
      </div>
    </>
  );
};

export default RosterControls;
