{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "22"}, "main": "index.js", "dependencies": {"firebase-admin": "^12.6.0", "firebase-functions": "^6.0.1", "telegraf": "^4.16.3", "twilio": "^5.6.0"}, "devDependencies": {"firebase-functions-test": "^3.1.0"}, "private": true}