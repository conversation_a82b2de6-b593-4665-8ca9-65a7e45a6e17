// App.js
import React,{ useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { AuthProvider, AuthContext } from './components/AuthProvider';
import { DataProvider } from './contexts/DataContext';
import Navbar from './Navbar';
import Home from './Home';
import EmployeeDocs from './EmployeeDocs';
import EmployeePage from './EmployeePage';
import MobileRoster from './MobileRoster';
import About from './About';
import LoginPage from './components/LoginPage';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import './App.css';
import RosterPageView from './RosterPageView';
import EmployeeSearch from './EmployeeSearch';
import DataProviderExample from './components/DataProviderExample';
import CacheStatus from './components/CacheStatus';
import PerformanceMonitor from './components/PerformanceMonitor';


const PrivateRoute = ({ children, allowedRoles, requireWriteAccess = false }) => {
  const {
    currentUser,
    userRole,
    userDepartment,
    selectedDepartment,
    loading,
    isSuperAdmin,
    isRegionManager,
    isSuperUser,
    isRegionUser,
    canSwitchDepartments,
    hasAccessToDepartment,
    hasWriteAccessToDepartment
  } = React.useContext(AuthContext);

  console.log('PrivateRoute: Checking auth', {
    currentUser,
    userRole,
    userDepartment,
    loading,
    allowedRoles,
    requireWriteAccess
  });

  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  if (loading) {
    return <div className="text-center mt-8">Loading...</div>;
  }

  if (!currentUser) {
    return <Navigate to="/login" />;
  }

  // For users who can switch departments, ensure they have a selected department
  if (canSwitchDepartments() && !selectedDepartment && window.location.pathname !== '/') {
    return <Navigate to="/" />;
  }

  // Department access check
  const deptToCheck = canSwitchDepartments() ? selectedDepartment : userDepartment;
  if (deptToCheck && !hasAccessToDepartment(deptToCheck)) {
    return <div className="text-center mt-8">You don't have access to this department.</div>;
  }

  // Write access check
  if (requireWriteAccess && deptToCheck && !hasWriteAccessToDepartment(deptToCheck)) {
    return <div className="text-center mt-8">You don't have write access to this department.</div>;
  }

  // Conditional routing for mobile devices
  if (isMobile) {
    // For home route
    if (allowedRoles.includes('admin') && window.location.pathname === '/') {
      console.log('Redirecting small screen user from home to /mobile-roster');
      return <Navigate to="/mobile-roster" />;
    }

    // For roster route
    if (allowedRoles.includes('admin') && window.location.pathname === '/roster') {
      console.log('Redirecting small screen user from roster to /mobile-roster');
      return <Navigate to="/mobile-roster" />;
    }

    // For employees route
    if (allowedRoles.includes('admin') && window.location.pathname === '/employees') {
      console.log('Redirecting small screen user from employees to /mobile-roster');
      return <Navigate to="/mobile-roster" />;
    }
    // For employees-docs route
    if (allowedRoles.includes('admin') && window.location.pathname === '/employees-docs') {
      console.log('Redirecting small screen user from employees to /mobile-roster');
      return <Navigate to="/mobile-roster" />;
    }
  }

  if (allowedRoles && !allowedRoles.includes(userRole) && !isSuperAdmin() && !isRegionManager) {
    return <Navigate to={userRole === 'user' || isSuperUser || isRegionUser ? '/mobile-roster' : '/'} />;
  }

  return children;
};

const AppContent = () => {
  const location = useLocation();
  const showNavbar = !['/login'].includes(location.pathname);
  const { userRole } = React.useContext(AuthContext);

  return (
    <div className="min-h-screen flex flex-col">
      <ToastContainer position="top-right" autoClose={3000} />
      {showNavbar && <Navbar />}
      <main className="flex-1 w-full  p-4">
        <Routes>
          <Route path="/login" element={<LoginPage />} />

          <Route path="/" element={
            <PrivateRoute allowedRoles={['admin', 'region-manager']}>
              <Home />
            </PrivateRoute>
          } />

          <Route path="/roster" element={
            <PrivateRoute allowedRoles={['user', 'admin', 'region-manager', 'super-user', 'region-user']}>
              <RosterPageView />
            </PrivateRoute>
          } />

          <Route path="/mobile-roster" element={
            <PrivateRoute allowedRoles={['user', 'admin', 'region-manager', 'super-user', 'region-user']}>
              <MobileRoster />
            </PrivateRoute>
          } />

          <Route path="/employee-search" element={
            <PrivateRoute allowedRoles={['user', 'admin', 'region-manager', 'super-user', 'region-user']}>
              <EmployeeSearch />
            </PrivateRoute>
          } />

          <Route path="/employees" element={
            <PrivateRoute allowedRoles={['admin', 'region-manager']} requireWriteAccess={true}>
              <EmployeePage />
            </PrivateRoute>
          } />

          <Route path="/employees-docs" element={
            <PrivateRoute allowedRoles={['admin', 'region-manager']} requireWriteAccess={true}>
              <EmployeeDocs />
            </PrivateRoute>
          } />

          <Route path="/about" element={
            <PrivateRoute allowedRoles={['user', 'admin', 'region-manager', 'super-user', 'region-user']}>
              <About />
            </PrivateRoute>
          } />

          <Route path="/data-provider-example" element={
            <PrivateRoute allowedRoles={['admin', 'region-manager']}>
              <DataProviderExample />
            </PrivateRoute>
          } />

          <Route path="*" element={
            <div className="text-center mt-8">404 Not Found</div>
          } />
        </Routes>
      </main>

      {/* Show cache status and performance monitor for admin and region-manager users */}
      {(userRole === 'admin' || userRole === 'region-manager') && (
        <>
          <CacheStatus />
          <PerformanceMonitor />
        </>
      )}
    </div>
  );
};

function App() {
  return (
    <AuthProvider>
      <DataProvider>
        <Router>
          <AppContent />
        </Router>
      </DataProvider>
    </AuthProvider>
  );
}

export default App;
