// src/hooks/RosterTable/useRowDragAndDrop.js
import { useCallback, useState } from 'react';

/**
 * Custom hook to manage row-level drag and drop functionality in the roster table
 * This allows dragging entire employee rows to reorder them (changing rowIndex)
 *
 * @param {Array} rosterData - The roster data
 * @param {string} mode - Current mode ('update', 'drag', 'dialog', 'view', 'rowDrag')
 * @param {boolean} isRosterDraft - Whether the roster is in draft mode
 * @param {Function} onRowReorder - Callback when rows are reordered
 * @returns {Object} Row drag and drop handlers and state
 */
const useRowDragAndDrop = (
  rosterData,
  mode,
  isRosterDraft,
  onRowReorder
) => {
  const [draggedRowId, setDraggedRowId] = useState(null);
  const [dropTargetRowId, setDropTargetRowId] = useState(null);

  // Handle drag start for employee rows
  const handleRowDragStart = useCallback((e, employeeId) => {
    // Prevent drag if not in row drag mode or not in draft
    if (!isRosterDraft || mode !== 'rowDrag') {
      e.preventDefault();
      return false;
    }

    setDraggedRowId(employeeId);
    
    // Find the employee being dragged
    const draggedEmployee = rosterData.find(emp => emp.employeeId === employeeId);
    if (!draggedEmployee) {
      e.preventDefault();
      return false;
    }

    // Set drag data
    e.dataTransfer.setData('text/plain', JSON.stringify({
      type: 'employeeRow',
      employeeId: employeeId,
      employeeName: draggedEmployee.Name || draggedEmployee.name,
      currentRowIndex: draggedEmployee.rowIndex
    }));

    // Create drag preview
    const dragPreview = document.createElement('div');
    dragPreview.style.position = 'absolute';
    dragPreview.style.top = '-1000px';
    dragPreview.style.left = '-1000px';
    dragPreview.style.padding = '8px 16px';
    dragPreview.style.background = '#3b82f6';
    dragPreview.style.color = 'white';
    dragPreview.style.opacity = '0.9';
    dragPreview.style.border = '2px solid #1d4ed8';
    dragPreview.style.borderRadius = '6px';
    dragPreview.style.fontWeight = 'bold';
    dragPreview.style.fontSize = '14px';
    dragPreview.innerText = `Moving: ${draggedEmployee.Name || draggedEmployee.name}`;
    document.body.appendChild(dragPreview);
    e.dataTransfer.setDragImage(dragPreview, 0, 0);
    setTimeout(() => document.body.removeChild(dragPreview), 0);

    return true;
  }, [isRosterDraft, mode, rosterData]);

  // Handle drag over for employee rows
  const handleRowDragOver = useCallback((e, targetEmployeeId) => {
    if (!isRosterDraft || mode !== 'rowDrag' || !draggedRowId) return;
    
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    
    // Only set drop target if it's different from dragged row
    if (targetEmployeeId !== draggedRowId) {
      setDropTargetRowId(targetEmployeeId);
    }
  }, [isRosterDraft, mode, draggedRowId]);

  // Handle drop for employee rows
  const handleRowDrop = useCallback((e, targetEmployeeId) => {
    if (!isRosterDraft || mode !== 'rowDrag') return;
    
    e.preventDefault();
    setDraggedRowId(null);
    setDropTargetRowId(null);

    try {
      const dataStr = e.dataTransfer.getData('text/plain');
      if (!dataStr) return;

      const dragData = JSON.parse(dataStr);
      if (dragData.type !== 'employeeRow') return;

      const draggedEmployeeId = dragData.employeeId;
      
      // Don't allow dropping on self
      if (draggedEmployeeId === targetEmployeeId) return;

      // Find the dragged and target employees
      const draggedEmployee = rosterData.find(emp => emp.employeeId === draggedEmployeeId);
      const targetEmployee = rosterData.find(emp => emp.employeeId === targetEmployeeId);
      
      if (!draggedEmployee || !targetEmployee) {
        console.error('Could not find dragged or target employee');
        return;
      }

      const oldRowIndex = draggedEmployee.rowIndex;
      const newRowIndex = targetEmployee.rowIndex;

      // Don't do anything if dropping in the same position
      if (oldRowIndex === newRowIndex) return;

      // Call the reorder callback with the necessary information
      if (onRowReorder) {
        onRowReorder({
          employeeId: draggedEmployeeId,
          employeeName: draggedEmployee.Name || draggedEmployee.name,
          oldRowIndex,
          newRowIndex
        });
      }

    } catch (error) {
      console.error('Error during row drop operation:', error);
    }
  }, [isRosterDraft, mode, rosterData, onRowReorder]);

  // Handle drag end
  const handleRowDragEnd = useCallback(() => {
    setDraggedRowId(null);
    setDropTargetRowId(null);
  }, []);

  // Handle drag leave
  const handleRowDragLeave = useCallback((e) => {
    // Only clear drop target if we're actually leaving the row area
    if (!e.currentTarget.contains(e.relatedTarget)) {
      setDropTargetRowId(null);
    }
  }, []);

  return {
    draggedRowId,
    dropTargetRowId,
    handleRowDragStart,
    handleRowDragOver,
    handleRowDrop,
    handleRowDragEnd,
    handleRowDragLeave
  };
};

export default useRowDragAndDrop;
