// src/useFetchEmployeesData.js
import {  ref, get } from 'firebase/database';
import { database } from './firebaseConfig.js';
import { useState, useEffect } from 'react';

const useFetchEmployeesData = () => {
  const [employeesData, setEmployeesData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchEmployeesData = async () => {
      setLoading(true);
      setError(null);

      try {
        const employeesRef = ref(database, 'employees'); // Reference to the 'employees' node

        const snapshot = await get(employeesRef);

        if (snapshot.exists()) {
          setEmployeesData(snapshot.val());
          console.log("Employees Data:", snapshot.val());
        } else {
          setEmployeesData({}); // Or null, depending on your needs
          console.log("No data available for employees");
        }
      } catch (err) {
        setError(err);
        console.error("Error fetching employees data:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchEmployeesData();
  }, []);

  return { employeesData, loading, error };
};

export default useFetchEmployeesData;
