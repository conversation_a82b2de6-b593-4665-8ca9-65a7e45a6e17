// src/utils/rosterUtils.js

/**
 * Get markers for a specific day
 * @param {number} year - The year
 * @param {number} month - The month (1-12)
 * @param {number} day - The day of the month
 * @param {Object} weekdayMarkers - Weekday markers
 * @param {Object} holidayMarkers - Holiday markers
 * @param {Object} specialDayMarkers - Special day markers
 * @returns {Array} Array of markers for the day
 */
export const getMarkersForDay = (year, month, day, weekdayMarkers, holidayMarkers, specialDayMarkers) => {
  const date = new Date(year, month - 1, day);
  const weekday = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][date.getDay()];
  const markers = [];

  // Weekday Markers (apply to all matching weekdays)
  Object.entries(weekdayMarkers || {}).forEach(([id, marker]) => {
    if (marker.weekday === weekday) {
      markers.push({ ...marker, id, type: 'weekday' });
    }
  });

  // Holiday Markers (only for current year and month)
  Object.entries(holidayMarkers || {}).forEach(([id, marker]) => {
    const startDate = new Date(marker.startDate.year, marker.startDate.month - 1, marker.startDate.day);
    const endDate = new Date(marker.endDate.year, marker.endDate.month - 1, marker.endDate.day);
    if (marker.recurring) {
      if (date.getMonth() + 1 === marker.startDate.month && date.getDate() >= marker.startDate.day && date.getDate() <= marker.endDate.day) {
        markers.push({ ...marker, id, type: 'holiday' });
      }
    } else if (
      date >= startDate &&
      date <= endDate &&
      (marker.startDate.year === year || marker.endDate.year === year) &&
      (marker.startDate.month === month || marker.endDate.month === month)
    ) {
      markers.push({ ...marker, id, type: 'holiday' });
    }
  });

  // Special Day Markers (only for current year and month)
  Object.entries(specialDayMarkers || {}).forEach(([id, marker]) => {
    const startDate = new Date(marker.startDate.year, marker.startDate.month - 1, marker.startDate.day);
    const endDate = new Date(marker.endDate.year, marker.endDate.month - 1, marker.endDate.day);
    if (
      date >= startDate &&
      date <= endDate &&
      (marker.startDate.year === year || marker.endDate.year === year) &&
      (marker.startDate.month === month || marker.endDate.month === month)
    ) {
      markers.push({ ...marker, id, type: 'special_day' });
    }
  });

  // Sort by priority: holiday > special_day > weekday
  return markers.sort((a, b) => {
    const priority = { holiday: 1, special_day: 2, weekday: 3 };
    return priority[a.type] - priority[b.type];
  });
};

/**
 * Check if an employee is new (no shifts assigned)
 * @param {Object} employee - The employee object
 * @returns {boolean} True if the employee is new
 */
export const isNewEmployee = (employee) => {
  return !Object.keys(employee).some(key => /^\d+$/.test(key));
};

/**
 * Calculate the number of days in a month
 * @param {number} year - The year
 * @param {number} month - The month (1-12)
 * @returns {number} Number of days in the month
 */
export const getDaysInMonth = (year, month) => {
  return new Date(year, month, 0).getDate();
};

/**
 * Format hours and minutes
 * @param {number} totalMinutes - Total minutes
 * @returns {string} Formatted hours and minutes (HH:MM)
 */
export const formatHoursMinutes = (totalMinutes) => {
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;
  return `${hours}:${minutes.toString().padStart(2, '0')}`;
};

/**
 * Calculate totals for roster data
 * @param {Array} rosterData - The roster data
 * @param {number} daysInMonth - Number of days in the month
 * @returns {Object} Totals for various metrics
 */
export const calculateTotals = (rosterData) => {
  const totals = rosterData.reduce((acc, tech) => {
    acc['Leave days'] += parseInt(tech['Leave days']) || 0;
    acc['Sick days'] += parseInt(tech['Sick days']) || 0;
    acc['Work days'] += parseInt(tech['Work days']) || 0;
    const workTimeParts = (tech['Work Time'] || '0:00').split(':').map(Number);
    acc['Work Time'] += workTimeParts[0] * 60 + workTimeParts[1];
    acc['Overtime days'] += parseInt(tech['Overtime days']) || 0;
    const overtimeParts = (tech['Overtime'] || '0:00').split(':').map(Number);
    acc['Overtime'] += overtimeParts[0] * 60 + overtimeParts[1];
    const aircraftParts = (tech['Aircraft'] || '0:00').split(':').map(Number);
    acc['Aircraft'] += aircraftParts[0] * 60 + aircraftParts[1];
    const officeParts = (tech['Office'] || '0:00').split(':').map(Number);
    acc['Office'] += officeParts[0] * 60 + officeParts[1];
    return acc;
  }, { 'Leave days': 0, 'Sick days': 0, 'Work days': 0, 'Work Time': 0, 'Overtime days': 0, 'Overtime': 0, 'Aircraft': 0, 'Office': 0 });

  // Format time values
  totals['Work Time'] = formatHoursMinutes(totals['Work Time']);
  totals['Overtime'] = formatHoursMinutes(totals['Overtime']);
  totals['Aircraft'] = formatHoursMinutes(totals['Aircraft']);
  totals['Office'] = formatHoursMinutes(totals['Office']);

  return totals;
};

/**
 * Default shift colors
 */
export const defaultShiftColors = {
  'D': '#ffffff',
  'D1': '#fff2af',
  'D2': '#4a90e2',
  'D3': '#4a90e2',
  'N': '#d9e1f2',
  'N1': '#d9e1f2',
  'N2': '#d9e1f2',
  'N3': '#d9e1f2',
  'O': '#93C47D',
  'X': '#f1c40f',
  'R': '#f1c40f',
  'SL': '#6d9eeb',
  'TR': '#00FFFF',
  'DT': '#808080'
};

/**
 * Update work metrics for an employee
 * @param {Object} employee - The employee object
 * @param {Object} shiftHours - Hours for each shift type
 * @param {number} daysInMonth - Number of days in the month
 * @returns {Object} Updated employee object with recalculated metrics
 */
export const updateEmployeeWorkMetrics = (employee, shiftHours, daysInMonth) => {
  const updatedEmployee = { ...employee };
  let workDays = 0;
  let totalHours = 0;

  for (let day = 1; day <= daysInMonth; day++) {
    const shift = updatedEmployee[day.toString()];
    if (shift && shiftHours[shift] > 0) {
      workDays++;
      totalHours += shiftHours[shift];
    }
  }

  updatedEmployee['Work days'] = workDays;
  updatedEmployee['Work Time'] = formatHoursMinutes(totalHours);
  
  return updatedEmployee;
};
