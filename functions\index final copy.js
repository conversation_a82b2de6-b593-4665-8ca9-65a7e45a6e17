// functions/index.js
const functions = require('firebase-functions');
const admin = require('firebase-admin');

admin.initializeApp();

exports.deleteUser = functions.https.onCall(async (data, context) => {
  // Ensure the caller is an admin
  if (!context.auth || !context.auth.uid) {
    throw new functions.https.HttpsError('unauthenticated', 'You must be signed in.');
  }
  const userRef = admin.database().ref(`users/${context.auth.uid}`);
  const snapshot = await userRef.once('value');
  if (!snapshot.exists() || snapshot.val().role !== 'admin') {
    throw new functions.https.HttpsError('permission-denied', 'Only admins can delete users.');
  }

  const { uid } = data;
  if (!uid) {
    throw new functions.https.HttpsError('invalid-argument', 'User ID is required.');
  }
  if (uid === context.auth.uid) {
    throw new functions.https.HttpsError('failed-precondition', 'Cannot delete your own account.');
  }

  try {
    // Delete from Firebase Auth
    await admin.auth().deleteUser(uid);
    // Delete from Realtime Database
    await admin.database().ref(`users/${uid}`).remove();
    return { success: true };
  } catch (error) {
    console.error('Error deleting user:', error);
    throw new functions.https.HttpsError('internal', `Failed to delete user: ${error.message}`);
  }
});