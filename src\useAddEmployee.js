import { useState, useContext, useEffect } from 'react';
import { getDatabase, ref, get, update, runTransaction } from 'firebase/database';
import { AuthContext } from './components/AuthProvider';

const useAddEmployee = (year, month) => {
  const { userDepartment, selectedDepartment, isRegionManager, isSuperAdmin, isSuperUser, isRegionUser } = useContext(AuthContext);

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [departmentPath, setDepartmentPath] = useState('');

  // Set department path when department changes
  useEffect(() => {
    const deptToUse = isRegionManager || isSuperAdmin() || isSuperUser || isRegionUser ? selectedDepartment : userDepartment;
    if (deptToUse) {
      const path = `departments/${deptToUse}/`;
      console.log('useAddEmployee: Setting department path:', path);
      setDepartmentPath(path);
    } else {
      console.log('useAddEmployee: No department found, using empty path');
      setDepartmentPath('');
    }
  }, [userDepartment, selectedDepartment, isRegionManager, isSuperAdmin, isSuperUser, isRegionUser]);

  const addEmployee = async (employeeId, preview = false) => {
    setLoading(true);
    setError(null);

    try {
      // Check if department path is set
      if (!departmentPath) {
        throw new Error('Department path not set');
      }

      const db = getDatabase();

      // Fetch department-specific employees
      const employeesRef = ref(db, `${departmentPath}employees`);
      console.log('useAddEmployee: Fetching employees from path:', `${departmentPath}employees`);
      const employeesSnapshot = await get(employeesRef);
      if (!employeesSnapshot.exists()) {
        throw new Error('No employees found in this department');
      }
      const employees = employeesSnapshot.val();
      const newEmployee = employees[employeeId];
      if (!newEmployee) {
        throw new Error(`Employee ${employeeId} not found in this department`);
      }

      // Fetch available roster months from department
      const rostersRef = ref(db, `${departmentPath}rostersDraft`);
      console.log('useAddEmployee: Fetching rosters from path:', `${departmentPath}rostersDraft`);
      const rostersSnapshot = await get(rostersRef);
      const availableRosters = rostersSnapshot.exists() ? rostersSnapshot.val() : {};
      const affectedMonths = [];

      // Add current month
      affectedMonths.push({ year: parseInt(year), month: parseInt(month) });

      // Process existing future months
      Object.entries(availableRosters).forEach(([rosterYear, months]) => {
        const yearNum = parseInt(rosterYear);
        // Skip years before current year or same year with no future months
        if (yearNum < year || (yearNum === year && parseInt(month) >= 12)) return;

        Object.keys(months).forEach((rosterMonth) => {
          const monthNum = parseInt(rosterMonth);
          // Validate month is a number between 1 and 12
          if (isNaN(monthNum) || monthNum < 1 || monthNum > 12) {
            console.warn(`Invalid month ${rosterMonth} in rosters/${rosterYear}`);
            return;
          }
          // Skip current or past months in the same year
          if (yearNum === year && monthNum <= month) return;
          // Verify roster has employeeShifts
          if (!months[rosterMonth]?.employeeShifts) {
            console.warn(`No employeeShifts in rosters/${rosterYear}/${rosterMonth}`);
            return;
          }
          affectedMonths.push({ year: yearNum, month: monthNum });
        });
      });

      // Sort affected months chronologically
      affectedMonths.sort((a, b) => {
        if (a.year !== b.year) return a.year - b.year;
        return a.month - b.month;
      });

      if (preview) {
        setLoading(false);
        return { success: true, affectedMonths };
      }

      // Process current month and get the adjusted rowIndex
      const adjustedRowIndex = await addToMonth(year, month, employeeId, newEmployee);

      // Update employees collection with transaction
      console.log('useAddEmployee: Updating employee rowIndex in path:', `${departmentPath}employees`);
      await runTransaction(employeesRef, (currentEmployees) => {
        if (!currentEmployees) return currentEmployees;

        // Update new employee's rowIndex
        if (currentEmployees[employeeId]) {
          currentEmployees[employeeId].rowIndex = adjustedRowIndex;
        }

        // Shift other employees' rowIndex
        Object.entries(currentEmployees).forEach(([empId, data]) => {
          if (empId !== employeeId && data.rowIndex >= adjustedRowIndex) {
            currentEmployees[empId].rowIndex = data.rowIndex + 1;
          }
        });

        return currentEmployees;
      });

      // Add employee to future months
      for (const { year: rosterYear, month: rosterMonth } of affectedMonths) {
        if (rosterYear === parseInt(year) && rosterMonth === parseInt(month)) continue; // Skip current month
        await addToMonth(rosterYear, rosterMonth, employeeId, newEmployee, adjustedRowIndex);
      }

      return { success: true, affectedMonths };
    } catch (err) {
      setError(err.message);
      console.error('Error adding employee:', err);
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  };

  const addToMonth = async (year, month, employeeId, newEmployee, fixedRowIndex = null) => {
    const db = getDatabase();
    const rosterRef = ref(db, `${departmentPath}rostersDraft/${year}/${month}`);
    console.log('useAddEmployee: Adding employee to roster path:', `${departmentPath}rostersDraft/${year}/${month}`);
    const snapshot = await get(rosterRef);
    let employeeShifts = snapshot.exists() ? snapshot.val().employeeShifts || {} : {};

    if (employeeShifts[employeeId]) {
      console.log(`Employee ${employeeId} already in roster ${year}/${month}`);
      return employeeShifts[employeeId].rowIndex;
    }

    const rosterLength = Object.keys(employeeShifts).length;
    const newRowIndex = fixedRowIndex !== null ? fixedRowIndex : rosterLength + 1;

    const updates = {};
    Object.entries(employeeShifts).forEach(([empId, data]) => {
      if (data.rowIndex >= newRowIndex) {
        updates[`employeeShifts/${empId}/rowIndex`] = data.rowIndex + 1;
      }
    });

    const newEmployeeData = {
      name: newEmployee.name || 'Unknown',
      srcNumber: newEmployee.srcNumber || '',
      position: newEmployee.position || 'Technician',
      rowIndex: newRowIndex,
      shifts: {},
      leaveDays: 0,
      sickDays: 0,
      overtimeDays: 0,
      overtime: '0:00',
      aircraft: '0:00',
      office: '0:00',
    };

    updates[`employeeShifts/${employeeId}`] = newEmployeeData;

    await update(ref(db, `${departmentPath}rostersDraft/${year}/${month}`), updates);
    console.log(`Added employee ${employeeId} to ${departmentPath}rostersDraft/${year}/${month} with rowIndex ${newRowIndex}`);

    return newRowIndex;
  };

  return { addEmployee, loading, error };
};

export default useAddEmployee;