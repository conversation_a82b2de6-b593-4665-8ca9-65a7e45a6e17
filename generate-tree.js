const fs = require('fs');
const path = require('path');

// Get all JS and JSX files in the src directory
const getAllJsFiles = (dir) => {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      // Recursively search directories
      results = results.concat(getAllJsFiles(filePath));
    } else {
      // Check if file is JS or JSX
      if (file.endsWith('.js') || file.endsWith('.jsx')) {
        results.push({
          path: filePath,
          size: stat.size,
          lastModified: stat.mtime
        });
      }
    }
  });
  
  return results;
};

// Extract file references from a file (imports, requires, and component references)
const extractFileReferences = (filePath) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const references = new Set();
    
    // Static imports
    const importRegex = /import\s+(?:(?:{[^}]*}|\*\s+as\s+[^,]+|\w+)\s+from\s+)?['"]([^'"]+)['"]/g;
    let match;
    
    while ((match = importRegex.exec(content)) !== null) {
      let importPath = match[1];
      
      // Skip node_modules imports
      if (!importPath.startsWith('.') && !importPath.startsWith('/')) {
        continue;
      }
      
      // Resolve relative path
      if (importPath.startsWith('.')) {
        const dir = path.dirname(filePath);
        importPath = path.resolve(dir, importPath);
        
        // Handle imports without extension
        if (!importPath.endsWith('.js') && !importPath.endsWith('.jsx')) {
          // Try to resolve with extensions
          if (fs.existsSync(`${importPath}.js`)) {
            importPath = `${importPath}.js`;
          } else if (fs.existsSync(`${importPath}.jsx`)) {
            importPath = `${importPath}.jsx`;
          } else if (fs.existsSync(path.join(importPath, 'index.js'))) {
            importPath = path.join(importPath, 'index.js');
          } else if (fs.existsSync(path.join(importPath, 'index.jsx'))) {
            importPath = path.join(importPath, 'index.jsx');
          }
        }
      }
      
      references.add(importPath);
    }
    
    // Dynamic imports
    const dynamicImportRegex = /import\s*\(\s*['"]([^'"]+)['"]\s*\)/g;
    while ((match = dynamicImportRegex.exec(content)) !== null) {
      let importPath = match[1];
      if (importPath.startsWith('.')) {
        const dir = path.dirname(filePath);
        importPath = path.resolve(dir, importPath);
        references.add(importPath);
      }
    }
    
    // Require statements
    const requireRegex = /require\s*\(\s*['"]([^'"]+)['"]\s*\)/g;
    while ((match = requireRegex.exec(content)) !== null) {
      let importPath = match[1];
      if (importPath.startsWith('.')) {
        const dir = path.dirname(filePath);
        importPath = path.resolve(dir, importPath);
        references.add(importPath);
      }
    }
    
    // Extract component names from JSX
    const componentRegex = /<([A-Z][a-zA-Z0-9]*)/g;
    const componentNames = new Set();
    while ((match = componentRegex.exec(content)) !== null) {
      componentNames.add(match[1]);
    }
    
    return {
      paths: Array.from(references),
      componentNames: Array.from(componentNames)
    };
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return { paths: [], componentNames: [] };
  }
};

// Format file size in a human-readable format
const formatFileSize = (bytes) => {
  if (bytes < 1024) {
    return bytes + ' B';
  } else if (bytes < 1024 * 1024) {
    return (bytes / 1024).toFixed(2) + ' KB';
  } else {
    return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
  }
};

// Generate a tree structure of unused files
const generateTree = () => {
  const srcDir = path.resolve('./src');
  const allFiles = getAllJsFiles(srcDir);
  
  // Normalize paths for comparison
  const normalizedFiles = allFiles.map(file => ({
    ...file,
    normalizedPath: path.normalize(file.path)
  }));
  
  // Track which files are imported or referenced
  const referencedFiles = new Set();
  const componentNameToFile = new Map();
  
  // First pass: map component names to files
  normalizedFiles.forEach(file => {
    try {
      const fileName = path.basename(file.path, path.extname(file.path));
      // If the file name starts with uppercase, it's likely a component
      if (/^[A-Z]/.test(fileName)) {
        componentNameToFile.set(fileName, file.normalizedPath);
      }
    } catch (error) {
      console.error(`Error processing file name ${file.path}:`, error.message);
    }
  });
  
  // Second pass: extract all references
  normalizedFiles.forEach(file => {
    const { paths, componentNames } = extractFileReferences(file.path);
    
    // Add direct path references
    paths.forEach(importPath => {
      try {
        // Normalize the import path
        const normalizedImport = path.normalize(importPath);
        referencedFiles.add(normalizedImport);
      } catch (error) {
        console.error(`Error normalizing path ${importPath}:`, error.message);
      }
    });
    
    // Add component references
    componentNames.forEach(componentName => {
      if (componentNameToFile.has(componentName)) {
        referencedFiles.add(componentNameToFile.get(componentName));
      }
    });
  });
  
  // Find files that are not referenced anywhere
  const unusedFiles = normalizedFiles.filter(file => {
    // Skip entry point files and test files
    if (
      file.path.endsWith('index.js') || 
      file.path.endsWith('App.js') || 
      file.path.includes('setupTests.js') || 
      file.path.includes('.test.js') ||
      file.path.includes('reportWebVitals.js')
    ) {
      return false;
    }
    
    return !referencedFiles.has(file.normalizedPath);
  });
  
  // Build a tree structure
  const tree = {};
  
  unusedFiles.forEach(file => {
    const relPath = path.relative(srcDir, file.path);
    const parts = relPath.split(path.sep);
    
    let current = tree;
    for (let i = 0; i < parts.length - 1; i++) {
      const part = parts[i];
      if (!current[part]) {
        current[part] = {};
      }
      current = current[part];
    }
    
    const fileName = parts[parts.length - 1];
    current[fileName] = {
      size: formatFileSize(file.size),
      lastModified: file.lastModified.toISOString().split('T')[0]
    };
  });
  
  // Print the tree
  const printTree = (node, prefix = '', isLast = true, depth = 0) => {
    const connector = isLast ? '└── ' : '├── ';
    const indent = prefix + (isLast ? '    ' : '│   ');
    
    Object.entries(node).forEach(([key, value], index, array) => {
      const isLastItem = index === array.length - 1;
      
      if (typeof value === 'object' && !value.size) {
        // Directory
        console.log(`${prefix}${connector}${key}/`);
        printTree(value, indent, isLastItem, depth + 1);
      } else {
        // File
        console.log(`${prefix}${connector}${key} (${value.size}, ${value.lastModified})`);
      }
    });
  };
  
  console.log('Unused Files Tree:');
  console.log('src/');
  printTree(tree, '', true);
  
  console.log(`\nTotal unused files: ${unusedFiles.length}`);
  
  // Calculate total size of unused files
  const totalSize = unusedFiles.reduce((total, file) => total + file.size, 0);
  console.log(`Total size: ${formatFileSize(totalSize)}`);
};

generateTree();
