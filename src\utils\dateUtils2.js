export const getDaysInMonth = (year, month) => {
  return new Date(year, month, 0).getDate();
};

export const getDaysOfWeek = (year, month, daysInMonth) => {
  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  const firstDayOfMonth = new Date(year, month - 1, 1).getDay();
  return Array.from({ length: daysInMonth }, (_, i) => ({
    dayOfWeek: dayNames[(firstDayOfMonth + i) % 7],
    date: (i + 1).toString(),
  }));
};

export const formatHoursMinutes = (days) => {
  if (!days || isNaN(days)) return '0:00';
  const totalHours = days * 24;
  const hours = Math.floor(totalHours);
  const minutes = Math.round((totalHours - hours) * 60);
  return `${hours}:${minutes.toString().padStart(2, '0')}`;
};