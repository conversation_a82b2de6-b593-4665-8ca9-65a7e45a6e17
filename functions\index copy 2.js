const functions = require('firebase-functions');
const admin = require('firebase-admin');

admin.initializeApp();

exports.deleteUser = functions.https.onCall(async (data, context) => {
  console.log('deleteUser called with data:', data, 'context:', {
    uid: context.auth?.uid,
  });

  // Note: Admin role check is disabled for development. Enable before production.
  // if (!context.auth || !context.auth.uid) {
  //   throw new functions.https.HttpsError('unauthenticated', 'You must be signed in.');
  // }
  // const userRef = admin.database().ref(`users/${context.auth.uid}`);
  // const snapshot = await userRef.once('value');
  // if (!snapshot.exists() || snapshot.val().role !== 'admin') {
  //   throw new functions.https.HttpsError('permission-denied', 'Only admins can delete users.');
  // }

  const { uid } = data;
  if (!uid) {
    console.error('Invalid input: User ID is missing', { data });
    throw new functions.https.HttpsError('invalid-argument', 'User ID is required.');
  }
  // Allow self-deletion for development
  // if (uid === context.auth.uid) {
  //   throw new functions.https.HttpsError('failed-precondition', 'Cannot delete your own account.');
  // }

  try {
    console.log('Attempting to delete user:', { uid });
    await admin.auth().deleteUser(uid);
    console.log('Deleted user from Firebase Auth:', { uid });
    await admin.database().ref(`users/${uid}`).remove();
    console.log('Removed user data from Realtime Database:', { uid });
    return { success: true };
  } catch (error) {
    console.error('Error deleting user:', {
      error: error.message,
      code: error.code,
      uid,
    });
    throw new functions.https.HttpsError('internal', `Failed to delete user: ${error.message}`);
  }
});