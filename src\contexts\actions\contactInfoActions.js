// src\contexts\actions\contactInfoActions.js
import { getDatabase, ref, set, remove, onValue } from 'firebase/database';
import dataCache from '../../utils/cacheUtils';
import  getDepartmentPath from '../../utils/getDepartmentPath';
import {
  FETCH_CONTACT_INFO_START,
  FETCH_CONTACT_INFO_SUCCESS,
  FETCH_CONTACT_INFO_ERROR,
  UPDATE_CONTACT_INFO,
  DELETE_CONTACT_INFO
} from '../DataContext';

// ===== CONTACT INFO ACTIONS =====

// Fetch contact info with caching
export const fetchContactInfo = async (dispatch, forceRefresh = false, selectedDepartment = null) => {
  dispatch({ type: FETCH_CONTACT_INFO_START });

  const cacheKey = `contact_info_${selectedDepartment || 'default'}`;

  // For real-time data, we still use onValue but we can cache the initial data
  // to show something immediately while waiting for the real-time updates
  if (!forceRefresh) {
    const cachedData = dataCache.get(cacheKey);
    if (cachedData) {
      console.log('Using cached contact info data');
      dispatch({ type: FETCH_CONTACT_INFO_SUCCESS, payload: cachedData });
      // We still continue to set up the real-time listener below
    }
  }

  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);

    const db = getDatabase();
    const contactInfoRef = ref(db, `${departmentPath}contactInfo`);

    const unsubscribe = onValue(
      contactInfoRef,
      (snapshot) => {
        if (snapshot.exists()) {
          const data = snapshot.val();

          // Cache the data for future use (15 minutes TTL)
          dataCache.set(cacheKey, data, 15 * 60 * 1000);

          dispatch({ type: FETCH_CONTACT_INFO_SUCCESS, payload: data });
        } else {
          dispatch({ type: FETCH_CONTACT_INFO_SUCCESS, payload: {} });
        }
      },
      (err) => {
        dispatch({
          type: FETCH_CONTACT_INFO_ERROR,
          payload: new Error('Failed to fetch contact info: ' + err.message)
        });
      }
    );

    // Return unsubscribe function for cleanup
    return unsubscribe;
  } catch (err) {
    console.error('Error getting department path:', err);
    dispatch({
      type: FETCH_CONTACT_INFO_ERROR,
      payload: new Error('Failed to get department path: ' + err.message)
    });

    // Return a no-op function as a fallback
    return () => {};
  }
};

// Update contact info with optimistic update
export const updateContactInfo = async (dispatch, id, data, selectedDepartment = null) => {
  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);
    const cacheKey = `contact_info_${selectedDepartment || 'default'}`;

    // Optimistically update the UI immediately
    dispatch({ type: UPDATE_CONTACT_INFO, payload: { id, data } });

    // Invalidate the cache
    dataCache.delete(cacheKey);

    // Perform the actual database update
    const db = getDatabase();
    await set(ref(db, `${departmentPath}contactInfo/${id}`), data);

    return true;
  } catch (err) {
    console.error('Error updating contact info:', err);

    // Since we're using real-time updates with onValue,
    // the UI will automatically revert to the correct state
    return false;
  }
};

// Delete contact info with optimistic update
export const deleteContactInfo = async (dispatch, id, selectedDepartment = null) => {
  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);
    const cacheKey = `contact_info_${selectedDepartment || 'default'}`;

    // Optimistically update the UI immediately
    dispatch({ type: DELETE_CONTACT_INFO, payload: id });

    // Invalidate the cache
    dataCache.delete(cacheKey);

    // Perform the actual database update
    const db = getDatabase();
    await remove(ref(db, `${departmentPath}contactInfo/${id}`));

    return true;
  } catch (err) {
    console.error('Error deleting contact info:', err);

    // Since we're using real-time updates with onValue,
    // the UI will automatically revert to the correct state
    return false;
  }
};