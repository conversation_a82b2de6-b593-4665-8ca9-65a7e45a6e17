// src/hooks/RosterTable/useDragAndDrop.js
import { useCallback } from 'react';
import { updateEmployeeWorkMetrics } from '../../utils/rosterUtils';

/**
 * Custom hook to manage drag and drop functionality in the roster table
 *
 * @param {Array} rosterData - The roster data
 * @param {Object} selectionState - Selection state from useSelection hook
 * @param {Function} addToHistory - Function to add to history
 * @param {Function} trackCellChange - Function to track cell changes
 * @param {number} daysInMonth - Number of days in the month
 * @param {Object} shiftHours - Shift hours configuration
 * @param {string} mode - Current mode ('update', 'drag', 'dialog', 'view')
 * @param {boolean} isRosterDraft - Whether the roster is in draft mode
 * @returns {Object} Drag and drop handlers
 */
const useDragAndDrop = (
  rosterData,
  selectionState,
  addToHistory,
  trackCellChange,
  daysInMonth,
  shiftHours,
  mode,
  isRosterDraft
) => {
  const {
    selection,
    multiSelection,
    setDragging,
    setDropTarget
  } = selectionState;

  // Enhanced handleDragStart for drag preview
  const handleDragStart = useCallback((e, techId, dayNum, isSelected, isMultiSelected) => {
    // Prevent drag if not in draft mode or not in drag/crossMonthDrag mode
    if (!isRosterDraft || (mode !== 'drag' && mode !== 'crossMonthDrag')) {
      e.preventDefault();
      return false;
    }

    // Prevent drag if no selection
    if (!isSelected && !isMultiSelected) {
      e.preventDefault();
      return false;
    }

    // Set data for drag operation
    let dragData;

    // If we have a rectangular selection
    if (selection.start.techId && selection.end.techId) {
      dragData = { selection };
    }
    // If we have multi-selection
    else if (multiSelection.length > 0) {
      dragData = { multiSelection };
    }

    if (!dragData) {
      e.preventDefault();
      return false;
    }

    e.dataTransfer.effectAllowed = 'copy';

    // Add mode information to drag data for cross-month functionality
    const enhancedDragData = {
      ...dragData,
      mode: mode,
      type: selection.start.techId && selection.end.techId ? 'rectangular' : 'multi'
    };

    e.dataTransfer.setData('text/plain', JSON.stringify(enhancedDragData));

    // Drag preview: show a semi-transparent preview with mode indication
    const dragPreview = document.createElement('div');
    dragPreview.style.position = 'absolute';
    dragPreview.style.top = '-1000px';
    dragPreview.style.left = '-1000px';
    dragPreview.style.padding = '8px 16px';
    dragPreview.style.background = mode === 'crossMonthDrag' ? '#e0f2fe' : (isMultiSelected ? '#bde0fe' : '#ffcccb');
    dragPreview.style.opacity = '0.7';
    dragPreview.style.border = mode === 'crossMonthDrag' ? '2px solid #0284c7' : '2px solid #2563eb';
    dragPreview.style.borderRadius = '6px';
    dragPreview.style.fontWeight = 'bold';
    dragPreview.innerText = mode === 'crossMonthDrag'
      ? '📅 Cross-Month Drag'
      : (isMultiSelected ? 'Dragging Multiple Cells' : 'Dragging Selection');
    document.body.appendChild(dragPreview);
    e.dataTransfer.setDragImage(dragPreview, 0, 0);
    setTimeout(() => document.body.removeChild(dragPreview), 0);
  }, [isRosterDraft, mode, selection, multiSelection]);

  // Enhanced handleDragOver: track drop target for visual feedback
  const handleDragOver = useCallback((e, techId, dayNum) => {
    if (!isRosterDraft || (mode !== 'drag' && mode !== 'crossMonthDrag')) return;
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
    setDropTarget({ techId, day: dayNum });
  }, [isRosterDraft, mode, setDropTarget]);

  // Handle drop for rectangular drag
  const handleDrop = useCallback((e, targetTechId, targetDay) => {
    if (!isRosterDraft || (mode !== 'drag' && mode !== 'crossMonthDrag')) return;
    e.preventDefault();
    setDragging(false);
    setDropTarget(null); // Clear drop target after drop

    try {
      const dataStr = e.dataTransfer.getData('text/plain');
      if (!dataStr) return;

      const data = JSON.parse(dataStr);
      const techIds = rosterData.map(t => t.employeeId);
      const targetRow = techIds.indexOf(targetTechId);
      const targetCol = parseInt(targetDay);

      if (targetRow === -1 || isNaN(targetCol)) return;

      let selectedShifts = [];

      // Handle rectangular selection
      if (data.selection) {
        const dragSelection = data.selection;
        const startRow = techIds.indexOf(dragSelection.start.techId);
        const endRow = techIds.indexOf(dragSelection.end.techId);
        if (startRow === -1 || endRow === -1) return;

        const minRow = Math.min(startRow, endRow);
        const maxRow = Math.max(startRow, endRow);
        const minCol = Math.min(dragSelection.start.day, dragSelection.end.day);
        const maxCol = Math.max(dragSelection.start.day, dragSelection.end.day);

        // Gather selected block values
        for (let row = minRow; row <= maxRow; row++) {
          const rowTech = rosterData[row];
          const rowShifts = [];
          for (let col = minCol; col <= maxCol; col++) {
            rowShifts.push({
              value: rowTech[col.toString()] ?? null,
              rowOffset: row - minRow,
              colOffset: col - minCol
            });
          }
          selectedShifts = [...selectedShifts, ...rowShifts];
        }
      }
      // Handle multi-selection
      else if (data.multiSelection) {
        const multiCells = data.multiSelection;
        if (!Array.isArray(multiCells) || multiCells.length === 0) return;

        // Find the min tech index and min day to use as reference point
        const cellTechIndices = multiCells.map(cell => techIds.indexOf(cell.techId));
        const minTechIdx = Math.min(...cellTechIndices);
        const minDay = Math.min(...multiCells.map(cell => cell.day));

        // Gather selected values with relative offsets
        selectedShifts = multiCells.map(cell => {
          const techIdx = techIds.indexOf(cell.techId);
          return {
            value: rosterData[techIdx][cell.day.toString()] ?? null,
            rowOffset: techIdx - minTechIdx,
            colOffset: cell.day - minDay
          };
        });
      }

      if (selectedShifts.length === 0) return;

      // Track if any actual changes occurred
      let hasChanges = false;

      // Apply shifts to target location
      const newRosterData = rosterData.map((tech, idx) => {
        // Skip rows that won't receive data
        const rowShifts = selectedShifts.filter(shift =>
          idx === targetRow + shift.rowOffset &&
          targetRow + shift.rowOffset < rosterData.length
        );

        if (rowShifts.length === 0) return { ...tech };

        const updatedTech = { ...tech };
        const originalShifts = { ...tech };

        // Apply each shift to this row
        rowShifts.forEach(shift => {
          const dayCol = targetCol + shift.colOffset;
          if (dayCol < 1 || dayCol > daysInMonth) return;

          const dayStr = dayCol.toString();
          const originalValue = originalShifts[dayStr];

          if (shift.value !== null) {
            updatedTech[dayStr] = shift.value;
          } else {
            delete updatedTech[dayStr];
          }

          // Check if the value actually changed
          const newValue = updatedTech[dayStr];
          if (newValue !== originalValue) {
            hasChanges = true;
            trackCellChange(tech.employeeId, dayStr);
          }
        });

        // Update work metrics and return the result
        return updateEmployeeWorkMetrics(updatedTech, shiftHours, daysInMonth);
      });

      // Only add to history if there were actual changes
      if (hasChanges) {
        addToHistory(newRosterData);
        return newRosterData;
      } else {
        // No changes occurred, return null to indicate no update needed
        return null;
      }
    } catch (error) {
      console.error('Error during drop operation:', error);
      return null;
    }
  }, [
    isRosterDraft,
    mode,
    rosterData,
    selection,
    multiSelection,
    setDragging,
    setDropTarget,
    addToHistory,
    trackCellChange,
    daysInMonth,
    shiftHours
  ]);

  return {
    handleDragStart,
    handleDragOver,
    handleDrop
  };
};

export default useDragAndDrop;
