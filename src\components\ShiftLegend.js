import React from 'react';
import PropTypes from 'prop-types';

function ShiftLegend({ shiftHours, shiftColors }) {
  return (
    <div
      style={{
        marginTop: '0.5rem',
        marginBottom: '1rem',
        padding: '0.75rem',
        backgroundColor: '#f3f4f6',
        borderRadius: '0.5rem',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        maxWidth: '500px', // Match MarkerLegend
        width: '100%',
        boxSizing: 'border-box',
      }}
    >
      <h3 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '0.5rem' }}>Shift Legend</h3>
      {Object.keys(shiftHours || {}).length === 0 ? (
        <p style={{ fontSize: '0.75rem', color: '#6b7280' }}>No shifts defined.</p>
      ) : (
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(2, minmax(0, 1fr))', // Two-column grid
            gap: '0.25rem', // Tight spacing
            fontSize: '0.75rem', // Match MarkerLegend
            lineHeight: '1.25',
          }}
        >
          {Object.entries(shiftHours || {}).map(([shift, hours]) => (
            <div key={shift} style={{ display: 'flex', alignItems: 'center' }}>
              <div
                style={{
                  width: '0.75rem',
                  height: '0.75rem',
                  marginRight: '0.25rem',
                  backgroundColor: shiftColors[shift] || '#d3d3d3',
                  flexShrink: 0,
                }}
              ></div>
              <span style={{ whiteSpace: 'nowrap' }}>
                {shift}: {hours} hours
              </span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

ShiftLegend.propTypes = {
  shiftHours: PropTypes.object.isRequired,
  shiftColors: PropTypes.object.isRequired,
};

export default ShiftLegend;