// src/contexts/DataContext.jsx
import React, { createContext, useReducer, useContext, useEffect, useCallback } from 'react';
import { getDatabase, ref, onValue, get, set, remove, update, off } from 'firebase/database';

// Create contexts for different data types
const RosterDataContext = createContext();
const EmployeeDataContext = createContext();
const ShiftDataContext = createContext();
const ContactInfoContext = createContext();
const CalendarMarkersContext = createContext();
const DataDispatchContext = createContext();

// Initial state
const initialState = {
  rosters: {
    data: [],
    availableYears: [],
    availableMonths: {},
    loading: false,
    error: null,
    isDraftRoster: true,
    year: new Date().getFullYear(),
    month: new Date().getMonth() + 1
  },
  employees: {
    data: [],
    loading: false,
    error: null
  },
  shifts: {
    data: {},
    types: [],
    hours: {},
    colors: {},
    startTimes: {},
    endTimes: {},
    loading: false,
    error: null
  },
  contactInfo: {
    data: {},
    loading: false,
    error: null
  },
  calendarMarkers: {
    weekdayMarkers: {},
    holidayMarkers: {},
    specialDayMarkers: {},
    loading: false,
    error: null
  }
};

// Action types
const FETCH_ROSTER_START = 'FETCH_ROSTER_START';
const FETCH_ROSTER_SUCCESS = 'FETCH_ROSTER_SUCCESS';
const FETCH_ROSTER_ERROR = 'FETCH_ROSTER_ERROR';
const SET_ROSTER_YEAR = 'SET_ROSTER_YEAR';
const SET_ROSTER_MONTH = 'SET_ROSTER_MONTH';
const TOGGLE_ROSTER_DRAFT = 'TOGGLE_ROSTER_DRAFT';
const SET_AVAILABLE_YEARS = 'SET_AVAILABLE_YEARS';
const SET_AVAILABLE_MONTHS = 'SET_AVAILABLE_MONTHS';

const FETCH_EMPLOYEES_START = 'FETCH_EMPLOYEES_START';
const FETCH_EMPLOYEES_SUCCESS = 'FETCH_EMPLOYEES_SUCCESS';
const FETCH_EMPLOYEES_ERROR = 'FETCH_EMPLOYEES_ERROR';
const ADD_EMPLOYEE = 'ADD_EMPLOYEE';
const UPDATE_EMPLOYEE = 'UPDATE_EMPLOYEE';
const DELETE_EMPLOYEE = 'DELETE_EMPLOYEE';

const FETCH_SHIFTS_START = 'FETCH_SHIFTS_START';
const FETCH_SHIFTS_SUCCESS = 'FETCH_SHIFTS_SUCCESS';
const FETCH_SHIFTS_ERROR = 'FETCH_SHIFTS_ERROR';
const ADD_SHIFT = 'ADD_SHIFT';
const UPDATE_SHIFT = 'UPDATE_SHIFT';
const DELETE_SHIFT = 'DELETE_SHIFT';

const FETCH_CONTACT_INFO_START = 'FETCH_CONTACT_INFO_START';
const FETCH_CONTACT_INFO_SUCCESS = 'FETCH_CONTACT_INFO_SUCCESS';
const FETCH_CONTACT_INFO_ERROR = 'FETCH_CONTACT_INFO_ERROR';
const UPDATE_CONTACT_INFO = 'UPDATE_CONTACT_INFO';
const ADD_CONTACT_INFO = 'ADD_CONTACT_INFO';
const DELETE_CONTACT_INFO = 'DELETE_CONTACT_INFO';

const FETCH_CALENDAR_MARKERS_START = 'FETCH_CALENDAR_MARKERS_START';
const FETCH_CALENDAR_MARKERS_SUCCESS = 'FETCH_CALENDAR_MARKERS_SUCCESS';
const FETCH_CALENDAR_MARKERS_ERROR = 'FETCH_CALENDAR_MARKERS_ERROR';

// Reducer function
function dataReducer(state, action) {
  switch (action.type) {
    // Roster actions
    case FETCH_ROSTER_START:
      return {
        ...state,
        rosters: {
          ...state.rosters,
          loading: true,
          error: null
        }
      };
    case FETCH_ROSTER_SUCCESS:
      return {
        ...state,
        rosters: {
          ...state.rosters,
          data: action.payload,
          loading: false,
          error: null
        }
      };
    case FETCH_ROSTER_ERROR:
      return {
        ...state,
        rosters: {
          ...state.rosters,
          error: action.payload,
          loading: false
        }
      };
    case SET_ROSTER_YEAR:
      return {
        ...state,
        rosters: {
          ...state.rosters,
          year: action.payload
        }
      };
    case SET_ROSTER_MONTH:
      return {
        ...state,
        rosters: {
          ...state.rosters,
          month: action.payload
        }
      };
    case TOGGLE_ROSTER_DRAFT:
      return {
        ...state,
        rosters: {
          ...state.rosters,
          isDraftRoster: !state.rosters.isDraftRoster
        }
      };
    case SET_AVAILABLE_YEARS:
      return {
        ...state,
        rosters: {
          ...state.rosters,
          availableYears: action.payload
        }
      };
    case SET_AVAILABLE_MONTHS:
      return {
        ...state,
        rosters: {
          ...state.rosters,
          availableMonths: {
            ...state.rosters.availableMonths,
            [action.payload.year]: action.payload.months
          }
        }
      };

    // Employee actions
    case FETCH_EMPLOYEES_START:
      return {
        ...state,
        employees: {
          ...state.employees,
          loading: true,
          error: null
        }
      };
    case FETCH_EMPLOYEES_SUCCESS:
      return {
        ...state,
        employees: {
          ...state.employees,
          data: action.payload,
          loading: false,
          error: null
        }
      };
    case FETCH_EMPLOYEES_ERROR:
      return {
        ...state,
        employees: {
          ...state.employees,
          error: action.payload,
          loading: false
        }
      };
    case ADD_EMPLOYEE:
      return {
        ...state,
        employees: {
          ...state.employees,
          data: [...state.employees.data, action.payload]
        }
      };
    case UPDATE_EMPLOYEE:
      return {
        ...state,
        employees: {
          ...state.employees,
          data: state.employees.data.map(emp => 
            emp.employeeId === action.payload.employeeId ? action.payload : emp
          )
        }
      };
    case DELETE_EMPLOYEE:
      return {
        ...state,
        employees: {
          ...state.employees,
          data: state.employees.data.filter(emp => emp.employeeId !== action.payload)
        }
      };

    // Shift actions
    case FETCH_SHIFTS_START:
      return {
        ...state,
        shifts: {
          ...state.shifts,
          loading: true,
          error: null
        }
      };
    case FETCH_SHIFTS_SUCCESS:
      return {
        ...state,
        shifts: {
          ...state.shifts,
          data: action.payload.data,
          types: action.payload.types,
          hours: action.payload.hours,
          colors: action.payload.colors,
          startTimes: action.payload.startTimes,
          endTimes: action.payload.endTimes,
          loading: false,
          error: null
        }
      };
    case FETCH_SHIFTS_ERROR:
      return {
        ...state,
        shifts: {
          ...state.shifts,
          error: action.payload,
          loading: false
        }
      };
    case ADD_SHIFT:
    case UPDATE_SHIFT:
      return {
        ...state,
        shifts: {
          ...state.shifts,
          data: {
            ...state.shifts.data,
            [action.payload.name]: action.payload.data
          }
        }
      };
    case DELETE_SHIFT:
      const updatedShifts = { ...state.shifts.data };
      delete updatedShifts[action.payload];
      return {
        ...state,
        shifts: {
          ...state.shifts,
          data: updatedShifts
        }
      };

    // Contact info actions
    case FETCH_CONTACT_INFO_START:
      return {
        ...state,
        contactInfo: {
          ...state.contactInfo,
          loading: true,
          error: null
        }
      };
    case FETCH_CONTACT_INFO_SUCCESS:
      return {
        ...state,
        contactInfo: {
          ...state.contactInfo,
          data: action.payload,
          loading: false,
          error: null
        }
      };
    case FETCH_CONTACT_INFO_ERROR:
      return {
        ...state,
        contactInfo: {
          ...state.contactInfo,
          error: action.payload,
          loading: false
        }
      };
    case UPDATE_CONTACT_INFO:
    case ADD_CONTACT_INFO:
      return {
        ...state,
        contactInfo: {
          ...state.contactInfo,
          data: {
            ...state.contactInfo.data,
            [action.payload.id]: action.payload.data
          }
        }
      };
    case DELETE_CONTACT_INFO:
      const updatedContactInfo = { ...state.contactInfo.data };
      delete updatedContactInfo[action.payload];
      return {
        ...state,
        contactInfo: {
          ...state.contactInfo,
          data: updatedContactInfo
        }
      };

    // Calendar markers actions
    case FETCH_CALENDAR_MARKERS_START:
      return {
        ...state,
        calendarMarkers: {
          ...state.calendarMarkers,
          loading: true,
          error: null
        }
      };
    case FETCH_CALENDAR_MARKERS_SUCCESS:
      return {
        ...state,
        calendarMarkers: {
          ...state.calendarMarkers,
          weekdayMarkers: action.payload.weekdayMarkers,
          holidayMarkers: action.payload.holidayMarkers,
          specialDayMarkers: action.payload.specialDayMarkers,
          loading: false,
          error: null
        }
      };
    case FETCH_CALENDAR_MARKERS_ERROR:
      return {
        ...state,
        calendarMarkers: {
          ...state.calendarMarkers,
          error: action.payload,
          loading: false
        }
      };

    default:
      return state;
  }
}

// Provider component
export function DataProvider({ children }) {
  const [state, dispatch] = useReducer(dataReducer, initialState);

  // Provide the contexts
  return (
    <DataDispatchContext.Provider value={dispatch}>
      <RosterDataContext.Provider value={state.rosters}>
        <EmployeeDataContext.Provider value={state.employees}>
          <ShiftDataContext.Provider value={state.shifts}>
            <ContactInfoContext.Provider value={state.contactInfo}>
              <CalendarMarkersContext.Provider value={state.calendarMarkers}>
                {children}
              </CalendarMarkersContext.Provider>
            </ContactInfoContext.Provider>
          </ShiftDataContext.Provider>
        </EmployeeDataContext.Provider>
      </RosterDataContext.Provider>
    </DataDispatchContext.Provider>
  );
}

// Custom hooks to use the contexts
export function useRosterData() {
  return useContext(RosterDataContext);
}

export function useEmployeeData() {
  return useContext(EmployeeDataContext);
}

export function useShiftData() {
  return useContext(ShiftDataContext);
}

export function useContactInfo() {
  return useContext(ContactInfoContext);
}

export function useCalendarMarkers() {
  return useContext(CalendarMarkersContext);
}

export function useDataDispatch() {
  return useContext(DataDispatchContext);
}

// Export action types for use in action creators
export {
  FETCH_ROSTER_START,
  FETCH_ROSTER_SUCCESS,
  FETCH_ROSTER_ERROR,
  SET_ROSTER_YEAR,
  SET_ROSTER_MONTH,
  TOGGLE_ROSTER_DRAFT,
  SET_AVAILABLE_YEARS,
  SET_AVAILABLE_MONTHS,
  FETCH_EMPLOYEES_START,
  FETCH_EMPLOYEES_SUCCESS,
  FETCH_EMPLOYEES_ERROR,
  ADD_EMPLOYEE,
  UPDATE_EMPLOYEE,
  DELETE_EMPLOYEE,
  FETCH_SHIFTS_START,
  FETCH_SHIFTS_SUCCESS,
  FETCH_SHIFTS_ERROR,
  ADD_SHIFT,
  UPDATE_SHIFT,
  DELETE_SHIFT,
  FETCH_CONTACT_INFO_START,
  FETCH_CONTACT_INFO_SUCCESS,
  FETCH_CONTACT_INFO_ERROR,
  UPDATE_CONTACT_INFO,
  ADD_CONTACT_INFO,
  DELETE_CONTACT_INFO,
  FETCH_CALENDAR_MARKERS_START,
  FETCH_CALENDAR_MARKERS_SUCCESS,
  FETCH_CALENDAR_MARKERS_ERROR
};
