.navbar {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10;
  width: 100%;
  background-color: #ffffff;
  padding: 1rem 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar-container {
  max-width: 1400px;
  width: 100%;
  margin: 0 auto;


  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar-logo {
  color: #2563eb;
  font-weight: bold;
  font-size: 1.5rem;
  text-decoration: none;
  display: flex;
  align-items: center;
  margin-right: 1.5rem;
}

.navbar-icon {
  width: 32px;
  height: 32px;
  margin-right: 10px;
}

.mobile-menu-icon {
  display: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #1f2937;
}

.navbar-menu {
  display: flex;
  list-style: none;
  padding: 0;
  margin: 0;
  align-items: center;
}

.navbar-menu li {
  margin: 0 1.5rem;
}

.navbar-link {
  text-decoration: none;
  color: #1f2937;
  font-weight: 600;
  font-size: 1.1rem;
  transition: color 0.2s ease;
}

.navbar-link:hover {
  color: #2563eb;
}

.navbar-link.active {
  color: #2563eb;
  border-bottom: 2px solid #2563eb;
  padding-bottom: 0.2rem;
}

.navbar-auth {
  display: flex;
  align-items: center;
}

.navbar-signin {
  text-decoration: none;
  color: #2563eb;
  font-weight: 600;
  font-size: 1rem;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  transition: background-color 0.2s ease;
}

.navbar-signin:hover {
  background-color: #eff6ff;
}

.navbar-signout {
  background-color: #6b7280;
  color: white;
  font-weight: 600;
  font-size: 1rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-left: 1rem;
}

.navbar-signout:hover {
  background-color: #b91c1c;
}

/* Department Dropdown (for super-admins) */
.navbar-department {
  position: relative;
  margin-right: 1.5rem;
}

.navbar-department-button {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  background-color: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: background-color 0.2s ease, box-shadow 0.2s ease;
}

.navbar-department-button:hover {
  background-color: #f9fafb;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.navbar-department-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.3);
}

.navbar-department-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.navbar-department-icon {
  width: 1.25rem;
  height: 1.25rem;
  margin-right: 0.5rem;
  color: #6b7280;
}

.navbar-department-chevron {
  width: 1rem;
  height: 1rem;
  margin-left: 0.5rem;
  color: #6b7280;
  transition: transform 0.2s ease;
}

.navbar-department-chevron.rotate-180 {
  transform: rotate(180deg);
}

.navbar-department-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 200px;
  background-color: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 20;
  max-height: 240px;
  overflow-y: auto;
  margin-top: 0.25rem;
  padding: 0.25rem 0;
}

.navbar-department-item {
  padding: 0.5rem 1rem;
  font-size: 1rem;
  color: #1f2937;
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.navbar-department-item:hover {
  background-color: #eff6ff;
  color: #2563eb;
}

.navbar-department-item[aria-selected="true"] {
  background-color: #2563eb;
  color: #ffffff;
}

/* Add Department Button */
.navbar-add-department {
  border-top: 1px solid #e5e7eb;
  margin-top: 0.25rem;
  padding-top: 0.5rem;
  color: #2563eb;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-add-department:hover {
  background-color: #eff6ff;
  color: #1d4ed8;
}

/* Static Department Display (for non-super-admins) */
.navbar-department-static {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  background-color: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-right: 1.5rem;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .navbar-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .mobile-menu-icon {
    display: block;
  }

  .navbar-menu-wrapper {
    display: none;
  }

  .navbar-mobile-menu {
    position: absolute;
    top: 60px;
    left: 0;
    width: 70%;
    max-width: 300px;
    background-color: rgba(245, 245, 245, 1);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
    flex-direction: column;
    align-items: flex-start;
    padding: 1rem;
    border-radius: 0.5rem;
    backdrop-filter: blur(6px);
    z-index: 100;
  }

  .navbar-menu {
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
  }

  .navbar-menu li {
    margin: 0.5rem 0;
    width: 100%;
  }

  .navbar-link,
  .navbar-signin.mobile-signin,
  .navbar-signout.mobile-signout {
    display: block;
    padding: 0.75rem 1rem;
    text-align: center;
    width: 100%;
    border-radius: 0.25rem;
  }

  .navbar-signin.mobile-signin:hover {
    background-color: #eff6ff;
  }

  .navbar-signout.mobile-signout {
    background-color: #6b7280;
    width: fit-content;
    margin: 0 auto;
    color: white;
    border: none;
  }

  .navbar-signout.mobile-signout:hover {
    background-color: #b91c1c;
  }

  .navbar-auth {
    display: none;
  }

  .navbar-department,
  .navbar-department-static {
    margin-right: 1rem;
    flex-shrink: 0;
  }

  .navbar-department-button,
  .navbar-department-static {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }

  .navbar-department-dropdown {
    width: 180px;
  }
}

/* Role Indicator Styles */
.navbar-role-indicator {
  display: flex;
  justify-content: center;
  margin-top: 2px;
  font-size: 10px;
}

.navbar-role-label {
  background-color: #e5e7eb;
  color: #374151;
  padding: 1px 6px;
  border-radius: 8px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 9px;
  line-height: 1.2;
  white-space: nowrap;
}

/* Role-specific colors */
.navbar-role-label.super-admin {
  background-color: #fef3c7;
  color: #92400e;
}

.navbar-role-label.region-manager {
  background-color: #dbeafe;
  color: #1e40af;
}

.navbar-role-label.admin {
  background-color: #dcfce7;
  color: #166534;
}

.navbar-role-label.super-user {
  background-color: #fce7f3;
  color: #be185d;
}

.navbar-role-label.region-user {
  background-color: #e0e7ff;
  color: #3730a3;
}

.navbar-role-label.user {
  background-color: #f3f4f6;
  color: #4b5563;
}

/* Mobile adjustments for role indicator */
@media (max-width: 768px) {
  .navbar-role-indicator {
    margin-top: 1px;
  }

  .navbar-role-label {
    font-size: 8px;
    padding: 1px 4px;
  }
}