const fs = require('fs');
const path = require('path');

// Get all JS and JSX files in the src directory
const getAllJsFiles = (dir) => {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      // Recursively search directories
      results = results.concat(getAllJsFiles(filePath));
    } else {
      // Check if file is JS or JSX
      if (file.endsWith('.js') || file.endsWith('.jsx')) {
        results.push({
          path: filePath,
          size: stat.size,
          lastModified: stat.mtime
        });
      }
    }
  });
  
  return results;
};

// Extract file references from a file (imports, requires, and component references)
const extractFileReferences = (filePath) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const references = new Set();
    
    // Static imports
    const importRegex = /import\s+(?:(?:{[^}]*}|\*\s+as\s+[^,]+|\w+)\s+from\s+)?['"]([^'"]+)['"]/g;
    let match;
    
    while ((match = importRegex.exec(content)) !== null) {
      let importPath = match[1];
      
      // Skip node_modules imports
      if (!importPath.startsWith('.') && !importPath.startsWith('/')) {
        continue;
      }
      
      // Resolve relative path
      if (importPath.startsWith('.')) {
        const dir = path.dirname(filePath);
        importPath = path.resolve(dir, importPath);
        
        // Handle imports without extension
        if (!importPath.endsWith('.js') && !importPath.endsWith('.jsx')) {
          // Try to resolve with extensions
          if (fs.existsSync(`${importPath}.js`)) {
            importPath = `${importPath}.js`;
          } else if (fs.existsSync(`${importPath}.jsx`)) {
            importPath = `${importPath}.jsx`;
          } else if (fs.existsSync(path.join(importPath, 'index.js'))) {
            importPath = path.join(importPath, 'index.js');
          } else if (fs.existsSync(path.join(importPath, 'index.jsx'))) {
            importPath = path.join(importPath, 'index.jsx');
          }
        }
      }
      
      references.add(importPath);
    }
    
    // Dynamic imports
    const dynamicImportRegex = /import\s*\(\s*['"]([^'"]+)['"]\s*\)/g;
    while ((match = dynamicImportRegex.exec(content)) !== null) {
      let importPath = match[1];
      if (importPath.startsWith('.')) {
        const dir = path.dirname(filePath);
        importPath = path.resolve(dir, importPath);
        references.add(importPath);
      }
    }
    
    // Require statements
    const requireRegex = /require\s*\(\s*['"]([^'"]+)['"]\s*\)/g;
    while ((match = requireRegex.exec(content)) !== null) {
      let importPath = match[1];
      if (importPath.startsWith('.')) {
        const dir = path.dirname(filePath);
        importPath = path.resolve(dir, importPath);
        references.add(importPath);
      }
    }
    
    // Extract component names from JSX
    const componentRegex = /<([A-Z][a-zA-Z0-9]*)/g;
    const componentNames = new Set();
    while ((match = componentRegex.exec(content)) !== null) {
      componentNames.add(match[1]);
    }
    
    return {
      paths: Array.from(references),
      componentNames: Array.from(componentNames)
    };
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return { paths: [], componentNames: [] };
  }
};

// Format file size in a human-readable format
const formatFileSize = (bytes) => {
  if (bytes < 1024) {
    return bytes + ' B';
  } else if (bytes < 1024 * 1024) {
    return (bytes / 1024).toFixed(2) + ' KB';
  } else {
    return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
  }
};

// Extract a brief summary of the file content
const extractFileSummary = (filePath) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Look for component definition
    const componentRegex = /(?:class|function|const)\s+([A-Z][a-zA-Z0-9]*)/g;
    const components = [];
    let match;
    
    while ((match = componentRegex.exec(content)) !== null) {
      components.push(match[1]);
    }
    
    // Look for hook definition
    const hookRegex = /(?:function|const)\s+(use[A-Z][a-zA-Z0-9]*)/g;
    const hooks = [];
    
    while ((match = hookRegex.exec(content)) !== null) {
      hooks.push(match[1]);
    }
    
    // Look for exports
    const exportRegex = /export\s+(?:default\s+)?(?:const|function|class)?\s*([a-zA-Z0-9_]+)/g;
    const exports = [];
    
    while ((match = exportRegex.exec(content)) !== null) {
      exports.push(match[1]);
    }
    
    // Count lines of code
    const lines = content.split('\n').length;
    
    // Extract first comment block if it exists
    const commentRegex = /\/\*\*([\s\S]*?)\*\//;
    const commentMatch = content.match(commentRegex);
    let comment = '';
    
    if (commentMatch) {
      comment = commentMatch[1]
        .replace(/\n\s*\*/g, ' ')
        .trim()
        .substring(0, 150);
      
      if (comment.length >= 150) {
        comment += '...';
      }
    }
    
    return {
      components,
      hooks,
      exports,
      lines,
      comment
    };
  } catch (error) {
    console.error(`Error extracting summary for ${filePath}:`, error.message);
    return { components: [], hooks: [], exports: [], lines: 0, comment: '' };
  }
};

// Find similar files that might have replaced the unused file
const findSimilarFiles = (unusedFile, allFiles, srcDir) => {
  const unusedFileName = path.basename(unusedFile.path, path.extname(unusedFile.path));
  const unusedFileSummary = extractFileSummary(unusedFile.path);
  
  // Find files with similar names or exports
  return allFiles
    .filter(file => {
      if (file.path === unusedFile.path) return false;
      
      const fileName = path.basename(file.path, path.extname(file.path));
      const fileSummary = extractFileSummary(file.path);
      
      // Check for similar file name
      const nameIsSimilar = fileName.includes(unusedFileName) || 
                           unusedFileName.includes(fileName);
      
      // Check for similar exports
      const hasCommonExports = fileSummary.exports.some(exp => 
        unusedFileSummary.exports.includes(exp));
      
      // Check for similar components
      const hasCommonComponents = fileSummary.components.some(comp => 
        unusedFileSummary.components.includes(comp));
      
      // Check for similar hooks
      const hasCommonHooks = fileSummary.hooks.some(hook => 
        unusedFileSummary.hooks.includes(hook));
      
      return nameIsSimilar || hasCommonExports || hasCommonComponents || hasCommonHooks;
    })
    .map(file => ({
      path: path.relative(srcDir, file.path),
      reason: 'Similar name or functionality'
    }));
};

// Main function to analyze unused files
const analyzeUnusedFiles = () => {
  const srcDir = path.resolve('./src');
  const allFiles = getAllJsFiles(srcDir);
  
  // Normalize paths for comparison
  const normalizedFiles = allFiles.map(file => ({
    ...file,
    normalizedPath: path.normalize(file.path)
  }));
  
  // Track which files are imported or referenced
  const referencedFiles = new Set();
  const componentNameToFile = new Map();
  
  // First pass: map component names to files
  normalizedFiles.forEach(file => {
    try {
      const fileName = path.basename(file.path, path.extname(file.path));
      // If the file name starts with uppercase, it's likely a component
      if (/^[A-Z]/.test(fileName)) {
        componentNameToFile.set(fileName, file.normalizedPath);
      }
    } catch (error) {
      console.error(`Error processing file name ${file.path}:`, error.message);
    }
  });
  
  // Second pass: extract all references
  normalizedFiles.forEach(file => {
    const { paths, componentNames } = extractFileReferences(file.path);
    
    // Add direct path references
    paths.forEach(importPath => {
      try {
        // Normalize the import path
        const normalizedImport = path.normalize(importPath);
        referencedFiles.add(normalizedImport);
      } catch (error) {
        console.error(`Error normalizing path ${importPath}:`, error.message);
      }
    });
    
    // Add component references
    componentNames.forEach(componentName => {
      if (componentNameToFile.has(componentName)) {
        referencedFiles.add(componentNameToFile.get(componentName));
      }
    });
  });
  
  // Find files that are not referenced anywhere
  const unusedFiles = normalizedFiles.filter(file => {
    // Skip entry point files and test files
    if (
      file.path.endsWith('index.js') || 
      file.path.endsWith('App.js') || 
      file.path.includes('setupTests.js') || 
      file.path.includes('.test.js') ||
      file.path.includes('reportWebVitals.js')
    ) {
      return false;
    }
    
    return !referencedFiles.has(file.normalizedPath);
  });
  
  console.log('Detailed Analysis of Unused Files:');
  console.log('=================================');
  
  unusedFiles.forEach(file => {
    const relPath = path.relative(srcDir, file.path);
    const summary = extractFileSummary(file.path);
    const similarFiles = findSimilarFiles(file, normalizedFiles, srcDir);
    
    console.log(`\nFile: ${relPath}`);
    console.log(`Size: ${formatFileSize(file.size)}`);
    console.log(`Last Modified: ${file.lastModified.toISOString().split('T')[0]}`);
    console.log(`Lines of Code: ${summary.lines}`);
    
    if (summary.comment) {
      console.log(`Description: ${summary.comment}`);
    }
    
    if (summary.components.length > 0) {
      console.log(`Components: ${summary.components.join(', ')}`);
    }
    
    if (summary.hooks.length > 0) {
      console.log(`Hooks: ${summary.hooks.join(', ')}`);
    }
    
    if (summary.exports.length > 0) {
      console.log(`Exports: ${summary.exports.join(', ')}`);
    }
    
    if (similarFiles.length > 0) {
      console.log('Potentially Related Files:');
      similarFiles.forEach(similar => {
        console.log(`  - ${similar.path} (${similar.reason})`);
      });
    }
    
    console.log('Recommendation: Consider removing this file if it is confirmed to be unused.');
  });
  
  console.log(`\nTotal unused files: ${unusedFiles.length}`);
  
  // Calculate total size of unused files
  const totalSize = unusedFiles.reduce((total, file) => total + file.size, 0);
  console.log(`Total size that could be reclaimed: ${formatFileSize(totalSize)}`);
};

analyzeUnusedFiles();
