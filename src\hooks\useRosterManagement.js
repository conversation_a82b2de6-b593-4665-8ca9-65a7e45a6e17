// src/hooks/useRosterManagement.js
import { useState, useContext, useEffect } from 'react';
import { get, ref, getDatabase, update } from 'firebase/database';
import { AuthContext } from '../components/AuthProvider';

/**
 * Custom hook to manage roster creation and deletion
 * @param {number} year - Current year
 * @param {number} month - Current month
 * @param {Array} localRosterData - Current roster data
 * @param {Function} setLocalRosterData - Function to update roster data
 * @param {Function} addRoster - Function from useAddRoster hook
 * @param {Function} refetch - Function to refetch roster data
 * @param {Function} refetchRosters - Function to refetch available rosters
 * @param {Function} fetchAvailableEmployees - Function to refetch available employees
 * @returns {Object} State and functions for managing rosters
 */
const useRosterManagement = (
  year,
  month,
  localRosterData,
  setLocalRosterData,
  addRoster,
  refetch,
  refetchRosters,
  fetchAvailableEmployees
) => {
  const { userDepartment, selectedDepartment, isRegionManager, isSuperAdmin, isSuperUser, isRegionUser } = useContext(AuthContext);

  // Add roster state
  const [isAddRosterModalOpen, setIsAddRosterModalOpen] = useState(false);
  const [addRosterResult, setAddRosterResult] = useState(null);
  const [nextRoster, setNextRoster] = useState(null);

  // Delete row state
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [deleteResult, setDeleteResult] = useState(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [affectedDeleteMonths, setAffectedDeleteMonths] = useState([]);

  // Department path state
  const [departmentPath, setDepartmentPath] = useState('');

  // Set department path when department changes
  useEffect(() => {
    const deptToUse = isRegionManager || isSuperAdmin() || isSuperUser || isRegionUser ? selectedDepartment : userDepartment;
    if (deptToUse) {
      const path = `departments/${deptToUse}/`;
      console.log('useRosterManagement: Setting department path:', path);
      setDepartmentPath(path);
    } else {
      console.log('useRosterManagement: No department found, using empty path');
      setDepartmentPath('');
    }
  }, [userDepartment, selectedDepartment, isRegionManager, isSuperAdmin, isSuperUser, isRegionUser]);

  // Check if current roster is for current or future month
  const currentDate = new Date();
  const rosterDate = new Date(year, month );
  const isCurrentOrFutureMonth = rosterDate >= currentDate;

  // Handle adding a new roster
  const handleAddRoster = async () => {
    // Check if department path is set
    if (!departmentPath) {
      console.warn('useRosterManagement: Department path not set, cannot add roster');
      setAddRosterResult({ success: false, error: 'Department path not set' });
      setIsAddRosterModalOpen(true);
      return;
    }

    const db = getDatabase();
    const rostersRef = ref(db, `${departmentPath}rostersDraft`);
    console.log('useRosterManagement: Fetching rosters from path:', `${departmentPath}rostersDraft`);

    try {
      const rostersSnapshot = await get(rostersRef);
      const availableRosters = rostersSnapshot.exists() ? rostersSnapshot.val() : {};

      let targetYear = year;
      let targetMonth = month + 1;

      if (targetMonth > 12) {
        targetMonth = 1;
        targetYear++;
      }

      while (availableRosters[targetYear]?.[targetMonth]) {
        targetMonth++;
        if (targetMonth > 12) {
          targetMonth = 1;
          targetYear++;
        }
        if (targetYear > year + 2) {
          setAddRosterResult({ success: false, error: 'No future months available to add' });
          setIsAddRosterModalOpen(true);
          return;
        }
      }

      setNextRoster({ year: targetYear, month: targetMonth });
      setIsAddRosterModalOpen(true);
      setAddRosterResult(null);
    } catch (error) {
      console.error('Error preparing to add roster:', error);
      setAddRosterResult({ success: false, error: error.message });
    }
  };

  // Confirm adding a new roster
  const confirmAddRoster = async () => {
    if (!nextRoster) return;

    try {
      const result = await addRoster(nextRoster.year, nextRoster.month);
      await refetchRosters();
      await refetch();
      setAddRosterResult(result);
    } catch (err) {
      setAddRosterResult({ success: false, error: err.message });
    }
  };

  // Close add roster modal
  const closeAddRosterModal = () => {
    setIsAddRosterModalOpen(false);
    setNextRoster(null);
    setAddRosterResult(null);
  };

  // Handle deleting the last row
  const handleDeleteLastRow = async () => {
    if (!isCurrentOrFutureMonth || localRosterData.length === 0) return;

    // Check if department path is set
    if (!departmentPath) {
      console.warn('useRosterManagement: Department path not set, cannot delete last row');
      setDeleteResult({ success: false, error: 'Department path not set' });
      setIsDeleteModalOpen(true);
      return;
    }

    const db = getDatabase();
    try {
      const rostersRef = ref(db, `${departmentPath}rostersDraft`);
      console.log('useRosterManagement: Fetching rosters for deletion from path:', `${departmentPath}rostersDraft`);
      const rostersSnapshot = await get(rostersRef);
      const availableRosters = rostersSnapshot.exists() ? rostersSnapshot.val() : {};
      const affectedMonthsList = [];

      affectedMonthsList.push({ year: parseInt(year), month: parseInt(month) });

      Object.entries(availableRosters).forEach(([rosterYear, months]) => {
        const yearNum = parseInt(rosterYear);
        if (yearNum < year || (yearNum === year && parseInt(month) >= 12)) return;

        Object.keys(months).forEach((rosterMonth) => {
          const monthNum = parseInt(rosterMonth);
          if (isNaN(monthNum) || monthNum < 1 || monthNum > 12) {
            console.warn(`Invalid month ${rosterMonth} in rosters/${rosterYear}`);
            return;
          }
          if (yearNum === year && monthNum <= month) return;
          if (!months[rosterMonth]?.employeeShifts) {
            console.warn(`No employeeShifts in rosters/${rosterYear}/${rosterMonth}`);
            return;
          }
          affectedMonthsList.push({ year: yearNum, month: monthNum });
        });
      });

      affectedMonthsList.sort((a, b) => {
        if (a.year !== b.year) return a.year - b.year;
        return a.month - b.month;
      });

      setAffectedDeleteMonths(affectedMonthsList);
      setIsDeleteModalOpen(true);
      setDeleteResult(null);
    } catch (err) {
      setDeleteResult({ success: false, error: err.message });
      console.error('Error fetching affected months for deletion:', err);
    }
  };

  // Confirm deleting the last row
  const confirmDeleteLastRow = async () => {
    if (!isCurrentOrFutureMonth || localRosterData.length === 0) return;

    // Check if department path is set
    if (!departmentPath) {
      console.warn('useRosterManagement: Department path not set, cannot confirm delete last row');
      setDeleteResult({ success: false, error: 'Department path not set' });
      return;
    }

    setIsDeleting(true);
    const db = getDatabase();

    try {
      const lastEmployee = localRosterData[localRosterData.length - 1];
      const employeeId = lastEmployee.employeeId;

      const rostersRef = ref(db, `${departmentPath}rostersDraft`);
      console.log('useRosterManagement: Fetching rosters for confirmed deletion from path:', `${departmentPath}rostersDraft`);
      const rostersSnapshot = await get(rostersRef);
      const availableRosters = rostersSnapshot.exists() ? rostersSnapshot.val() : {};
      const affectedMonths = [];

      affectedMonths.push({ year: parseInt(year), month: parseInt(month) });

      Object.entries(availableRosters).forEach(([rosterYear, months]) => {
        const yearNum = parseInt(rosterYear);
        if (yearNum < year || (yearNum === year && parseInt(month) >= 12)) return;

        Object.keys(months).forEach((rosterMonth) => {
          const monthNum = parseInt(rosterMonth);
          if (isNaN(monthNum) || monthNum < 1 || monthNum > 12) {
            console.warn(`Invalid month ${rosterMonth} in rostersDraft/${rosterYear}`);
            return;
          }
          if (yearNum === year && monthNum <= month) return;
          if (!months[rosterMonth]?.employeeShifts) {
            console.warn(`No employeeShifts in rostersDraft/${rosterYear}/${rosterMonth}`);
            return;
          }
          affectedMonths.push({ year: yearNum, month: monthNum });
        });
      });

      affectedMonths.sort((a, b) => {
        if (a.year !== b.year) return a.year - b.year;
        return a.month - b.month;
      });

      const currentRosterRef = ref(db, `${departmentPath}rostersDraft/${year}/${month}/employeeShifts/${employeeId}`);
      console.log('useRosterManagement: Checking employee in roster path:', `${departmentPath}rostersDraft/${year}/${month}/employeeShifts/${employeeId}`);
      const currentRosterSnapshot = await get(currentRosterRef);
      const rowIndexToRemove = currentRosterSnapshot.exists() ? currentRosterSnapshot.val().rowIndex : null;

      if (rowIndexToRemove === null) {
        throw new Error(`Employee ${employeeId} not found in roster for ${departmentPath}rostersDraft/${year}/${month}`);
      }

      for (const { year: rosterYear, month: rosterMonth } of affectedMonths) {
        const rosterRef = ref(db, `${departmentPath}rostersDraft/${rosterYear}/${rosterMonth}/employeeShifts`);
        console.log('useRosterManagement: Processing roster path for deletion:', `${departmentPath}rostersDraft/${rosterYear}/${rosterMonth}/employeeShifts`);
        const rosterSnapshot = await get(rosterRef);
        let employeeShifts = rosterSnapshot.exists() ? rosterSnapshot.val() : {};

        if (!employeeShifts[employeeId]) {
          console.log(`Employee ${employeeId} not in roster ${rosterYear}/${rosterMonth}, skipping`);
          continue;
        }

        const updates = {};
        updates[`${employeeId}`] = null;

        Object.entries(employeeShifts).forEach(([empId, data]) => {
          if (empId !== employeeId && data.rowIndex > rowIndexToRemove) {
            updates[`${empId}/rowIndex`] = data.rowIndex - 1;
          }
        });

        await update(rosterRef, updates);
        console.log(`Removed employee ${employeeId} from ${rosterYear}/${rosterMonth} and adjusted rowIndex`);
      }

      const updatedRosterData = localRosterData.slice(0, -1);
      setLocalRosterData(updatedRosterData);
      await fetchAvailableEmployees();
      setDeleteResult({ success: true });
    } catch (err) {
      setDeleteResult({ success: false, error: err.message });
      console.error('Error deleting last row:', err);
    } finally {
      setIsDeleting(false);
    }
  };

  // Close delete modal
  const closeDeleteModal = () => {
    setIsDeleteModalOpen(false);
    setDeleteResult(null);
    setAffectedDeleteMonths([]);
  };

  return {
    // Add roster
    isAddRosterModalOpen,
    addRosterResult,
    nextRoster,
    handleAddRoster,
    confirmAddRoster,
    closeAddRosterModal,

    // Delete row
    isDeleteModalOpen,
    deleteResult,
    isDeleting,
    affectedDeleteMonths,
    isCurrentOrFutureMonth,
    handleDeleteLastRow,
    confirmDeleteLastRow,
    closeDeleteModal,
  };
};

export default useRosterManagement;
