// src/hooks/useEmployeeManagement.js
import { useState, useEffect, useContext } from 'react';
import { get, ref, getDatabase } from 'firebase/database';
import { AuthContext } from '../components/AuthProvider';

/**
 * Custom hook to manage employee-related functionality
 * @param {number} year - Current year
 * @param {number} month - Current month
 * @param {Function} addEmployee - Function to add an employee from useAddEmployee hook
 * @param {Function} refetch - Function to refetch roster data
 * @returns {Object} State and functions for managing employees
 */
const useEmployeeManagement = (year, month, addEmployee, refetch) => {
  const { userDepartment, selectedDepartment, isRegionManager, isSuperAdmin, isSuperUser, isRegionUser } = useContext(AuthContext);

  const [availableEmployees, setAvailableEmployees] = useState([]);
  const [isAddEmployeeModalOpen, setIsAddEmployeeModalOpen] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [addEmployeeResult, setAddEmployeeResult] = useState(null);
  const [affectedMonths, setAffectedMonths] = useState([]);
  const [departmentPath, setDepartmentPath] = useState('');

  // Set department path when department changes
  useEffect(() => {
    const deptToUse = isRegionManager || isSuperAdmin() || isSuperUser || isRegionUser ? selectedDepartment : userDepartment;
    if (deptToUse) {
      const path = `departments/${deptToUse}/`;
      console.log('useEmployeeManagement: Setting department path:', path);
      setDepartmentPath(path);
    } else {
      console.log('useEmployeeManagement: No department found, using empty path');
      setDepartmentPath('');
    }
  }, [userDepartment, selectedDepartment, isRegionManager, isSuperAdmin, isSuperUser, isRegionUser]);

  // Fetch employees not in current roster
  const fetchAvailableEmployees = async () => {
    const db = getDatabase();

    // Use department-specific paths
    const employeesRef = ref(db, `${departmentPath}employees`);
    const rosterRef = ref(db, `${departmentPath}rostersDraft/${year}/${month}`);

    try {
      const [employeesSnapshot, rosterSnapshot] = await Promise.all([
        get(employeesRef),
        get(rosterRef),
      ]);

      const employees = employeesSnapshot.exists() ? employeesSnapshot.val() : {};
      const employeeShifts = rosterSnapshot.exists() ? rosterSnapshot.val().employeeShifts || {} : {};
      const currentEmployeeIds = Object.keys(employeeShifts);

      const available = Object.entries(employees)
        .filter(([empId]) => !currentEmployeeIds.includes(empId))
        .map(([empId, data]) => ({ employeeId: empId, name: data.name }));

      setAvailableEmployees(available);
    } catch (error) {
      console.error('Error fetching available employees:', error);
    }
  };

  // Fetch available employees when year, month, or departmentPath changes
  useEffect(() => {
    if (departmentPath) {
      console.log('useEmployeeManagement: Fetching available employees with path:', departmentPath);
      fetchAvailableEmployees();
    }
  }, [year, month, departmentPath]);

  // Handle adding an employee
  const handleAddEmployee = async (employeeId) => {
    const employee = availableEmployees.find(emp => emp.employeeId === employeeId);
    if (!employee) return;

    setIsAddEmployeeModalOpen(true);
    setSelectedEmployee(employee);
    setAddEmployeeResult(null);

    try {
      const { affectedMonths } = await addEmployee(employeeId, true);
      setAffectedMonths(affectedMonths);
    } catch (err) {
      setAffectedMonths([{ year, month: parseInt(month) }]);
      console.error('Error fetching affected months:', err);
    }
  };

  // Confirm adding an employee
  const confirmAddEmployee = async () => {
    if (!selectedEmployee) return;

    try {
      const result = await addEmployee(selectedEmployee.employeeId);
      await refetch();
      await fetchAvailableEmployees();
      setAddEmployeeResult(result);
    } catch (err) {
      setAddEmployeeResult({ success: false, error: err.message });
    }
  };

  // Close the add employee modal and reset state
  const closeAddEmployeeModal = () => {
    setIsAddEmployeeModalOpen(false);
    setSelectedEmployee(null);
    setAddEmployeeResult(null);
    setAffectedMonths([]);

    // Force a refresh of available employees to ensure UI is in sync
    fetchAvailableEmployees();
  };

  return {
    availableEmployees,
    isAddEmployeeModalOpen,
    selectedEmployee,
    addEmployeeResult,
    affectedMonths,
    fetchAvailableEmployees,
    handleAddEmployee,
    confirmAddEmployee,
    closeAddEmployeeModal,
  };
};

export default useEmployeeManagement;
