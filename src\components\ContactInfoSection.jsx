import React, { useState, useContext } from 'react';
import { toast } from 'react-toastify';
import { AuthContext } from './AuthProvider';

const ContactInfoSection = ({ contactInfo = {}, onUpdateContactInfo, onAddContactInfo, onDeleteContactInfo, isSubmitting, isInitializing }) => {
  const { userDepartment } = useContext(AuthContext);

  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [currentContactKey, setCurrentContactKey] = useState(null);
  const [contactForm, setContactForm] = useState({ key: '', phone: '' });

  // Updated phone validation: Accepts +**********, (*************, ************, etc.
  const validatePhone = (phone) => {
    // Remove all non-digit characters for validation
    const cleanedPhone = phone.replace(/[^\d+]/g, '');
    // Accepts: + followed by 10-15 digits, or 10-15 digits with optional separators
    return /^(\+?\d{10,15})$/.test(cleanedPhone);
  };

  const validateKey = (key) => /^[a-zA-Z0-9_-]+$/.test(key) && !contactInfo[key];

  const handleEditContact = async () => {
    if (isSubmitting || isInitializing) return;

    if (!onUpdateContactInfo) {
      toast.error('Update functionality is not available.');
      return;
    }
    if (!contactForm.phone) {
      toast.error('Phone number is required.');
      return;
    }
    if (!validatePhone(contactForm.phone)) {
      toast.error('Invalid phone format. Use formats like +**********, (*************, or ************.');
      return;
    }

    try {
      await onUpdateContactInfo(currentContactKey, contactForm.phone);
      toast.success('Contact info updated successfully!');
      setIsEditModalOpen(false);
      setContactForm({ key: '', phone: '' });
      setCurrentContactKey(null);
    } catch (err) {
      toast.error(err.message || 'Failed to update contact info.');
    }
  };

  const handleAddContact = async () => {
    if (isSubmitting || isInitializing) return;

    if (!onAddContactInfo) {
      toast.error('Add functionality is not available.');
      return;
    }
    if (!contactForm.key || !contactForm.phone) {
      toast.error('Both key and phone number are required.');
      return;
    }
    if (!validateKey(contactForm.key)) {
      toast.error('Invalid or duplicate key. Use letters, numbers, underscores, or hyphens.');
      return;
    }
    if (!validatePhone(contactForm.phone)) {
      toast.error('Invalid phone format. Use formats like +**********, (*************, or ************.');
      return;
    }

    try {
      await onAddContactInfo(contactForm.key, contactForm.phone);
      toast.success('Contact info added successfully!');
      setIsAddModalOpen(false);
      setContactForm({ key: '', phone: '' });
    } catch (err) {
      toast.error(err.message || 'Failed to add contact info.');
      console.error('Add contact error:', err);
    }
  };

  const handleDeleteContact = async (key) => {
    if (isSubmitting || isInitializing) return;

    if (!onDeleteContactInfo) {
      toast.error('Delete functionality is not available.');
      return;
    }
    if (!window.confirm(`Delete contact info for ${key}? This cannot be undone.`)) return;

    try {
      await onDeleteContactInfo(key);
      toast.success(`Contact info ${key} deleted successfully!`);
    } catch (err) {
      toast.error(err.message || 'Failed to delete contact info.');
      console.error('Delete contact error:', err);
    }
  };

  return (
    <div className="mb-8">
      <h2 className="text-xl font-bold mb-4">Contact Information</h2>
      <div className="mb-4">
        <button
          onClick={() => {
            setContactForm({ key: '', phone: '' });
            setIsAddModalOpen(true);
          }}
          className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400"
          disabled={isSubmitting || isInitializing || !onAddContactInfo}
          aria-label="Add new contact information"
          title="Add new contact information"
        >
          Add Contact
        </button>
      </div>
      <div className="overflow-x-auto">
        <table className="min-w-full border-collapse border border-gray-300">
          <thead>
            <tr className="bg-gray-100">
              <th className="border border-gray-300 p-2 text-left">Key</th>
              <th className="border border-gray-300 p-2 text-left">Phone</th>
              <th className="border border-gray-300 p-2 text-left">Actions</th>
            </tr>
          </thead>
          <tbody>
            {Object.entries(contactInfo).map(([key, info]) => (
              <tr key={key} className="hover:bg-gray-50">
                <td className="border border-gray-300 p-2">{key}</td>
                <td className="border border-gray-300 p-2">{info.phone}</td>
                <td className="border border-gray-300 p-2 flex gap-2">
                  <button
                    onClick={() => {
                      setCurrentContactKey(key);
                      setContactForm({ key, phone: info.phone });
                      setIsEditModalOpen(true);
                    }}
                    className="text-blue-600 hover:underline disabled:text-gray-400"
                    disabled={isSubmitting || isInitializing || !onUpdateContactInfo}
                    aria-label={`Edit contact info for ${key}`}
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => handleDeleteContact(key)}
                    className="text-red-600 hover:underline disabled:text-gray-400"
                    disabled={isSubmitting || isInitializing || !onDeleteContactInfo}
                    aria-label={`Delete contact info for ${key}`}
                  >
                    Delete
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        {Object.keys(contactInfo).length === 0 && (
          <p className="text-gray-500 mt-2">No contact information available.</p>
        )}
      </div>

      {/* Edit Modal */}
      {isEditModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[900]" role="dialog" aria-modal="true" aria-labelledby="edit-modal-title">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h2 id="edit-modal-title" className="text-xl font-bold mb-4">Edit Contact Info: {currentContactKey}</h2>
            <div className="space-y-4">
              <div>
                <label htmlFor="edit-phone" className="block text-sm font-medium text-gray-700 mb-1">Phone *</label>
                <input
                  id="edit-phone"
                  type="text"
                  value={contactForm.phone}
                  onChange={(e) => setContactForm({ ...contactForm, phone: e.target.value })}
                  className="w-full p-2 border rounded-md"
                  required
                  disabled={isSubmitting || isInitializing}
                  aria-required="true"
                />
              </div>
            </div>
            <div className="mt-6 flex justify-end space-x-2">
              <button
                onClick={() => {
                  setIsEditModalOpen(false);
                  setContactForm({ key: '', phone: '' });
                  setCurrentContactKey(null);
                }}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 disabled:bg-gray-400"
                disabled={isSubmitting || isInitializing}
                aria-label="Cancel edit"
              >
                Cancel
              </button>
              <button
                onClick={handleEditContact}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400"
                disabled={isSubmitting || isInitializing}
                aria-label="Update contact info"
              >
                Update
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Add Modal */}
      {isAddModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[900]" role="dialog" aria-modal="true" aria-labelledby="add-modal-title">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h2 id="add-modal-title" className="text-xl font-bold mb-4">Add Contact Info</h2>
            <div className="space-y-4">
              <div>
                <label htmlFor="add-key" className="block text-sm font-medium text-gray-700 mb-1">Key *</label>
                <input
                  id="add-key"
                  type="text"
                  value={contactForm.key}
                  onChange={(e) => setContactForm({ ...contactForm, key: e.target.value })}
                  className="w-full p-2 border rounded-md"
                  required
                  disabled={isSubmitting || isInitializing}
                  aria-required="true"
                />
              </div>
              <div>
                <label htmlFor="add-phone" className="block text-sm font-medium text-gray-700 mb-1">Phone *</label>
                <input
                  id="add-phone"
                  type="text"
                  value={contactForm.phone}
                  onChange={(e) => setContactForm({ ...contactForm, phone: e.target.value })}
                  className="w-full p-2 border rounded-md"
                  required
                  disabled={isSubmitting || isInitializing}
                  aria-required="true"
                />
              </div>
            </div>
            <div className="mt-6 flex justify-end space-x-2">
              <button
                onClick={() => {
                  setIsAddModalOpen(false);
                  setContactForm({ key: '', phone: '' });
                }}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 disabled:bg-gray-400"
                disabled={isSubmitting || isInitializing}
                aria-label="Cancel add"
              >
                Cancel
              </button>
              <button
                onClick={handleAddContact}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400"
                disabled={isSubmitting || isInitializing}
                aria-label="Add contact info"
              >
                Add
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default React.memo(ContactInfoSection);