// src/components/modals/DeleteEmployeeModal.js
import React from 'react';

/**
 * DeleteEmployeeModal component - Modal for confirming employee deletion
 * 
 * @param {Object} props - Component props
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {Object} props.previewData - Preview data from previewEmployeeDelete
 * @param {Object} props.deleteResult - Result of deleting an employee
 * @param {boolean} props.isDeleting - Whether deleting an employee is in progress
 * @param {Function} props.closeModal - Function to close the modal
 * @param {Function} props.confirmDelete - Function to confirm deleting an employee
 * @returns {JSX.Element|null} DeleteEmployeeModal component or null if not open
 */
const DeleteEmployeeModal = ({
  isOpen,
  previewData,
  deleteResult,
  isDeleting,
  closeModal,
  confirmDelete,
}) => {
  if (!isOpen || !previewData) return null;

  const {
    employee,
    affectedEmployees,
    affectedMonths,
    hasRowAdjustments
  } = previewData;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-lg max-h-[90vh] overflow-y-auto">
        <h2 className="text-xl font-bold mb-4 text-red-600">Delete Employee</h2>
        
        {!deleteResult ? (
          <>
            <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Warning: Permanent Deletion</h3>
                  <p className="text-sm text-red-700 mt-1">
                    You are about to permanently delete <strong>{employee.name}</strong> (Position {employee.rowIndex}).
                    This action cannot be undone.
                  </p>
                </div>
              </div>
            </div>

            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Employee Details:</h4>
              <div className="bg-gray-50 rounded-md p-3">
                <div className="text-sm text-gray-700">
                  <div><strong>Name:</strong> {employee.name}</div>
                  <div><strong>Position:</strong> {employee.position || 'Not specified'}</div>
                  <div><strong>Row Index:</strong> {employee.rowIndex}</div>
                  {employee.email && <div><strong>Email:</strong> {employee.email}</div>}
                  {employee.phone && <div><strong>Phone:</strong> {employee.phone}</div>}
                </div>
              </div>
            </div>

            {affectedMonths.length > 0 && (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-900 mb-2">
                  Employee will be removed from {affectedMonths.length} future roster(s):
                </h4>
                <ul className="list-disc pl-5 text-sm text-gray-700 bg-gray-50 rounded-md p-3 max-h-24 overflow-y-auto">
                  {affectedMonths.map(({ year, month }) => (
                    <li key={`${year}-${month}`}>
                      {new Date(year, month - 1).toLocaleString('default', { month: 'long' })} {year}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {hasRowAdjustments && (
              <div className="mb-4">
                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-3">
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-yellow-800">Position Adjustments</h3>
                      <p className="text-sm text-yellow-700 mt-1">
                        Other employees will move up to fill the gap.
                      </p>
                    </div>
                  </div>
                </div>

                <h4 className="text-sm font-medium text-gray-900 mb-2">
                  Employees that will move up:
                </h4>
                <div className="bg-gray-50 rounded-md p-3 max-h-32 overflow-y-auto">
                  {affectedEmployees.map((emp) => (
                    <div key={emp.employeeId} className="text-sm text-gray-700 mb-1">
                      <strong>{emp.name}</strong>: Position {emp.oldRowIndex} → {emp.newRowIndex}
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="bg-gray-50 border border-gray-200 rounded-md p-3 mb-4">
              <p className="text-sm text-gray-700">
                <strong>Summary:</strong> This will permanently delete {employee.name} from the system
                {affectedMonths.length > 0 && `, remove them from ${affectedMonths.length} future roster(s)`}
                {hasRowAdjustments && `, and adjust ${affectedEmployees.length} other employee position(s)`}.
              </p>
            </div>

            {isDeleting && (
              <div className="flex flex-col items-center justify-center mb-4">
                <p className="text-sm text-gray-600 mb-2">Deleting employee...</p>
                <div
                  className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-red-600"
                  role="status"
                  aria-label="Loading"
                ></div>
              </div>
            )}

            <div className="flex justify-end space-x-2">
              <button
                onClick={closeModal}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                disabled={isDeleting}
              >
                Cancel
              </button>
              <button
                onClick={confirmDelete}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
                disabled={isDeleting}
              >
                Delete Employee
              </button>
            </div>
          </>
        ) : deleteResult.success ? (
          <>
            <div className="text-center mb-4">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-3">
                <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Employee Deleted</h3>
              <p className="text-sm text-green-600 mb-2">
                <strong>{employee.name}</strong> has been successfully deleted.
              </p>
              {hasRowAdjustments && (
                <p className="text-sm text-gray-600">
                  {affectedEmployees.length} employee position(s) have been adjusted.
                </p>
              )}
            </div>
            <div className="flex justify-end">
              <button
                onClick={closeModal}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Close
              </button>
            </div>
          </>
        ) : (
          <>
            <div className="text-center mb-4">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-3">
                <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Deletion Failed</h3>
              <p className="text-sm text-red-600 mb-4">
                {deleteResult.error || 'An error occurred while deleting the employee.'}
              </p>
            </div>
            <div className="flex justify-end space-x-2">
              <button
                onClick={closeModal}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
              >
                Cancel
              </button>
              <button
                onClick={confirmDelete}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
              >
                Retry
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default DeleteEmployeeModal;
