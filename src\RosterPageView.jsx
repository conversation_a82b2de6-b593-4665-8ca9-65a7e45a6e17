
// src\RosterPageView.jsx
import React, { useState, useEffect, useMemo } from "react";
import RosterTableViewOnly from "./RosterTableViewOnly";
import useShiftSummary from "./hooks/useShiftSummary";
import {
    getDaysInMonth,
    getDaysOfWeek,
    formatHoursMinutes,
} from "./utils/dateUtils";
import { orderContactInfo } from "./utils/contactUtils";
import RosterHeader from "./components/RosterHeader";
import ShiftSummaryTable from "./components/ShiftSummaryTable";
import ShiftLegend from "./components/ShiftLegend";
import RosterAdditionalInfo from "./components/RosterAdditionalInfo";
// Import data provider hooks
import {
    useRosterData,
    useShiftData,
    useContactInfo,
    useCalendarMarkers
} from "./contexts/DataContext.jsx";
import useDataProvider from "./contexts/useDataProvider";
import RosterNavBar from "./components/RosterNavBar";
import * as XLSX from "xlsx-js-style";
import { saveAs } from "file-saver";
import { getDatabase, ref, get } from 'firebase/database';

function RosterPageView() {
    // Use data from the DataProvider
    const {
        year,
        month,
        isDraftRoster,
        data: rosterData,
        availableYears,
        availableMonths,
        loading: rosterLoading,
        error: rosterError
    } = useRosterData();

    const {
        types: shiftTypes,
        hours: shiftHours,
        colors: shiftColors,
        startTimes: shiftStartTimes,
        endTimes: shiftEndTimes,
        loading: shiftLoading,
        error: shiftError
    } = useShiftData();

    const {
        data: contactInfo,
        loading: contactLoading,
        error: contactError
    } = useContactInfo();

    const {
        weekdayMarkers,
        holidayMarkers,
        specialDayMarkers,
        loading: markersLoading,
        error: markersError
    } = useCalendarMarkers();

    // Use data provider actions
    const {
        changeYear: setYear,
        changeMonth: setMonth,
        toggleDraftMode: toggleRosterView,
        fetchRosterDataWithOverride,
        fetchAvailableYearsWithOverride,
        fetchAvailableMonthsWithOverride
    } = useDataProvider();

    const [localRosterData, setLocalRosterData] = useState([]);

    // Create a simple array of available rosters for export
    const availableRosters = useMemo(() => {
        const result = [];
        if (availableMonths && availableYears) {
            availableYears.forEach(y => {
                const months = availableMonths[y] || [];
                months.forEach(m => {
                    result.push({ year: y, month: m });
                });
            });
        }
        return result;
    }, [availableYears, availableMonths]);

    const daysInMonth = getDaysInMonth(year, month);
    const daysOfWeek = getDaysOfWeek(year, month, daysInMonth);

    // Always use live roster data for this component
    useEffect(() => {
        console.log('Setting RosterPageView to use live roster data (isDraftRoster=false)');
        fetchRosterDataWithOverride(false);
        fetchAvailableYearsWithOverride(false);
    }, [fetchRosterDataWithOverride, fetchAvailableYearsWithOverride]);

    // Fetch available months with override when year changes
    useEffect(() => {
        console.log('Fetching available months for RosterPageView with live roster data');
        fetchAvailableMonthsWithOverride(year, false);
    }, [year, fetchAvailableMonthsWithOverride]);

    useEffect(() => {
        if (rosterData && rosterData.length > 0) {
            const updatedRosterData = rosterData.map(tech => {
                if (tech["Name"] === "Munendra Singh" && tech["1"] === "SL") {
                    return { ...tech, "1": "D1" };
                }
                return tech;
            });
            setLocalRosterData(updatedRosterData);
        } else {
            setLocalRosterData([]);
        }
    }, [rosterData]);

    // Combine loading and error states
    const isLoading =
        rosterLoading ||
        shiftLoading ||
        contactLoading ||
        markersLoading;
    const error =
        rosterError ||
        shiftError ||
        contactError ||
        markersError;
    const { shiftSummary, shiftTotals } = useShiftSummary(localRosterData, daysInMonth);

    let orderedContactInfo = orderContactInfo(contactInfo);

    if (!Array.isArray(orderedContactInfo) || orderedContactInfo.length === 0) {
        orderedContactInfo = [
            { role: "Manager", phone: "(+973) 33 6464 20" },
            { role: "Duty Phone 1", phone: "(+973) 33 6464 21" },
            { role: "Duty Phone 2", phone: "(+973) 33 6464 22" },
            { role: "Duty Phone 3", phone: "(+973) 33 544211" },
            { role: "Admn/Logistics", phone: "(+973) 33 9815 61" },
        ];
    }
    const orderedContactInfo2 = orderContactInfo(contactInfo);



    const defaultShiftColors = {
        'D': '#ffffff',
        'D1': '#fff2af',
        'D2': '#4a90e2',
        'D3': '#4a90e2',
        'N': '#d9e1f2',
        'N1': '#d9e1f2',
        'N2': '#d9e1f2',
        'N3': '#d9e1f2',
        'O': '#93C47D',
        'X': '#f1c40f',
        'R': '#f1c40f',
        'SL': '#6d9eeb',
        'TR': '#00FFFF',
        'DT': '#808080'
    };
    const effectiveShiftColors = { ...defaultShiftColors, ...shiftColors };

    const cleanedHex = (hex) => hex.replace("#", "").toUpperCase();

    // Function to fetch roster data manually without hooks
    const fetchRosterDataManually = async (fetchYear, fetchMonth) => {
        try {
            const db = getDatabase();
            const rosterRef = ref(db, `rosters/${fetchYear}/${fetchMonth}/employeeShifts`);
            const snapshot = await get(rosterRef);

            if (snapshot.exists()) {
                const data = snapshot.val();
                const formattedData = Object.entries(data)
                    .map(([employeeId, employeeData]) => ({
                        employeeId,
                        name: employeeData.name || 'Unknown',
                        srcNumber: employeeData.srcNumber || 'N/A',
                        position: employeeData.position || 'Technician',
                        rowIndex: employeeData.rowIndex || 0,
                        shifts: employeeData.shifts || [],
                        leaveDays: employeeData.leaveDays || 0,
                        sickDays: employeeData.sickDays || 0,
                        workDays: employeeData.workDays || 0,
                        workTime: employeeData.workTime || '0:00',
                        overtimeDays: employeeData.overtimeDays || 0,
                        overtime: employeeData.overtime || '0:00',
                        aircraft: employeeData.aircraft || '0:00',
                        office: employeeData.office || '0:00',
                    }))
                    .sort((a, b) => a.rowIndex - b.rowIndex);
                return formattedData;
            }
            return [];
        } catch (err) {
            console.error(`Error fetching roster data for ${fetchYear}-${fetchMonth}:`, err);
            return [];
        }
    };

    // Function to compute shift summary manually (replicating useShiftSummary logic)
    const computeShiftSummary = (rosterData, daysInMonth) => {
        const shiftSummary = {
            Day: Array(daysInMonth).fill(0),
            Night: Array(daysInMonth).fill(0),
            N1: Array(daysInMonth).fill(0),
            N2: Array(daysInMonth).fill(0),
        };

        rosterData.forEach(tech => {
            for (let day = 1; day <= daysInMonth; day++) {
                const shift = tech.shifts[day - 1] || "";
                if (shift.startsWith("D")) {
                    shiftSummary.Day[day - 1]++;
                } else if (shift.startsWith("N")) {
                    shiftSummary.Night[day - 1]++;
                    if (shift === "N1") shiftSummary.N1[day - 1]++;
                    if (shift === "N2") shiftSummary.N2[day - 1]++;
                }
            }
        });

        return shiftSummary;
    };

    const handleExportToExcel = async () => {
        const wb = XLSX.utils.book_new();

        // Fetch roster data for each available month
        for (const { year: exportYear, month: exportMonth } of availableRosters) {
            const exportRosterData = await fetchRosterDataManually(exportYear, exportMonth);

            if (!exportRosterData || exportRosterData.length === 0) {
                console.warn(`No roster data available for ${exportYear}-${exportMonth}`);
                continue; // Skip this month if no data is available
            }

            const daysInCurrentMonth = getDaysInMonth(exportYear, exportMonth);
            const daysOfWeekRow = [
                "", "", "",
                ...Array.from({ length: daysInCurrentMonth }, (_, i) => {
                    const date = new Date(exportYear, exportMonth - 1, i + 1);
                    return date.toLocaleDateString("en-US", { weekday: "short" });
                }),
                ...Array(8).fill(""),
            ];

            const rosterTableHeaders = [
                "Name", "Position", "Staff/SRC#",
                ...Array.from({ length: daysInCurrentMonth }, (_, i) => (i + 1).toString()),
                "Leave days", "Sick days", "Work days", "Work Time", "Overtime days", "Overtime", "Aircraft", "Office",
            ];

            const rosterTableData = exportRosterData.map((tech) => {
                const row = [];
                rosterTableHeaders.forEach((header, index) => {
                    if (index >= 3 && index < 3 + daysInCurrentMonth) {
                        const day = (index - 2).toString();
                        row.push(tech.shifts[day ] || "");
                    } else if (header === "Name") {
                        row.push(tech.name || "");
                    }
                    else if (header === "Position") {
                        row.push(tech.position || "");
                    } else if (header === "Staff/SRC#") {
                        row.push(tech.srcNumber || "");
                    } else {
                        row.push(tech[header] || "");
                    }
                });
                return row;
            });

            const totals = rosterTableData.reduce(
                (acc, row) => {
                    acc["Leave days"] += parseInt(row[rosterTableHeaders.indexOf("Leave days")]) || 0;
                    acc["Sick days"] += parseInt(row[rosterTableHeaders.indexOf("Sick days")]) || 0;
                    acc["Work days"] += parseInt(row[rosterTableHeaders.indexOf("Work days")]) || 0;
                    const workTimeParts = (row[rosterTableHeaders.indexOf("Work Time")] || "0:00").split(":").map(Number);
                    acc["Work Time"] += workTimeParts[0] * 60 + workTimeParts[1];
                    acc["Overtime days"] += parseInt(row[rosterTableHeaders.indexOf("Overtime days")]) || 0;
                    const overtimeParts = (row[rosterTableHeaders.indexOf("Overtime")] || "0:00").split(":").map(Number);
                    acc["Overtime"] += overtimeParts[0] * 60 + overtimeParts[1];
                    const aircraftParts = (row[rosterTableHeaders.indexOf("Aircraft")] || "0:00").split(":").map(Number);
                    acc["Aircraft"] += aircraftParts[0] * 60 + aircraftParts[1];
                    const officeParts = (row[rosterTableHeaders.indexOf("Office")] || "0:00").split(":").map(Number);
                    acc["Office"] += officeParts[0] * 60 + officeParts[1];
                    return acc;
                },
                { "Leave days": 0, "Sick days": 0, "Work days": 0, "Work Time": 0, "Overtime days": 0, "Overtime": 0, "Aircraft": 0, "Office": 0 }
            );

            totals["Work Time"] = `${Math.floor(totals["Work Time"] / 60)}:${(totals["Work Time"] % 60).toString().padStart(2, "0")}`;
            totals["Overtime"] = `${Math.floor(totals["Overtime"] / 60)}:${(totals["Overtime"] % 60).toString().padStart(2, "0")}`;
            totals["Aircraft"] = `${Math.floor(totals["Aircraft"] / 60)}:${(totals["Aircraft"] % 60).toString().padStart(2, "0")}`;
            totals["Office"] = `${Math.floor(totals["Office"] / 60)}:${(totals["Office"] % 60).toString().padStart(2, "0")}`;

            const totalsRow = rosterTableHeaders.map((header) => totals[header] || "");

            // Compute shift summary manually for the exported month
            const exportShiftSummary = computeShiftSummary(exportRosterData, daysInCurrentMonth);

            const wsData = [
                // Title row
                [...Array(3).fill(""), `${new Date(exportYear, exportMonth - 1).toLocaleString("default", { month: "long" })} ${exportYear} Roster`, ...Array(rosterTableHeaders.length + 3 - 1).fill("")],
                // Add empty rows at the beginning to shift the content down
                ...Array(4).fill(Array(rosterTableHeaders.length + 3).fill("")),  // Add 4 empty rows
                // Days of the week row
                daysOfWeekRow,
                // Roster Table
                rosterTableHeaders,
                ...rosterTableData,
                totalsRow,
                // Empty row after roster table
                Array(rosterTableHeaders.length).fill(""),
            ];

            // Work Schedule section
            const workScheduleHeaders = ["Work Schedule", "", "", "", "", ""];
            const workScheduleData = [
                ["D - Day Shift", shiftHours["D"] || 0.3333333333333333, "", 0.7291666666666666, "", 0.3958333333333333],
                ["D1", shiftHours["D1"] || 0.25, "", 0.7083333333333334, "", 0.45833333333333337],
                ["N1", shiftHours["N1"] || 0.6875, "", 0.14583333333333334, "", 23.458333333333332],
                ["N2", shiftHours["N2"] || 0.75, "", 0.20833333333333334, "", 23.458333333333332],
                ["O - Off", 0, "", 0, "", 0],
            ];

            wsData.push(workScheduleHeaders);
            workScheduleData.forEach(row => wsData.push(row));
            // Empty row after Work Schedule
            wsData.push(Array(rosterTableHeaders.length).fill(""));

            // Contact Details section
            const contactDetailsHeaders = ["Contact Details", ""];
            wsData.push(contactDetailsHeaders);
            orderedContactInfo.forEach(contact => {
                wsData.push([contact.role, contact.phone, "", "", "", ""]);
            });
            // Empty row after contact details
            wsData.push(Array(rosterTableHeaders.length).fill(""));

            // Legends section
            const legendsHeaders = ["Legends", ""];
            wsData.push(legendsHeaders);
            const legendsData = [
                ["Public Holiday (H)", ""],
                ["Vacation (X)", ""],
                ["Duty Travel (DT)", ""],
                ["Sick Leave (SL)", ""],
                ["Training (TR)", ""],
            ];
            legendsData.forEach(row => wsData.push(row));
            // Empty row after Legends
            wsData.push(Array(rosterTableHeaders.length).fill(""));

            // Daily counts
            const dailyCountsHeaders = ["", "Day", ...Array.from({ length: daysInCurrentMonth }, (_, i) => (i + 1).toString()), ""];
            const dailyCountsData = [
                ["Day", ...Array.from({ length: daysInCurrentMonth }, (_, i) => exportShiftSummary["Day"][i] || 0), ""],
                ["Night", ...Array.from({ length: daysInCurrentMonth }, (_, i) => exportShiftSummary["Night"][i] || 0), ""],
                ["N1", ...Array.from({ length: daysInCurrentMonth }, (_, i) => exportShiftSummary["N1"][i] || 0), ""],
                ["N2", ...Array.from({ length: daysInCurrentMonth }, (_, i) => exportShiftSummary["N2"][i] || 0), ""],
            ];
            wsData.push(dailyCountsHeaders);
            dailyCountsData.forEach(row => wsData.push(row));

            const ws = XLSX.utils.aoa_to_sheet(wsData);

            // Define column widths
            ws["!cols"] = [
                { wch: 32 }, // Name
                { wch: 10 }, // Position
                { wch: 12 }, // Staff/SRC#
                ...Array(daysInCurrentMonth).fill({ wch: 4.82 }), // Days of month
                { wch: 10 }, // Leave days
                { wch: 10 }, // Sick days
                { wch: 10 }, // Work days
                { wch: 12 }, // Work Time
                { wch: 12 }, // Overtime days
                { wch: 12 }, // Overtime
                { wch: 12 }, // Aircraft
                { wch: 12 }, // Office
            ];

            // Add merges for headers and titles
            const titleRowIndex = 0;
            const rosterEndRowIndex = titleRowIndex + 7 + rosterTableData.length;
            const workScheduleRowIndex = rosterEndRowIndex + 2;
            const contactDetailsRowIndex = workScheduleRowIndex + workScheduleData.length + 2;
            const legendsRowIndex = contactDetailsRowIndex + orderedContactInfo.length + 2;
            const dailyCountsRowIndex = legendsRowIndex + legendsData.length + 2;

            ws["!merges"] = [
                // Title merge
                { s: { r: titleRowIndex, c: 3 }, e: { r: titleRowIndex + 1, c:34 } },
                // Work Schedule header
                { s: { r: workScheduleRowIndex, c: 0 }, e: { r: workScheduleRowIndex, c: 5 } },
                // Contact Details header
                { s: { r: contactDetailsRowIndex, c: 0 }, e: { r: contactDetailsRowIndex, c: 1 } },
                // Legends header
                { s: { r: legendsRowIndex, c: 0 }, e: { r: legendsRowIndex, c: 1 } },
            ];

            // Set row heights for all rows (14.5 points)
            ws["!rows"] = wsData.map(() => ({ hpt: 14.5 }));

            // Create the custom title style
            const customTitleStyle = {
                font: { bold: true, sz: 18 },
                fill: { patternType: "solid", fgColor: { rgb: "6D9EEB" } },
                alignment: { horizontal: "center", vertical: "center" },
                border: {
                    top: { style: "thin", color: { rgb: "000000" } },
                    bottom: { style: "thin", color: { rgb: "000000" } },
                    left: { style: "thin", color: { rgb: "000000" } },
                    right: { style: "thin", color: { rgb: "000000" } },
                },
            };

            // Apply styles to cells
            const headerStyle = {
                font: { bold: true, sz: 10 },
                fill: { patternType: "solid", fgColor: { rgb: "D3D3D3" } },
                alignment: { horizontal: "center" },
                border: {
                    top: { style: "thin", color: { rgb: "000000" } },
                    bottom: { style: "thin", color: { rgb: "000000" } },
                    left: { style: "thin", color: { rgb: "000000" } },
                    right: { style: "thin", color: { rgb: "000000" } },
                },
            };

            const numberStyle = {
                font: { sz: 10 },
                alignment: { horizontal: "right" },
                border: {
                    top: { style: "thin", color: { rgb: "000000" } },
                    bottom: { style: "thin", color: { rgb: "000000" } },
                    left: { style: "thin", color: { rgb: "000000" } },
                    right: { style: "thin", color: { rgb: "000000" } },
                },
            };

            const centerStyle = {
                font: { sz: 10 },
                alignment: { horizontal: "center", vertical: "center" },
                border: {
                    top: { style: "thin", color: { rgb: "000000" } },
                    bottom: { style: "thin", color: { rgb: "000000" } },
                    left: { style: "thin", color: { rgb: "000000" } },
                    right: { style: "thin", color: { rgb: "000000" } },
                },
            };

            const leftStyle = {
                font: { sz: 10 },
                alignment: { horizontal: "left" },
                border: {
                    top: { style: "thin", color: { rgb: "000000" } },
                    bottom: { style: "thin", color: { rgb: "000000" } },
                    left: { style: "thin", color: { rgb: "000000" } },
                    right: { style: "thin", color: { rgb: "000000" } },
                },
            };

            const applyStyle = (rowIdx, colIdx, style, fillColor = null) => {
                const cellRef = XLSX.utils.encode_cell({ r: rowIdx, c: colIdx });
                if (!ws[cellRef]) ws[cellRef] = { t: "s", v: "" };
                const cellStyle = { ...style };
                if (fillColor) {
                    cellStyle.fill = { patternType: "solid", fgColor: { rgb: cleanedHex(fillColor) } };
                }
                ws[cellRef].s = cellStyle;
            };

            // Apply styles
            applyStyle(titleRowIndex, 3, customTitleStyle);
            for (let col = 3; col <= 3 + daysInCurrentMonth; col++) {
                applyStyle(titleRowIndex, col, customTitleStyle);
            }

            // Apply styles to days of week row
            for (let col = 3; col < 3 + daysInCurrentMonth; col++) {
                applyStyle(titleRowIndex + 5, col, centerStyle);
            }
            // Apply styles to roster headers
            for (let col = 0; col < rosterTableHeaders.length; col++) {
                applyStyle(titleRowIndex + 6, col, headerStyle);
            }

            // Apply styles to roster data
            for (let row = titleRowIndex + 7; row < rosterTableData.length + titleRowIndex + 7; row++) {
                applyStyle(row, 0, leftStyle);
                applyStyle(row, 1, leftStyle);
                applyStyle(row, 2, leftStyle);

                for (let col = 3; col < 3 + daysInCurrentMonth; col++) {
                    const shift = rosterTableData[row - (titleRowIndex + 7)][col];
                    const fillColor = effectiveShiftColors[shift] || "#FFFFFF";
                    applyStyle(row, col, centerStyle, fillColor);
                }
                [
                    rosterTableHeaders.indexOf("Leave days"),
                    rosterTableHeaders.indexOf("Sick days"),
                    rosterTableHeaders.indexOf("Work days"),
                    rosterTableHeaders.indexOf("Overtime days"),
                ].forEach(col => {
                    applyStyle(row, col, numberStyle);
                });

                [
                    rosterTableHeaders.indexOf("Work Time"),
                    rosterTableHeaders.indexOf("Overtime"),
                    rosterTableHeaders.indexOf("Aircraft"),
                    rosterTableHeaders.indexOf("Office"),
                ].forEach(col => {
                    applyStyle(row, col, numberStyle);
                });
            }

            // Apply styles to totals row
            const totalRowIndex = exportRosterData.length + titleRowIndex + 7;
            for (let col = 0; col < rosterTableHeaders.length; col++) {
                const style = col >= rosterTableHeaders.indexOf("Leave days") ? numberStyle : headerStyle;
                applyStyle(totalRowIndex, col, style);
            }
            // Apply style to Work Schedule Header
            applyStyle(workScheduleRowIndex, 0, headerStyle);

            // Apply style to Contact Details header
            applyStyle(contactDetailsRowIndex, 0, headerStyle);

            // Apply style to Legends header
            applyStyle(legendsRowIndex, 0, headerStyle);

            // Apply style to Daily Counts header
            for (let col = 0; col < dailyCountsHeaders.length; col++) {
                applyStyle(dailyCountsRowIndex, col, headerStyle);
            }

            XLSX.utils.book_append_sheet(wb, ws, `${new Date(exportYear, exportMonth - 1).toLocaleString("default", { month: "long" })} ${exportYear} Roster`);
        }

        const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array", bookSST: false });
        const data = new Blob([excelBuffer], { type: "application/octet-stream" });
        saveAs(data, `Rosters ${year}.xlsx`);
    };

    return (
        <div style={{ display: "flex", minHeight: "100vh", position: "relative",  paddingTop: "60px" }}>
            {!isLoading && (
                <RosterNavBar
                    availableYears={availableYears}
                    availableMonths={availableMonths[year] || []}
                    year={year}
                    setYear={setYear}
                    selectedMonth={month}
                    onMonthSelect={setMonth}
                    setMonth={setMonth}
                />
            )}
            <div
                style={{
                    paddingLeft: isLoading ? "0" : "120px",
                    paddingTop: "60px",
                    flex: 1,
                    minHeight: "100vh",
                    backgroundColor: "#f9fafb",
                    paddingBottom: "1rem",
                    boxSizing: "border-box",
                }}
            >
                {isLoading ? (
                    <div
                        style={{
                            minHeight: "calc(100vh - 60px)",
                            display: "flex",
                            width: "100%",
                            alignItems: "center",
                            justifyContent: "center",
                        }}
                    >
                        <div style={{ textAlign: "center", fontSize: "1.25rem" }}>
                            Loading roster, shift, and contact data...
                        </div>
                        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-[1000]">
                            <div className="animate-spin rounded-full h-20 w-20 border-t-4 border-blue-600"></div>
                        </div>
                    </div>
                ) : error ? (
                    <div
                        style={{
                            minHeight: "calc(100vh - 60px)",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                        }}
                    >
                        <div
                            style={{
                                backgroundColor: "#fee2e2",
                                border: "1px solid #f87171",
                                color: "#b91c1c",
                                padding: "0.75rem 1rem",
                                borderRadius: "0.375rem",
                                maxWidth: "28rem",
                                textAlign: "center",
                            }}
                        >
                            <strong style={{ fontWeight: "bold" }}>Error!</strong>
                            <span> {error.message}</span>
                            <p style={{ marginTop: "0.5rem", fontSize: "0.875rem" }}>
                                Please try again later.
                            </p>
                        </div>
                    </div>
                ) : (
                    <div style={{ minHeight: "calc(100vh - 60px)" }}>
                        <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "1rem", width: "700px", minWidth: "600px" }}>
                            <RosterHeader year={year} month={month} />
                            <button
                                onClick={handleExportToExcel}
                                style={{
                                    padding: "0.5rem 1rem",
                                    backgroundColor: "#10b981",
                                    color: "white",
                                    borderRadius: "0.375rem",
                                    cursor: "pointer",
                                    border: "none",
                                    fontSize: "1rem",
                                    display: "block",
                                    visibility: "visible",
                                    opacity: 1,
                                    zIndex: 20,
                                    flexShrink: 0,
                                }}
                            >
                                Download as Excel
                            </button>
                        </div>
                        {localRosterData.length === 0 ? (
                            <div
                                style={{
                                    minHeight: "calc(100vh - 60px)",
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                }}
                            >
                                <div style={{ textAlign: "center", fontSize: "1.25rem" }}>
                                    No roster data available for{" "}
                                    {new Date(year, month - 1).toLocaleString("default", { month: "long" })}{" "}
                                    {year}.
                                </div>
                            </div>
                        ) : (
                            <>
                                <RosterTableViewOnly
                                    rosterData={localRosterData}
                                    shiftTypes={shiftTypes}
                                    shiftHours={shiftHours}
                                    shiftColors={shiftColors}
                                    daysOfWeek={daysOfWeek}
                                    formatHoursMinutes={formatHoursMinutes}
                                    year={year}
                                    month={month}
                                />
                                <ShiftSummaryTable
                                    shiftSummary={shiftSummary}
                                    shiftTotals={shiftTotals}
                                    daysInMonth={daysInMonth}
                                />
                                <ShiftLegend shiftHours={shiftHours} shiftColors={shiftColors} />
                                <RosterAdditionalInfo
                                    shiftHours={shiftHours}
                                    shiftStartTimes={shiftStartTimes}
                                    shiftEndTimes={shiftEndTimes}
                                    orderedContactInfo={orderedContactInfo2}
                                />
                            </>
                        )}
                    </div>
                )}
            </div>
        </div>
    );
}

export default RosterPageView;