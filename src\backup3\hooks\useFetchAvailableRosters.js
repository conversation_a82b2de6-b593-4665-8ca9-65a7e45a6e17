//src/hooks/useFetchAvailableRosters.js
import { useState, useEffect, useCallback } from 'react';
import { getDatabase, ref, onValue } from 'firebase/database';

const useFetchAvailableRosters = (year) => {
  const [availableMonths, setAvailableMonths] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchRosters = useCallback(() => {
    setLoading(true);
    const db = getDatabase();
    const rostersRef = ref(db, `rosters/${year}`);

    const unsubscribe = onValue(
      rostersRef,
      (snapshot) => {
        try {
          if (snapshot.exists()) {
            const rostersData = snapshot.val();
            const months = Object.keys(rostersData)
              .filter((month) => !isNaN(parseInt(month)) && parseInt(month) >= 1 && parseInt(month) <= 12)
              .map((month) => parseInt(month))
              .sort((a, b) => a - b);
            setAvailableMonths(months);
            setError(null);
          } else {
            setAvailableMonths([]);
            setError(new Error(`No rosters available for ${year}`));
          }
        } catch (err) {
          setError(err);
          setAvailableMonths([]);
        } finally {
          setLoading(false);
        }
      },
      (err) => {
        setError(err);
        setAvailableMonths([]);
        setLoading(false);
      }
    );

    return () => unsubscribe();
  }, [year]);

  useEffect(() => {
    const cleanup = fetchRosters();
    return cleanup;
  }, [fetchRosters]);

  return { availableMonths, loading, error, refetch: fetchRosters };
};

export default useFetchAvailableRosters;