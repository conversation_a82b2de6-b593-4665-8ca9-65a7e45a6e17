import { useState, useEffect, useCallback, useMemo } from 'react';
import { getDatabase, ref, onValue } from 'firebase/database';

const useFetchRosterGen = (year, month, isDraftRoster) => {
  const [rawData, setRawData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Memoize the formatted roster data based on rawData
  const rosterData = useMemo(() => {
    if (!rawData) return [];

    try {
      return Object.entries(rawData)
        .map(([employeeId, employeeData]) => ({
          employeeId,
          Name: employeeData.name || 'Unknown',
          'Staff/SRC#': employeeData.srcNumber || '',
          Position: employeeData.position || 'Technician',
          rowIndex: employeeData.rowIndex || 0,
          ...employeeData.shifts,
          'Leave days': employeeData.leaveDays || 0,
          'Sick days': employeeData.sickDays || 0,
          'Work days': employeeData.workDays || 0,
          'Work Time': employeeData.workTime || '0:00',
          'Overtime days': employeeData.overtimeDays || 0,
          Overtime: employeeData.overtime || '0:00',
          Aircraft: employeeData.aircraft || '0:00',
          Office: employeeData.office || '0:00',
        }))
        .sort((a, b) => a.rowIndex - b.rowIndex);
    } catch (err) {
      setError(new Error('Failed to process roster data: ' + err.message));
      return [];
    }
  }, [rawData]);

  const fetchRosterData = useCallback(() => {
    setLoading(true);
    const db = getDatabase();
    // Replace console.log with a production-safe logger
    const logger = process.env.NODE_ENV === 'development' ? console.log : () => {};
    logger(isDraftRoster ? 'Draft Roster' : 'Live Roster');

    const newRosterRef = ref(db, isDraftRoster
      ? `rostersDraft/${year}/${month}/employeeShifts`
      : `rosters/${year}/${month}/employeeShifts`
    );

    const unsubscribe = onValue(
      newRosterRef,
      (snapshot) => {
        try {
          if (snapshot.exists()) {
            setRawData(snapshot.val());
            setError(null);
          } else {
            setRawData(null);
            setError(new Error('No roster data found for the selected year and month'));
          }
        } catch (err) {
          setRawData(null);
          setError(new Error('Failed to fetch roster data: ' + err.message));
        } finally {
          setLoading(false);
        }
      },
      (err) => {
        setRawData(null);
        setError(new Error('Failed to fetch roster data: ' + err.message));
        setLoading(false);
      }
    );

    return () => unsubscribe();
  }, [year, month, isDraftRoster]);

  useEffect(() => {
    const cleanup = fetchRosterData();
    return cleanup;
  }, [fetchRosterData]);

  return { rosterData, loading, error, refetch: fetchRosterData };
};

export default useFetchRosterGen;