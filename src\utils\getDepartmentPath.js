// src\utils\getDepartmentPath.js
// Helper function to get department path
import { getAuth } from 'firebase/auth';
import { getDatabase, ref, get } from 'firebase/database';

const getDepartmentPath = async (isDepartmentSpecific = true, selectedDepartment = null) => {
  if (!isDepartmentSpecific) {
    return '';
  }

  const auth = getAuth();
  const user = auth.currentUser;

  if (!user) return '';

  const db = getDatabase();
  const userRef = ref(db, `users/${user.uid}`);
  const snapshot = await get(userRef);

  if (snapshot.exists()) {
    const userData = snapshot.val();

    // Super-admin, region-manager, super-user, and region-user use selectedDepartment if provided
    if ((userData.role === 'super-admin' || userData.role === 'region-manager' ||
         userData.role === 'super-user' || userData.role === 'region-user') && selectedDepartment) {
      console.log('Selected Department:', selectedDepartment);
      return `departments/${selectedDepartment}/`;
    }

    // For region-manager and region-user without selectedDepartment, check managedDepartments
    if ((userData.role === 'region-manager' || userData.role === 'region-user') &&
        userData.managedDepartments && userData.managedDepartments.length > 0) {
      // Use the first managed department as default
      return `departments/${userData.managedDepartments[0]}/`;
    }

    // Return the user's department path if available (for regular admin/user)
    return userData.department ? `departments/${userData.department}/` : '';
  }

  return '';
};

export default getDepartmentPath;