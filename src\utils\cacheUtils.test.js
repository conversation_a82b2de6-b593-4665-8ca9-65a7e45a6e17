// src/utils/cacheUtils.test.js
import dataCache from './cacheUtils';

describe('CacheUtils', () => {
  beforeEach(() => {
    // Clear the cache before each test
    dataCache.clear();
    
    // Mock Date.now() to control time
    jest.spyOn(Date, 'now').mockImplementation(() => 1000);
  });
  
  afterEach(() => {
    // Restore Date.now()
    jest.restoreAllMocks();
  });
  
  test('sets and gets values correctly', () => {
    // Set a value
    dataCache.set('testKey', 'testValue');
    
    // Get the value
    expect(dataCache.get('testKey')).toBe('testValue');
  });
  
  test('returns null for non-existent keys', () => {
    expect(dataCache.get('nonExistentKey')).toBeNull();
  });
  
  test('checks if a key exists', () => {
    dataCache.set('testKey', 'testValue');
    
    expect(dataCache.has('testKey')).toBe(true);
    expect(dataCache.has('nonExistentKey')).toBe(false);
  });
  
  test('deletes keys', () => {
    dataCache.set('testKey', 'testValue');
    expect(dataCache.has('testKey')).toBe(true);
    
    dataCache.delete('testKey');
    expect(dataCache.has('testKey')).toBe(false);
  });
  
  test('clears all keys', () => {
    dataCache.set('testKey1', 'testValue1');
    dataCache.set('testKey2', 'testValue2');
    
    expect(dataCache.size()).toBe(2);
    
    dataCache.clear();
    expect(dataCache.size()).toBe(0);
  });
  
  test('respects TTL for cached items', () => {
    // Set a value with a TTL of 500ms
    dataCache.set('testKey', 'testValue', 500);
    
    // Value should be available immediately
    expect(dataCache.get('testKey')).toBe('testValue');
    
    // Advance time by 600ms (past TTL)
    jest.spyOn(Date, 'now').mockImplementation(() => 1600);
    
    // Value should now be expired
    expect(dataCache.get('testKey')).toBeNull();
  });
  
  test('returns all valid keys', () => {
    dataCache.set('testKey1', 'testValue1');
    dataCache.set('testKey2', 'testValue2');
    dataCache.set('expiredKey', 'expiredValue', 100);
    
    // Advance time to expire one key
    jest.spyOn(Date, 'now').mockImplementation(() => 1200);
    
    const keys = dataCache.keys();
    expect(keys).toHaveLength(2);
    expect(keys).toContain('testKey1');
    expect(keys).toContain('testKey2');
    expect(keys).not.toContain('expiredKey');
  });
  
  test('handles maximum cache size', () => {
    // Create a new cache with max size of 2
    const smallCache = new (dataCache.constructor)({ maxSize: 2 });
    
    smallCache.set('key1', 'value1');
    smallCache.set('key2', 'value2');
    
    // Both keys should be present
    expect(smallCache.has('key1')).toBe(true);
    expect(smallCache.has('key2')).toBe(true);
    
    // Add a third key, which should evict the oldest key (key1)
    smallCache.set('key3', 'value3');
    
    // key1 should be gone, key2 and key3 should remain
    expect(smallCache.has('key1')).toBe(false);
    expect(smallCache.has('key2')).toBe(true);
    expect(smallCache.has('key3')).toBe(true);
  });
});
