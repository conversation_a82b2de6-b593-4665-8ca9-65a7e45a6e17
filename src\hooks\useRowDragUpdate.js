// src/hooks/useRowDragUpdate.js
import { useState, useCallback, useContext } from 'react';
import { AuthContext } from '../components/AuthProvider';
import { previewEmployeeUpdate, updateEmployee } from '../contexts/actions/employeeActions';
import { useDataDispatch } from '../contexts/DataContext';

/**
 * Simple hook for row drag and drop updates
 * Avoids the complex useDataProvider to prevent re-render issues
 */
const useRowDragUpdate = () => {
  const { selectedDepartment, isSuperAdmin, isRegionManager, isSuperUser, isRegionUser } = useContext(AuthContext);
  const dispatch = useDataDispatch();

  // Modal state
  const [isRowUpdateModalOpen, setIsRowUpdateModalOpen] = useState(false);
  const [rowUpdatePreviewData, setRowUpdatePreviewData] = useState(null);
  const [rowUpdateResult, setRowUpdateResult] = useState(null);
  const [isRowUpdating, setIsRowUpdating] = useState(false);

  // Helper function to determine if we should use selectedDepartment
  const shouldUseSelectedDepartment = () => isSuperAdmin() || isRegionManager || isSuperUser || isRegionUser;

  // Handle row reorder preview
  const handleRowReorder = useCallback(async (reorderData) => {
    try {
      const { employeeId, newRowIndex } = reorderData;
      
      // Get preview data and show confirmation modal
      const preview = await previewEmployeeUpdate(employeeId, { rowIndex: newRowIndex });
      setRowUpdatePreviewData(preview);
      setRowUpdateResult(null);
      setIsRowUpdateModalOpen(true);
    } catch (err) {
      console.error('Failed to preview row reorder:', err);
      alert('Failed to preview row reorder: ' + err.message);
    }
  }, []);

  // Confirm row update
  const confirmRowUpdate = useCallback(async () => {
    if (!rowUpdatePreviewData) return;
    
    setIsRowUpdating(true);
    try {
      const { currentEmployee, newRowIndex } = rowUpdatePreviewData;
      
      // Determine department to use
      const deptToUse = shouldUseSelectedDepartment() ? selectedDepartment : null;
      
      // Call the actual updateEmployee function
      const success = await updateEmployee(dispatch, currentEmployee.employeeId, { rowIndex: newRowIndex }, deptToUse);
      
      if (success) {
        setRowUpdateResult({ success: true });
        
        // The roster data will be automatically updated through the employee update system
        // which triggers a re-fetch of roster data, so the UI will update automatically
      } else {
        setRowUpdateResult({ success: false, error: 'Failed to update employee position' });
      }
    } catch (err) {
      setRowUpdateResult({ success: false, error: err.message });
    } finally {
      setIsRowUpdating(false);
    }
  }, [rowUpdatePreviewData, dispatch, selectedDepartment, shouldUseSelectedDepartment]);

  // Close modal
  const closeRowUpdateModal = useCallback(() => {
    setIsRowUpdateModalOpen(false);
    setRowUpdatePreviewData(null);
    setRowUpdateResult(null);
  }, []);

  return {
    // Modal state
    isRowUpdateModalOpen,
    rowUpdatePreviewData,
    rowUpdateResult,
    isRowUpdating,
    
    // Functions
    handleRowReorder,
    confirmRowUpdate,
    closeRowUpdateModal
  };
};

export default useRowDragUpdate;
