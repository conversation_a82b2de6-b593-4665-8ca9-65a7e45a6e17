import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

const RoleEditModal = ({ 
  isOpen, 
  onClose, 
  user, 
  availableRoles, 
  onSave,
  currentUserRole,
  maxAdmins,
  currentAdminCount 
}) => {
  const [selectedRole, setSelectedRole] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Role information with descriptions and permissions
  const roleInfo = {
    'user': {
      name: 'User',
      description: 'Basic user with limited access to their own data',
      permissions: [
        'View own schedule and shifts',
        'Update personal contact information',
        'View department roster',
        'Receive notifications'
      ],
      color: 'bg-gray-50 border-gray-200'
    },
    'admin': {
      name: 'Administrator',
      description: 'Department administrator with management capabilities',
      permissions: [
        'Manage department users and roles',
        'Create and edit employee schedules',
        'Manage employee data and information',
        'Access department reports and analytics',
        'Configure department settings'
      ],
      color: 'bg-blue-50 border-blue-200'
    },
    'region-manager': {
      name: 'Region Manager',
      description: 'Multi-department manager with regional oversight',
      permissions: [
        'Manage multiple departments',
        'Create admin and region-user accounts',
        'Access cross-department reports',
        'Regional planning and coordination',
        'Department configuration and setup'
      ],
      color: 'bg-purple-50 border-purple-200'
    },
    'super-user': {
      name: 'Super User',
      description: 'Enhanced user with access to all departments',
      permissions: [
        'Access all department data',
        'View cross-department information',
        'Enhanced reporting capabilities',
        'Read-only access to management features'
      ],
      color: 'bg-green-50 border-green-200'
    },
    'region-user': {
      name: 'Region User',
      description: 'User with access to multiple departments in a region',
      permissions: [
        'Access assigned departments',
        'View multi-department schedules',
        'Regional data visibility',
        'Cross-department coordination'
      ],
      color: 'bg-yellow-50 border-yellow-200'
    },
    'super-admin': {
      name: 'Super Administrator',
      description: 'System administrator with full access (view only)',
      permissions: [
        'Full system access',
        'Manage all users and departments',
        'System configuration',
        'Global reports and analytics'
      ],
      color: 'bg-red-50 border-red-200'
    }
  };

  // Reset selected role when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setSelectedRole('');
    }
  }, [isOpen]);

  // Handle role selection
  const handleRoleSelect = (e) => {
    setSelectedRole(e.target.value);
  };

  // Validate role change
  const validateRoleChange = () => {
    if (!selectedRole) {
      return { valid: false, message: 'Please select a role' };
    }

    if (selectedRole === user?.role) {
      return { valid: false, message: 'User already has this role' };
    }

    // Check admin limits
    if (selectedRole === 'admin' && currentAdminCount >= maxAdmins) {
      return { 
        valid: false, 
        message: `Cannot create more admins. Maximum allowed: ${maxAdmins}. Current: ${currentAdminCount}` 
      };
    }

    return { valid: true };
  };

  // Handle save
  const handleSave = async () => {
    const validation = validateRoleChange();
    if (!validation.valid) {
      alert(validation.message);
      return;
    }

    setIsLoading(true);
    try {
      await onSave(user.uid, selectedRole);
      onClose();
    } catch (error) {
      console.error('Failed to update role:', error);
      alert('Failed to update role. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    setSelectedRole('');
    onClose();
  };

  if (!isOpen || !user) return null;

  const selectedRoleInfo = selectedRole ? roleInfo[selectedRole] : null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold text-gray-900">Edit User Role</h2>
            <button
              onClick={handleCancel}
              className="text-gray-400 hover:text-gray-600"
              disabled={isLoading}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* User Information */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">User Email</label>
                <p className="text-sm text-gray-900">{user.email}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Current Role</label>
                <p className="text-sm text-gray-900 font-medium">{roleInfo[user.role]?.name || user.role}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Department</label>
                <p className="text-sm text-gray-900">{user.department || 'Not Set'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Your Role</label>
                <p className="text-sm text-gray-900">{roleInfo[currentUserRole]?.name || currentUserRole}</p>
              </div>
            </div>
          </div>

          {/* Role Selection */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select New Role
            </label>
            <select
              value={selectedRole}
              onChange={handleRoleSelect}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              disabled={isLoading}
            >
              <option value="">-- Select a Role --</option>
              {availableRoles.map(role => (
                <option key={role.value} value={role.value}>
                  {role.label}
                </option>
              ))}
            </select>
          </div>

          {/* Role Information Display */}
          {selectedRoleInfo && (
            <div className={`mb-6 p-4 rounded-lg border-2 ${selectedRoleInfo.color}`}>
              <h4 className="text-lg font-semibold text-gray-900 mb-2">
                {selectedRoleInfo.name}
              </h4>
              <p className="text-gray-700 mb-3">
                {selectedRoleInfo.description}
              </p>
              <div>
                <h5 className="text-sm font-medium text-gray-900 mb-2">Permissions:</h5>
                <ul className="space-y-1">
                  {selectedRoleInfo.permissions.map((permission, index) => (
                    <li key={index} className="text-sm text-gray-700 flex items-start">
                      <span className="text-green-500 mr-2 mt-0.5">•</span>
                      {permission}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}

          {/* Admin Limit Warning */}
          {selectedRole === 'admin' && (
            <div className="mb-6 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-sm text-yellow-800">
                <strong>Admin Limit:</strong> {currentAdminCount} of {maxAdmins} admins used in this department.
                {currentAdminCount >= maxAdmins && (
                  <span className="text-red-600 font-medium"> Cannot create more admins.</span>
                )}
              </p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3">
            <button
              onClick={handleCancel}
              className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 rounded-md transition-colors disabled:bg-gray-400"
              disabled={isLoading || !selectedRole || selectedRole === user?.role}
            >
              {isLoading ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

RoleEditModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  user: PropTypes.shape({
    uid: PropTypes.string.isRequired,
    email: PropTypes.string.isRequired,
    role: PropTypes.string.isRequired,
    department: PropTypes.string
  }),
  availableRoles: PropTypes.arrayOf(PropTypes.shape({
    value: PropTypes.string.isRequired,
    label: PropTypes.string.isRequired
  })).isRequired,
  onSave: PropTypes.func.isRequired,
  currentUserRole: PropTypes.string.isRequired,
  maxAdmins: PropTypes.number.isRequired,
  currentAdminCount: PropTypes.number.isRequired
};

export default RoleEditModal;
