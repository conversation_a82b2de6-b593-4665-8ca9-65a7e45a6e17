// src/components/RosterTable/RosterTableContainer.js
import React, { useMemo, useCallback, useState, useContext } from 'react';
import { Roster<PERSON>ow, RosterHeader, RosterFooter } from './index';
import RosterInstructions from './RosterInstructions';
import RosterControls from './RosterControls';
import RosterModal from './RosterModal';
import {
  useRosterHistory,
  useSelection,
  useDragAndDrop,
  useRosterModal,
  useAutoScroll
} from '../../hooks/RosterTable';
// Import from DataContext instead
import { AuthContext } from '../../components/AuthProvider';
import { calculateTotals, getDaysInMonth } from '../../utils/rosterUtils';
import useEmployeeReorder from '../../hooks/useEmployeeReorder';
import UpdateEmployeeModal from '../modals/UpdateEmployeeModal';

/**
 * RosterTableContainer component - Main container for the roster table
 *
 * @param {Object} props - Component props
 * @param {Array} props.rosterData - The roster data
 * @param {Function} props.setRosterData - Function to set roster data
 * @param {Array} props.shiftTypes - Available shift types
 * @param {Object} props.shiftHours - Shift hours configuration
 * @param {Object} props.shiftColors - Colors for each shift type
 * @param {Array} props.daysOfWeek - Array of day names
 * @param {number} props.year - Current year
 * @param {number} props.month - Current month
 * @param {Object} props.weekdayMarkers - Weekday markers
 * @param {Object} props.holidayMarkers - Holiday markers
 * @param {Object} props.specialDayMarkers - Special day markers
 * @param {boolean} props.isRosterDraft - Whether the roster is in draft mode
 * @returns {JSX.Element} RosterTableContainer component
 */
const RosterTableContainer = ({
  rosterData,
  setRosterData,
  shiftTypes,
  shiftHours,
  shiftColors,
  daysOfWeek,
  year,
  month,
  weekdayMarkers,
  holidayMarkers,
  isRosterDraft = true,
  specialDayMarkers
}) => {
  // Get user department and write access from AuthContext
  const { userDepartment, hasWriteAccessToDepartment } = useContext(AuthContext);



  // Check if user has write access to the department
  const hasWriteAccess = useMemo(() => {
    return hasWriteAccessToDepartment(userDepartment);
  }, [hasWriteAccessToDepartment, userDepartment]);

  // Calculate the number of days in the given month
  const daysInMonth = useMemo(() => getDaysInMonth(year, month), [year, month]);

  // Replace useRosterMode with useState - only allow edit modes if user has write access
  const [mode, setMode] = useState((isRosterDraft && hasWriteAccess) ? 'view' : 'disabled');

  // Employee reorder functionality - pass current viewing year/month
  const {
    isReorderModalOpen,
    reorderPreviewData,
    reorderResult,
    isReordering,
    handleEmployeeReorder,
    confirmReorder,
    closeReorderModal
  } = useEmployeeReorder(year, month);



  const {
    historyIndex,
    changedCells,
    isSaving,
    addToHistory,
    handleUndo,
    handleSave,
    trackCellChange
  } = useRosterHistory(rosterData, year, month, daysInMonth);

  const {
    selection,
    lastSelected,
    multiSelection,
    dragging,
    setDragging,
    dropdownPosition,
    setDropdownPosition,
    dropTarget,
    setDropTarget,
    handleClearSelection,
    handleCellMouseDown,
    handleCellMouseEnter
  } = useSelection(rosterData, daysInMonth, mode, isRosterDraft);

  const {
    handleDragStart,
    handleDragOver,
    handleDrop
  } = useDragAndDrop(
    rosterData,
    { selection, multiSelection, setDragging, setDropTarget },
    (newData) => {
      // Only update if newData is not null (indicating actual changes occurred)
      if (newData !== null) {
        addToHistory(newData);
        setRosterData(newData);
        return newData;
      }
      return null;
    },
    trackCellChange,
    daysInMonth,
    shiftHours,
    mode,
    isRosterDraft
  );



  // Create local handlers instead of using useRosterData hook
  const handleShiftChange = useCallback((techId, day, shift) => {
    // Prevent actions if not in draft mode or user doesn't have write access
    if (!isRosterDraft || !hasWriteAccess) return null;

    const newData = [...rosterData];
    const techIndex = newData.findIndex(t => t.employeeId === techId);

    if (techIndex === -1) return null;

    const oldShift = newData[techIndex][day];

    // Close dropdown regardless of whether value changed
    setDropdownPosition({ techId: null, day: null });

    // Check if the shift value actually changed
    if (oldShift === shift) {
      // No change needed, just close dropdown and return
      return null;
    }

    // Only proceed with change tracking and updates if value actually changed
    newData[techIndex] = { ...newData[techIndex], [day]: shift };

    // Track the cell change
    trackCellChange(techId, day, oldShift, shift);

    // Update history and state
    addToHistory(newData);
    setRosterData(newData);

    return newData;
  }, [rosterData, isRosterDraft, hasWriteAccess, addToHistory, setRosterData, trackCellChange, setDropdownPosition]);

  const handleModalSubmit = useCallback((modalData) => {
    // Prevent actions if not in draft mode or user doesn't have write access
    if (!isRosterDraft || !hasWriteAccess) return { error: "You don't have permission to edit this roster" };

    const { modalTechId, modalStartDay, modalEndDay, modalShifts } = modalData;
    const newData = [...rosterData];
    const techIndex = newData.findIndex(t => t.employeeId === modalTechId);

    if (techIndex === -1) return { error: "Employee not found" };

    // Apply shifts to the range
    for (let day = modalStartDay; day <= modalEndDay; day++) {
      const dayStr = day.toString();
      const oldShift = newData[techIndex][dayStr];
      const newShift = modalShifts[dayStr];

      // Only update if there's a shift value for this day
      if (newShift !== undefined) {
        newData[techIndex] = { ...newData[techIndex], [dayStr]: newShift };

        // Track the cell change only if value actually changed
        if (oldShift !== newShift) {
          trackCellChange(modalTechId, dayStr, oldShift, newShift);
        }
      }
    }

    // Close dropdown if open (correct format)
    setDropdownPosition({ techId: null, day: null });

    // Update history and return new data
    addToHistory(newData);
    return newData;
  }, [rosterData, isRosterDraft, hasWriteAccess, addToHistory, trackCellChange, setDropdownPosition]);

  const {
    isModalOpen,
    modalTechId,
    modalStartDay,
    modalEndDay,
    modalShifts,
    setModalTechId,
    setModalStartDay,
    setModalEndDay,
    setModalShifts,
    openModal,
    closeModal,
    getModalData
  } = useRosterModal(rosterData, daysInMonth);

  const tableContainerRef = useAutoScroll(dragging, mode);

  // Handle cell click for update and dialog modes
  const handleCellClick = useCallback((techId, day) => {
    // Prevent actions if not in draft mode or user doesn't have write access
    if (!isRosterDraft || !hasWriteAccess) return;

    if (mode === 'update') {
      setDropdownPosition({ techId, day });
    }
    else if (mode === 'dialog') {
      openModal(techId, day);
    }
  }, [isRosterDraft, hasWriteAccess, mode, setDropdownPosition, openModal]);

  // Handle save button click
  const handleSaveClick = useCallback(async () => {
    const success = await handleSave(rosterData, false);
    if (success) {
      alert('Changes saved successfully!');
    } else {
      alert('Failed to save changes to the database. Please try again.');
    }
  }, [handleSave, rosterData]);

  // Handle modal submit
  const handleModalSubmitClick = useCallback(() => {
    const modalData = getModalData();
    const result = handleModalSubmit(modalData);

    if (result && result.error) {
      alert(result.error);
    } else if (result) {
      setRosterData(result);
      closeModal();
    }
  }, [getModalData, handleModalSubmit, setRosterData, closeModal]);

  // Row update modal handlers are now in useRowDragUpdate hook

  // Compute selection rectangle bounds for rows
  const selectionBounds = useMemo(() => {
    const techIds = rosterData.map(t => t.employeeId);
    const startRow = techIds.indexOf(selection.start.techId);
    const endRow = techIds.indexOf(selection.end.techId);

    return {
      minRow: Math.min(startRow, endRow),
      maxRow: Math.max(startRow, endRow),
      minCol: Math.min(selection.start.day, selection.end.day),
      maxCol: Math.max(selection.start.day, selection.end.day),
      techIds
    };
  }, [rosterData, selection]);

  // Memoize the table rows to prevent re-rendering unchanged rows
  const memoizedRows = useMemo(() => {
    const { minRow, maxRow, minCol, maxCol, techIds } = selectionBounds;

    return rosterData.map((tech, idx) => (
      <RosterRow
        key={tech.employeeId}
        tech={tech}
        idx={idx}
        selection={selection}
        dragging={dragging}
        mode={mode}
        handleCellClick={handleCellClick}
        handleCellMouseDown={handleCellMouseDown}
        handleCellMouseEnter={handleCellMouseEnter}
        handleDragStart={handleDragStart}
        handleDragOver={handleDragOver}
        handleDrop={handleDrop}
        handleShiftChange={handleShiftChange}
        dropdownPosition={dropdownPosition}
        setDropdownPosition={setDropdownPosition}
        shiftTypes={shiftTypes}
        shiftColors={shiftColors}
        changedCells={changedCells}
        daysInMonth={daysInMonth}
        minRow={minRow}
        maxRow={maxRow}
        minCol={minCol}
        maxCol={maxCol}
        techIds={techIds}
        multiSelection={multiSelection}
        dropTarget={dropTarget}
        lastSelected={lastSelected}
        // Employee reorder props
        handleEmployeeReorder={handleEmployeeReorder}
      />
    ));
  }, [
    rosterData,
    selection,
    dragging,
    mode,
    dropdownPosition,
    changedCells,
    daysInMonth,
    shiftTypes,
    shiftColors,
    multiSelection,
    dropTarget,
    lastSelected,
    selectionBounds,
    handleCellClick,
    handleCellMouseDown,
    handleCellMouseEnter,
    handleDragStart,
    handleDragOver,
    handleDrop,
    handleShiftChange,
    handleEmployeeReorder
  ]);

  // Calculate totals for the main table
  const totals = useMemo(() => calculateTotals(rosterData), [rosterData]);

  // Check if there is an active selection
  const hasSelection = selection.start.techId || multiSelection.length > 0;

  return (
    <div>
      <RosterInstructions />

      <RosterControls
        mode={mode}
        setMode={setMode}
        isRosterDraft={isRosterDraft}
        hasSelection={hasSelection}
        handleClearSelection={handleClearSelection}
        historyIndex={historyIndex}
        handleUndo={handleUndo}
        handleSave={handleSaveClick}
        isSaving={isSaving}
        changedCells={changedCells}
        hasWriteAccess={hasWriteAccess}
      />

      <div
        style={{
          overflowX: 'auto',
          marginBottom: '2rem',
          position: 'relative'
        }}
        ref={tableContainerRef}
      >
        <table style={{
          minWidth: '100%',
          borderCollapse: 'collapse',
          tableLayout: 'auto',
          willChange: 'transform'
        }}>
          <RosterHeader
            daysInMonth={daysInMonth}
            daysOfWeek={daysOfWeek}
            year={year}
            month={month}
            weekdayMarkers={weekdayMarkers}
            holidayMarkers={holidayMarkers}
            specialDayMarkers={specialDayMarkers}
          />
          <tbody>
            {memoizedRows}
          </tbody>
          <RosterFooter
            totals={totals}
            daysInMonth={daysInMonth}
          />
        </table>
      </div>

      <RosterModal
        isModalOpen={isModalOpen}
        modalTechId={modalTechId}
        modalStartDay={modalStartDay}
        modalEndDay={modalEndDay}
        modalShifts={modalShifts}
        setModalTechId={setModalTechId}
        setModalStartDay={setModalStartDay}
        setModalEndDay={setModalEndDay}
        setModalShifts={setModalShifts}
        closeModal={closeModal}
        handleModalSubmit={handleModalSubmitClick}
        rosterData={rosterData}
        shiftTypes={shiftTypes}
        daysInMonth={daysInMonth}
      />

      {/* Employee Reorder Confirmation Modal */}
      <UpdateEmployeeModal
        isOpen={isReorderModalOpen}
        previewData={reorderPreviewData}
        updateResult={reorderResult}
        isUpdating={isReordering}
        closeModal={closeReorderModal}
        confirmUpdate={confirmReorder}
      />
    </div>
  );
};

export default RosterTableContainer;
