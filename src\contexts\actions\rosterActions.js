//src\contexts\actions\rosterActions.js
import { getDatabase, ref, set, get,onValue } from 'firebase/database';
import getDepartmentPath from '../../utils/getDepartmentPath';
import {
  FETCH_ROSTER_START,
  FETCH_ROSTER_SUCCESS,
  FETCH_ROSTER_ERROR,
  SET_ROSTER_YEAR,
  SET_ROSTER_MONTH,
  TOGGLE_ROSTER_DRAFT,
  SET_AVAILABLE_YEARS,
  SET_AVAILABLE_MONTHS
} from '../DataContext';

// ===== ROSTER ACTIONS =====

// Set roster year
export const setRosterYear = (dispatch, year) => {
  dispatch({ type: SET_ROSTER_YEAR, payload: year });
};

// Set roster month
export const setRosterMonth = (dispatch, month) => {
  dispatch({ type: SET_ROSTER_MONTH, payload: month });
};

// Toggle roster draft mode
export const toggleRosterDraft = (dispatch) => {
  dispatch({ type: TOGGLE_ROSTER_DRAFT });
};

// Fetch available years with optional override for isDraftRoster
export const fetchAvailableYears = async (dispatch, isDraftRoster, overrideIsDraftRoster = null, selectedDepartment = null) => {
  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);

    const db = getDatabase();
    // Use the override value if provided, otherwise use the context value
    const useDraftRoster = overrideIsDraftRoster !== null ? overrideIsDraftRoster : isDraftRoster;
    const path = useDraftRoster ? `${departmentPath}rostersDraft` : `${departmentPath}rosters`;
    const yearsRef = ref(db, path);

    onValue(
      yearsRef,
      (snapshot) => {
        try {
          if (snapshot.exists()) {
            const years = Object.keys(snapshot.val())
              .filter((year) => !isNaN(parseInt(year)))
              .map((year) => parseInt(year))
              .sort((a, b) => a - b);

            dispatch({ type: SET_AVAILABLE_YEARS, payload: years });
          } else {
            dispatch({ type: SET_AVAILABLE_YEARS, payload: [] });
          }
        } catch (err) {
          console.error('Error fetching available years:', err);
          dispatch({ type: SET_AVAILABLE_YEARS, payload: [] });
        }
      },
      (err) => {
        console.error('Error fetching available years:', err);
        dispatch({ type: SET_AVAILABLE_YEARS, payload: [] });
      }
    );
  } catch (err) {
    console.error('Error getting department path:', err);
    dispatch({ type: SET_AVAILABLE_YEARS, payload: [] });
  }
};

// Fetch available months for a year with optional override for isDraftRoster
export const fetchAvailableMonths = async (dispatch, year, isDraftRoster, overrideIsDraftRoster = null, selectedDepartment = null) => {
  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);

    const db = getDatabase();
    // Use the override value if provided, otherwise use the context value
    const useDraftRoster = overrideIsDraftRoster !== null ? overrideIsDraftRoster : isDraftRoster;
    const path = useDraftRoster ? `${departmentPath}rostersDraft/${year}` : `${departmentPath}rosters/${year}`;
    const monthsRef = ref(db, path);

    onValue(
      monthsRef,
      (snapshot) => {
        try {
          if (snapshot.exists()) {
            const months = Object.keys(snapshot.val())
              .filter((month) => !isNaN(parseInt(month)) && parseInt(month) >= 1 && parseInt(month) <= 12)
              .map((month) => parseInt(month))
              .sort((a, b) => a - b);

            dispatch({
              type: SET_AVAILABLE_MONTHS,
              payload: { year, months }
            });
          } else {
            dispatch({
              type: SET_AVAILABLE_MONTHS,
              payload: { year, months: [] }
            });
          }
        } catch (err) {
          console.error('Error fetching available months:', err);
          dispatch({
            type: SET_AVAILABLE_MONTHS,
            payload: { year, months: [] }
          });
        }
      },
      (err) => {
        console.error('Error fetching available months:', err);
        dispatch({
          type: SET_AVAILABLE_MONTHS,
          payload: { year, months: [] }
        });
      }
    );
  } catch (err) {
    console.error('Error getting department path:', err);
    dispatch({
      type: SET_AVAILABLE_MONTHS,
      payload: { year, months: [] }
    });
  }
};

// Fetch roster data with optional override for isDraftRoster
export const fetchRosterData = async (dispatch, year, month, isDraftRoster, overrideIsDraftRoster = null, selectedDepartment = null) => {
  dispatch({ type: FETCH_ROSTER_START });

  // Use the override value if provided, otherwise use the context value
  const useDraftRoster = overrideIsDraftRoster !== null ? overrideIsDraftRoster : isDraftRoster;

  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);

    const db = getDatabase();
    const path = useDraftRoster
      ? `${departmentPath}rostersDraft/${year}/${month}/employeeShifts`
      : `${departmentPath}rosters/${year}/${month}/employeeShifts`;

    const rosterRef = ref(db, path);

    const unsubscribe = onValue(
      rosterRef,
      (snapshot) => {
        try {
          if (snapshot.exists()) {
            const rawData = snapshot.val();

            // Format the data similar to useFetchRosterGen
            const formattedData = Object.entries(rawData)
              .map(([employeeId, employeeData]) => ({
                employeeId,
                Name: employeeData.name || 'Unknown',
                'Staff/SRC#': employeeData.srcNumber || '',
                Position: employeeData.position || 'Technician',
                rowIndex: employeeData.rowIndex || 0,
                ...employeeData.shifts,
                'Leave days': employeeData.leaveDays || 0,
                'Sick days': employeeData.sickDays || 0,
                'Work days': employeeData.workDays || 0,
                'Work Time': employeeData.workTime || '0:00',
                'Overtime days': employeeData.overtimeDays || 0,
                Overtime: employeeData.overtime || '0:00',
                Aircraft: employeeData.aircraft || '0:00',
                Office: employeeData.office || '0:00',
              }))
              .sort((a, b) => a.rowIndex - b.rowIndex);

            dispatch({ type: FETCH_ROSTER_SUCCESS, payload: formattedData });
          } else {
            dispatch({
              type: FETCH_ROSTER_ERROR,
              payload: new Error('No roster data found for the selected year and month')
            });
          }
        } catch (err) {
          dispatch({
            type: FETCH_ROSTER_ERROR,
            payload: new Error('Failed to fetch roster data: ' + err.message)
          });
        }
      },
      (err) => {
        dispatch({
          type: FETCH_ROSTER_ERROR,
          payload: new Error('Failed to fetch roster data: ' + err.message)
        });
      }
    );

    return () => unsubscribe();
  } catch (error) {
    dispatch({
      type: FETCH_ROSTER_ERROR,
      payload: new Error('Failed to fetch roster data: ' + error.message)
    });
  }
};
