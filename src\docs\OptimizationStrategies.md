# Optimization Strategies

This document outlines the optimization strategies used in the roster application to improve performance, reduce unnecessary renders, and enhance user experience.

## Table of Contents

1. [Caching](#caching)
2. [Memoization](#memoization)
3. [Optimistic Updates](#optimistic-updates)
4. [Performance Monitoring](#performance-monitoring)
5. [Code Splitting](#code-splitting)
6. [Best Practices](#best-practices)

## Caching

### In-Memory Cache

The application uses an in-memory cache system to store frequently accessed data:

```javascript
// src/utils/cacheUtils.js
class CacheManager {
  constructor(options = {}) {
    this.cache = new Map();
    this.defaultTTL = options.defaultTTL || 5 * 60 * 1000; // 5 minutes default TTL
    this.maxSize = options.maxSize || 100; // Maximum number of items in cache
  }
  
  // Methods: set, get, has, delete, clear, keys, size
}
```

### TTL (Time-To-Live)

Different types of data have different TTL values based on how frequently they change:

- Employee data: 30 minutes
- Shift data: 60 minutes
- Contact info: 15 minutes
- Calendar markers: 24 hours

### Cache Invalidation

The cache is automatically invalidated when data is modified:

```javascript
// Example: Invalidate cache when updating an employee
export const updateEmployee = async (dispatch, employeeId, employeeData) => {
  try {
    // Optimistically update the UI
    dispatch({ type: UPDATE_EMPLOYEE, payload: { employeeId, ...employeeData } });
    
    // Invalidate the cache
    dataCache.delete('employees_data');
    
    // Perform the actual database update
    const db = getDatabase();
    await update(ref(db, `employees/${employeeId}`), employeeData);
    
    return true;
  } catch (err) {
    // Error handling...
    return false;
  }
};
```

### Cache Visualization

The application includes a CacheStatus component that shows the current state of the cache:

```jsx
// src/components/CacheStatus.jsx
const CacheStatus = () => {
  const [cacheInfo, setCacheInfo] = useState({
    size: 0,
    keys: []
  });
  
  // Component implementation...
};
```

## Memoization

### React's useMemo and useCallback

The application uses React's built-in memoization hooks to prevent unnecessary calculations and re-renders:

```jsx
// Example: Memoize expensive calculations
const daysInMonth = useMemo(() => {
  performanceMonitor.startMeasure('getDaysInMonth');
  const result = getDaysInMonth(year, month);
  performanceMonitor.endMeasure('getDaysInMonth');
  return result;
}, [year, month]);

// Example: Memoize event handlers
const handleToggleRosterView = useCallback(() => {
  toggleRosterView();
}, [toggleRosterView]);
```

### Custom Memoization Hooks

The application includes custom hooks for more advanced memoization needs:

```jsx
// src/hooks/useMemoization.js
export const useDebounce = (value, delay = 300) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);

  return debouncedValue;
};

export const useThrottle = (value, limit = 300) => {
  // Implementation...
};

export const useStableValue = (value, isEqual = (a, b) => a === b) => {
  // Implementation...
};
```

## Optimistic Updates

### Immediate UI Updates

The application updates the UI immediately before database operations complete:

```javascript
// Example: Add employee with optimistic update
export const addEmployee = async (dispatch, employeeData) => {
  try {
    // Optimistically update the UI immediately
    dispatch({ type: ADD_EMPLOYEE, payload: employeeData });
    
    // Invalidate the cache
    dataCache.delete('employees_data');
    
    // Perform the actual database update
    const db = getDatabase();
    await set(ref(db, `employees/${employeeData.employeeId}`), employeeData);
    
    return true;
  } catch (err) {
    // Fetch fresh data to revert the optimistic update
    fetchEmployeesData(dispatch, true);
    
    return false;
  }
};
```

### Error Recovery

If a database operation fails, the application automatically reverts the optimistic update:

```javascript
// Example: Error recovery in updateEmployee
export const updateEmployee = async (dispatch, employeeId, employeeData) => {
  try {
    // Optimistic update...
    // Database operation...
    return true;
  } catch (err) {
    console.error('Error updating employee:', err);
    
    // Fetch fresh data to revert the optimistic update
    fetchEmployeesData(dispatch, true);
    
    return false;
  }
};
```

## Performance Monitoring

### Performance Monitor Utility

The application includes a utility for monitoring and logging performance metrics:

```javascript
// src/utils/performanceMonitor.js
class PerformanceMonitor {
  constructor() {
    this.measurements = {};
    this.isEnabled = process.env.NODE_ENV !== 'production';
    this.logLevel = 'warn'; // 'log', 'warn', 'error', or 'none'
  }
  
  // Methods: startMeasure, endMeasure, measureFunction, etc.
}
```

### Performance Monitoring Component

A visual component displays performance metrics in real-time:

```jsx
// src/components/PerformanceMonitor.jsx
const PerformanceMonitor = () => {
  const [report, setReport] = useState({});
  const [expanded, setExpanded] = useState(false);
  
  // Component implementation...
};
```

### Instrumentation

Key operations are instrumented to track performance:

```jsx
// Example: Measure expensive calculation
const daysInMonth = useMemo(() => {
  performanceMonitor.startMeasure('getDaysInMonth');
  const result = getDaysInMonth(year, month);
  performanceMonitor.endMeasure('getDaysInMonth');
  return result;
}, [year, month]);
```

## Code Splitting

### React.lazy and Suspense

The application uses React's lazy loading to split code into smaller chunks:

```jsx
// Example: Lazy load components
const LazyComponent = React.lazy(() => import('./LazyComponent'));

function App() {
  return (
    <React.Suspense fallback={<div>Loading...</div>}>
      <LazyComponent />
    </React.Suspense>
  );
}
```

### Route-Based Code Splitting

Components are loaded only when needed based on the current route:

```jsx
// Example: Route-based code splitting
const Home = React.lazy(() => import('./Home'));
const EmployeePage = React.lazy(() => import('./EmployeePage'));

function App() {
  return (
    <React.Suspense fallback={<div>Loading...</div>}>
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/employees" element={<EmployeePage />} />
        {/* Other routes */}
      </Routes>
    </React.Suspense>
  );
}
```

## Best Practices

### Conditional Logging

Logging is only enabled in development mode:

```jsx
// Example: Conditional logging
useEffect(() => {
  if (process.env.NODE_ENV !== 'production') {
    console.log("Available months:", availableMonths);
  }
}, [availableMonths]);
```

### Avoiding Unnecessary Re-renders

Components only re-render when their data changes:

```jsx
// Example: Destructure only what you need
const { year, month } = useRosterData();
// Instead of: const rosterData = useRosterData();
```

### Proper Dependency Arrays

useEffect and useMemo hooks have proper dependency arrays:

```jsx
// Example: Proper dependency array
useEffect(() => {
  fetchData();
}, [year, month]); // Only re-run when year or month changes
```

### Cleanup Functions

Effects that set up subscriptions include cleanup functions:

```jsx
// Example: Cleanup function
useEffect(() => {
  const unsubscribe = onValue(ref, callback);
  
  return () => {
    unsubscribe();
  };
}, []);
```

### Error Boundaries

The application uses error boundaries to prevent the entire app from crashing:

```jsx
// Example: Error boundary
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error("Error caught by boundary:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <h1>Something went wrong.</h1>;
    }

    return this.props.children;
  }
}
```
