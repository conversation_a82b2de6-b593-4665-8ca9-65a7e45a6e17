import React, { useContext, useState, useEffect, useRef } from 'react';
import { Link, NavLink, useNavigate, useLocation } from 'react-router-dom';
import { AuthContext } from './components/AuthProvider';
import { auth } from './firebaseConfig';
import { signOut } from 'firebase/auth';
import { toast } from 'react-toastify';
import './Navbar.css';
import { FaBars, FaTimes } from 'react-icons/fa';
import { getDatabase, ref, get } from 'firebase/database';

function Navbar() {
  const { currentUser, userRole, isSuperAdmin, selectedDepartment, setSelectedDepartment } = useContext(AuthContext);
  const navigate = useNavigate();
  const location = useLocation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isLargeScreen, setIsLargeScreen] = useState(window.innerWidth >= 769);
  const [departments, setDepartments] = useState([]);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);

  useEffect(() => {
    const handleResize = () => {
      setIsLargeScreen(window.innerWidth >= 769);
      if (window.innerWidth >= 769) setIsMobileMenuOpen(false);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Fetch departments for super-admin
  useEffect(() => {
    if (isSuperAdmin()) {
      const fetchDepartments = async () => {
        try {
          const db = getDatabase();
          const departmentsRef = ref(db, 'departments');
          const snapshot = await get(departmentsRef);
          if (snapshot.exists()) {
            const deptData = snapshot.val();
            const deptList = Object.keys(deptData).map(deptId => ({
              id: deptId,
              name: deptData[deptId].name || deptId, // Use name or fallback to deptId
            }));
            setDepartments(deptList);
            console.log('Navbar: Fetched departments', deptList);
          }
        } catch (error) {
          console.error('Navbar: Error fetching departments:', error);
          toast.error('Failed to load departments.');
        }
      };
      fetchDepartments();
    }
  }, [isSuperAdmin]);

  // Close dropdown on outside click
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSignOut = async () => {
    try {
      await signOut(auth);
      toast.success('SignedWik out successfully!');
      navigate('/login');
    } catch (error) {
      console.error('Navbar: Sign out error:', error);
      toast.error('Failed to sign out.');
    }
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
    setIsDropdownOpen(false);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
    setIsDropdownOpen(false);
  };

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  const handleDepartmentChange = (deptId) => {
    console.log('Navbar: Changing department to', deptId);
    setSelectedDepartment(deptId);
    setIsDropdownOpen(false);
  };

  const renderMenuItems = () => {
    const baseMenuItems = [
      { to: '/roster', label: 'Roster', showOnMobile: false },
      { to: '/mobile-roster', label: 'Mobile Roster' },
      { to: '/employee-search', label: 'Search' },
      { to: '/about', label: 'About' },
    ];

    const adminMenuItems = [
      { to: '/', label: 'Home', showOnMobile: false },
      { to: '/employees', label: 'Dashboard', showOnMobile: false },
      { to: '/employees-docs', label: 'Employee Docs', showOnMobile: false },
      ...baseMenuItems,
    ];

    const userMenuItems = [...baseMenuItems];

    let menuItems;
    if (userRole === 'admin' || isSuperAdmin()) {
      menuItems = adminMenuItems;
    } else if (userRole === 'user') {
      menuItems = userMenuItems;
    } else {
      return null;
    }

    const currentDeptName = departments.find(dept => dept.id === selectedDepartment)?.name || 'Select Location';

    return (
      <>
        {isSuperAdmin() && (
          <li className="relative" ref={dropdownRef}>
            <button
              onClick={toggleDropdown}
              className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition duration-200"
              aria-expanded={isDropdownOpen}
              aria-label="Select Location"
            >
              <svg
                className="w-5 h-5 text-gray-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                />
              </svg>
              <span>{currentDeptName}</span>
              <svg
                className={`w-4 h-4 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            {isDropdownOpen && (
              <ul className="absolute z-10 mt-2 w-56 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                {departments.map(dept => (
                  <li
                    key={dept.id}
                    className="px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 cursor-pointer transition duration-150"
                    onClick={() => handleDepartmentChange(dept.id)}
                    role="option"
                    aria-selected={selectedDepartment === dept.id}
                  >
                    {dept.name}
                  </li>
                ))}
              </ul>
            )}
          </li>
        )}
        {menuItems.map((item) => (
          (isLargeScreen || item.showOnMobile !== false) && (
            <li key={item.to}>
              <NavLink
                to={item.to}
                className={({ isActive }) => `navbar-link ${isActive ? 'active' : ''}`}
                aria-label={`${item.label} page`}
                onClick={closeMobileMenu}
              >
                {item.label}
              </NavLink>
            </li>
          )
        ))}
        {currentUser && (
          <li>
            <button onClick={handleSignOut} className="navbar-signout mobile-signout">
              Sign Out
            </button>
          </li>
        )}
        {!currentUser && (
          <li>
            <Link to="/login" className="navbar-signin mobile-signin" onClick={closeMobileMenu}>
              Sign In
            </Link>
          </li>
        )}
      </>
    );
  };

  return (
    <nav className="navbar">
      <div className="navbar-container">
        <Link to="/" className="navbar-logo" aria-label="Go to home page">
          <img src="./hrm.png" alt="Logo" className="navbar-icon" />
          {isLargeScreen ? 'HRM' : 'HRM'}
        </Link>
        <div className="mobile-menu-icon" onClick={toggleMobileMenu} aria-label="Toggle mobile navigation">
          {isMobileMenuOpen ? <FaTimes /> : <FaBars />}
        </div>
        <ul className={`navbar-menu ${isMobileMenuOpen ? 'open' : ''}`}>
          {renderMenuItems()}
        </ul>
      </div>
    </nav>
  );
}

export default Navbar;