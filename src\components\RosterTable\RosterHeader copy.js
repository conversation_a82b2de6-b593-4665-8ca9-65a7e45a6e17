// src/components/RosterTable/RosterHeader.js
import React from 'react';
import { getMarkersForDay } from '../../utils/rosterUtils';

/**
 * RosterHeader component - Renders the header of the roster table
 * 
 * @param {Object} props - Component props
 * @param {number} props.daysInMonth - Number of days in the month
 * @param {Array} props.daysOfWeek - Array of day names
 * @param {number} props.year - Current year
 * @param {number} props.month - Current month
 * @param {Object} props.weekdayMarkers - Weekday markers
 * @param {Object} props.holidayMarkers - Holiday markers
 * @param {Object} props.specialDayMarkers - Special day markers
 * @returns {JSX.Element} RosterHeader component
 */
const RosterHeader = ({
  daysInMonth,
  daysOfWeek,
  year,
  month,
  weekdayMarkers,
  holidayMarkers,
  specialDayMarkers
}) => {
  return (
    <thead>
      <tr style={{ backgroundColor: '#e5e7eb' }}>
        <th rowSpan="2" style={{ border: '1px solid #d1d5db', padding: '0.5rem 1rem', width: 'auto', minWidth: '150px', whiteSpace: 'nowrap', height: '80px', textAlign: 'center', verticalAlign: 'middle' }}>Name</th>
        <th rowSpan="2" style={{ border: '1px solid #d1d5db', padding: '0.5rem 1rem', width: '120px', minWidth: '120px', whiteSpace: 'wrap', textAlign: 'center', height: '80px', verticalAlign: 'middle' }}>Staff/SRC#</th>
        <th rowSpan="2" style={{ border: '1px solid #d1d5db', padding: '0.5rem 1rem', width: '80px', minWidth: '80px', whiteSpace: 'wrap', textAlign: 'center', height: '80px', verticalAlign: 'middle' }}>Position</th>
        {Array.from({ length: daysInMonth }, (_, i) => {
          const day = i + 1;
          const markers = getMarkersForDay(year, month, day, weekdayMarkers, holidayMarkers, specialDayMarkers);
          const primaryMarker = markers[0];
          return (
            <th
              key={i}
              style={{
                border: '1px solid #d1d5db',
                padding: '0.25rem 0.5rem',
                textAlign: 'center',
                width: '40px',
                minWidth: '40px',
                height: '40px',
                backgroundColor: primaryMarker ? `${primaryMarker.color}` : '#e5e7eb',
              }}
              title={markers.map((m) => `${m.type.charAt(0).toUpperCase() + m.type.slice(1)}: ${m.name}`).join(', ')}
              aria-label={
                markers.length > 0
                  ? `Day ${day}, ${daysOfWeek[i]}, marked as ${markers.map((m) => `${m.type}: ${m.name}`).join(', ')}`
                  : `Day ${day}, ${daysOfWeek[i]}`
              }
            >
              {daysOfWeek && daysOfWeek[i] ? daysOfWeek[i] : ''}
            </th>
          );
        })}
        <th rowSpan="2" style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', whiteSpace: 'wrap', height: '80px',verticalAlign: 'middle' }}>Leave Days</th>
        <th rowSpan="2" style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', whiteSpace: 'wrap', height: '80px',verticalAlign: 'middle' }}>Sick Days</th>
        <th rowSpan="2" style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', whiteSpace: 'wrap', height: '80px',verticalAlign: 'middle' }}>Work Days</th>
        <th rowSpan="2" style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', whiteSpace: 'wrap', height: '80px',verticalAlign: 'middle' }}>Work Time</th>
        <th rowSpan="2" style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', whiteSpace: 'wrap', height: '80px',verticalAlign: 'middle' }}>Overtime Days</th>
        <th rowSpan="2" style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', whiteSpace: 'wrap', height: '80px',verticalAlign: 'middle'}}>Overtime</th>
        <th rowSpan="2" style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', whiteSpace: 'wrap', height: '80px',verticalAlign: 'middle' }}>Aircraft</th>
        <th rowSpan="2" style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '80px', minWidth: '80px', whiteSpace: 'wrap', height: '80px',verticalAlign: 'middle' }}>Office</th>
      </tr>
      <tr style={{ backgroundColor: '#e5e7eb' }}>
        {Array.from({ length: daysInMonth }, (_, i) => (
          <th key={i} style={{ border: '1px solid #d1d5db', padding: '0.25rem 0.5rem', textAlign: 'center', width: '40px', minWidth: '40px', height: '40px' }}>{i + 1}</th>
        ))}
      </tr>
    </thead>
  );
};

export default RosterHeader;
