// src/components/RosterTable/RosterInstructions.js
import React from 'react';

/**
 * RosterInstructions component - Displays comprehensive instructions for using the roster table
 *
 * @returns {JSX.Element} RosterInstructions component
 */
const RosterInstructions = () => {
  return (
    <div style={{
      marginBottom: '1rem',
      width: 'fit-content',
      padding: '0.75rem',
      backgroundColor: '#e5e7eb',
      borderRadius: '0.375rem',
      maxWidth: '100%'
    }}>
      <strong style={{ fontSize: '0.875rem', color: '#374151', marginBottom: '0.5rem', display: 'block' }}>
        📋 Roster Table Instructions
      </strong>

      {/* Mode Instructions */}
      <div style={{ marginBottom: '0.75rem' }}>
        <strong style={{ fontSize: '0.8rem', color: '#1f2937', display: 'block', marginBottom: '0.25rem' }}>
          🎯 Editing Modes:
        </strong>
        <ul style={{
          fontSize: '0.75rem',
          color: '#374151',
          listStyleType: 'disc',
          marginLeft: '1.25rem',
          lineHeight: '1.4'
        }}>
          <li><strong>Update Mode</strong>: Click any cell to open shift dropdown for quick edits</li>
          <li><strong>Drag Mode</strong>: Select cells and drag to copy shifts to other locations</li>
          <li><strong>Dialog Mode</strong>: Click any cell to open modal for multi-day range editing</li>
        </ul>
      </div>

      {/* Selection Instructions */}
      <div style={{ marginBottom: '0.75rem' }}>
        <strong style={{ fontSize: '0.8rem', color: '#1f2937', display: 'block', marginBottom: '0.25rem' }}>
          🖱️ Selection Methods (Drag Mode):
        </strong>
        <ul style={{
          fontSize: '0.75rem',
          color: '#374151',
          listStyleType: 'disc',
          marginLeft: '1.25rem',
          lineHeight: '1.4'
        }}>
          <li><strong>Single Click</strong>: Select individual cell or start rectangular selection</li>
          <li><strong>Click + Drag</strong>: Create rectangular selection area</li>
          <li><strong>Ctrl + Click</strong>: Add/remove individual cells to multi-selection</li>
          <li><strong>Shift + Click</strong>: Extend selection from last selected cell</li>
        </ul>
      </div>

      {/* Keyboard Shortcuts */}
      <div style={{ marginBottom: '0.75rem' }}>
        <strong style={{ fontSize: '0.8rem', color: '#1f2937', display: 'block', marginBottom: '0.25rem' }}>
          ⌨️ Keyboard Shortcuts (Drag Mode):
        </strong>
        <ul style={{
          fontSize: '0.75rem',
          color: '#374151',
          listStyleType: 'disc',
          marginLeft: '1.25rem',
          lineHeight: '1.4'
        }}>
          <li><strong>Ctrl + A</strong>: Select entire roster table</li>
          <li><strong>Shift + Arrow Keys</strong>: Extend selection in any direction</li>
          <li><strong>Shift + ↑/↓</strong>: Extend selection up/down rows</li>
          <li><strong>Shift + ←/→</strong>: Extend selection left/right days</li>
        </ul>
      </div>

      {/* Drag and Drop */}
      <div style={{ marginBottom: '0.75rem' }}>
        <strong style={{ fontSize: '0.8rem', color: '#1f2937', display: 'block', marginBottom: '0.25rem' }}>
          🔄 Drag & Drop (Drag Mode):
        </strong>
        <ul style={{
          fontSize: '0.75rem',
          color: '#374151',
          listStyleType: 'disc',
          marginLeft: '1.25rem',
          lineHeight: '1.4'
        }}>
          <li><strong>Drag Selection</strong>: Drag selected cells to copy shifts to target location</li>
          <li><strong>Visual Feedback</strong>: Blue border shows drop target, preview shows drag content</li>
          <li><strong>Smart Copy</strong>: Maintains relative positions when copying rectangular selections</li>
          <li><strong>Multi-Cell Support</strong>: Copy individual cells or entire rectangular areas</li>
        </ul>
      </div>

      {/* Visual Indicators */}
      <div style={{ marginBottom: '0.5rem' }}>
        <strong style={{ fontSize: '0.8rem', color: '#1f2937', display: 'block', marginBottom: '0.25rem' }}>
          🎨 Visual Indicators:
        </strong>
        <ul style={{
          fontSize: '0.75rem',
          color: '#374151',
          listStyleType: 'disc',
          marginLeft: '1.25rem',
          lineHeight: '1.4'
        }}>
          <li><strong>Orange Background</strong>: Changed cells (unsaved edits)</li>
          <li><strong>Blue Border</strong>: Multi-selected cells or drop target</li>
          <li><strong>Red Border</strong>: Rectangular selection area</li>
          <li><strong>Dark Blue Border</strong>: Last selected cell</li>
          <li><strong>Gray Background</strong>: New employees (no shifts yet)</li>
        </ul>
      </div>

      {/* Save/Undo */}
      <div>
        <strong style={{ fontSize: '0.8rem', color: '#1f2937', display: 'block', marginBottom: '0.25rem' }}>
          💾 Save & Undo:
        </strong>
        <ul style={{
          fontSize: '0.75rem',
          color: '#374151',
          listStyleType: 'disc',
          marginLeft: '1.25rem',
          lineHeight: '1.4'
        }}>
          <li><strong>Save Changes</strong>: Commit all edits to database (appears when changes exist)</li>
          <li><strong>Undo Change</strong>: Revert to previous state (appears when changes exist)</li>
          <li><strong>Clear Selection</strong>: Remove all selections (appears when cells selected)</li>
          <li><strong>Auto-tracking</strong>: Only actual value changes are tracked and highlighted</li>
        </ul>
      </div>
    </div>
  );
};

export default RosterInstructions;
