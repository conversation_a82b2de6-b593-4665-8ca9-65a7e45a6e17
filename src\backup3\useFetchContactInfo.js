import { useState, useEffect } from 'react';
import { getDatabase, ref, onValue, off } from 'firebase/database';

function useFetchContactInfo() {
  const [contactInfo, setContactInfo] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const db = getDatabase();
    const contactInfoRef = ref(db, 'contactInfo');

    const handleData = (snapshot) => {
      if (snapshot.exists()) {
        const data = snapshot.val();
        console.log('useFetchContactInfo Data:', data);
        setContactInfo(data || {});
      } else {
        setContactInfo({});
      }
      setLoading(false);
    };

    const handleError = (err) => {
      console.error('useFetchContactInfo Error:', err);
      setError(err);
      setLoading(false);
    };

    onValue(contactInfoRef, handleData, handleError);

    return () => off(contactInfoRef, 'value', handleData);
  }, []);

  return { contactInfo, loading, error };
}

export default useFetchContactInfo;