
// src\components\AuthProvider.jsx
import React, { createContext, useState, useEffect } from 'react';
import { auth, database } from '../firebaseConfig';
import { onAuthStateChanged } from 'firebase/auth';
import { get, ref, set } from 'firebase/database';
import { toast } from 'react-toastify';

export const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [userRole, setUserRole] = useState(null);
  const [userDepartment, setUserDepartment] = useState(null);
  const [selectedDepartment, setSelectedDepartment] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setCurrentUser(user);
      if (user) {
        try {
          const userRef = ref(database, `users/${user.uid}`);
          const snapshot = await get(userRef);
          let role = null;
          let department = null;
          let selectedDept = null;

          if (snapshot.exists()) {
            role = snapshot.val().role; // 'user', 'admin', or 'super-admin'
            department = snapshot.val().department || null;
            if (role === 'super-admin') {
              selectedDept = 'dept_001';
              department = 'dept_001'; // Align with selectedDepartment
              console.log('AuthProvider: Super-admin detected, setting userDepartment and selectedDepartment to dept_001');
              // Remove department from database for super-admin
              if (snapshot.val().department) {
                await set(userRef, {
                  email: user.email,
                  role: 'super-admin',
                  uid: user.uid,
                });
              }
            } else {
              selectedDept = department;
            }
          } else {
            // Create user entry with 'user' role
            await set(userRef, {
              email: user.email,
              role: 'user',
              uid: user.uid,
            });
            role = 'user';
          }

          setUserRole(role);
          setUserDepartment(department);
          setSelectedDepartment(selectedDept);
          console.log('AuthProvider: Setting user role and department', {
            uid: user.uid,
            role,
            department,
            selectedDepartment: selectedDept,
          });
        } catch (error) {
          console.error('AuthProvider: Error fetching user data:', error);
          toast.error('Failed to load user profile.');
          setUserRole('user');
          setUserDepartment(null);
          setSelectedDepartment(null);
        }
      } else {
        setUserRole(null);
        setUserDepartment(null);
        setSelectedDepartment(null);
      }
      setLoading(false);
    }, (error) => {
      console.error('AuthProvider: Auth state error:', error);
      toast.error('Authentication error.');
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  // Method to update both userDepartment and selectedDepartment for super-admins
  const setDepartment = (deptId) => {
    if (isSuperAdmin()) {
      console.log('AuthProvider: Updating userDepartment and selectedDepartment to', deptId);
      setUserDepartment(deptId);
      setSelectedDepartment(deptId);
    } else {
      console.log('AuthProvider: Non-super-admin, only updating selectedDepartment to', deptId);
      setSelectedDepartment(deptId);
    }
  };

  const isSuperAdmin = () => {
    return userRole === 'super-admin';
  };

  const hasAccessToDepartment = (departmentId) => {
    if (isSuperAdmin()) {
      return selectedDepartment ? selectedDepartment === departmentId : true;
    }
    if (userRole === 'admin' || userRole === 'user') {
      return userDepartment === departmentId;
    }
    return false;
  };

  const hasWriteAccessToDepartment = (departmentId) => {
    if (isSuperAdmin()) {
      return selectedDepartment ? selectedDepartment === departmentId : true;
    }
    if (userRole === 'admin') {
      return userDepartment === departmentId;
    }
    return false;
  };

  const value = {
    currentUser,
    userRole,
    userDepartment,
    selectedDepartment,
    setSelectedDepartment: setDepartment, // Use new method
    loading,
    isSuperAdmin,
    hasAccessToDepartment,
    hasWriteAccessToDepartment,
  };

  return <AuthContext.Provider value={value}>{!loading && children}</AuthContext.Provider>;
};