import React, { useState, useEffect, useCallback, useMemo, Component, useContext } from 'react';
import PropTypes from 'prop-types';
import { auth, database } from '../firebaseConfig';
import { sendPasswordResetEmail, onAuthStateChanged } from 'firebase/auth';
import { ref, set, get, onValue } from 'firebase/database';
import { toast } from 'react-toastify';
import { AuthContext } from './AuthProvider';

// REMINDER: Before moving to production, you MUST:
// 1. Set Firebase Realtime Database rules to secure 'users', 'employees', 'config/maxAdmins', 'telegram_subscribers', and 'whatsapp_subscribers'.
// 2. Create an admin account in the 'users' database with role: 'admin'.
// 3. Deploy 'addUser' and 'deleteUser' Cloud Functions.

// Error Boundary Component
class AdminUserManagerErrorBoundary extends Component {
  state = { hasError: false, error: null };

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-4 bg-red-100 text-red-700 rounded-md">
          <h3 className="font-bold">Something went wrong.</h3>
          <p>{this.state.error?.message || 'An unexpected error occurred.'}</p>
          <p>Please try refreshing the page or contact support.</p>
        </div>
      );
    }
    return this.props.children;
  }
}

const AdminUserManager = ({ employees: propEmployees, isSubmitting, isInitializing }) => {
  const { userDepartment, selectedDepartment, isSuperAdmin, isRegionManager, isSuperUser, isRegionUser } = useContext(AuthContext);

  const [selectedEmployee, setSelectedEmployee] = useState('');
  const [users, setUsers] = useState([]);
  const [maxAdmins, setMaxAdmins] = useState(2);
  const [telegramSubscribers, setTelegramSubscribers] = useState([]);
  const [whatsappSubscribers, setWhatsappSubscribers] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState({});
  const [localEmployees, setLocalEmployees] = useState(propEmployees || []);
  const [isAuthReady, setIsAuthReady] = useState(false);
  const [duplicateEmails, setDuplicateEmails] = useState([]);
  const [duplicateTelegramIds, setDuplicateTelegramIds] = useState([]);
  const [duplicateWhatsappNumbers, setDuplicateWhatsappNumbers] = useState([]);
  const [departmentPath, setDepartmentPath] = useState('');

  // Set department path
  useEffect(() => {
    const deptToUse = isRegionManager || isSuperAdmin() || isSuperUser || isRegionUser ? selectedDepartment : userDepartment;
    if (deptToUse) {
      const path = `departments/${deptToUse}/`;
      setDepartmentPath(path);
    } else {
      setDepartmentPath('');
    }
  }, [userDepartment, selectedDepartment, isRegionManager, isSuperAdmin, isSuperUser, isRegionUser]);

  // Wait for auth state to settle
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (user) {
        console.log('User signed in:', user.uid);
      } else {
        console.warn('No user signed in. Please sign in to test deletion.');
        toast.warn('Sign in to test user management features.');
      }
      setIsAuthReady(true);
    }, (error) => {
      console.error('Auth state error:', error);
      toast.error('Failed to initialize authentication.');
      setIsAuthReady(true);
    });
    return () => unsubscribe();
  }, []);

  // Update localEmployees based on propEmployees
  useEffect(() => {
    console.log('propEmployees received:', propEmployees?.map(emp => ({ employeeId: emp.employeeId, name: emp.name, email: emp.email })));

    if (!propEmployees) {
      console.warn('propEmployees is null or undefined, setting empty array...');
      setLocalEmployees([]);
      return;
    }

    setLocalEmployees(propEmployees);
    console.log('Updated localEmployees:', propEmployees.map(emp => ({ employeeId: emp.employeeId, name: emp.name, email: emp.email })));

    // Detect duplicate emails
    const emailCounts = {};
    propEmployees.forEach(emp => {
      if (emp.email && emp.email !== '') {
        emailCounts[emp.email] = (emailCounts[emp.email] || 0) + 1;
      }
    });
    const duplicates = Object.entries(emailCounts)
      .filter(([_, count]) => count > 1)
      .map(([email]) => email);
    setDuplicateEmails(duplicates);
  }, [propEmployees]);

  // Fetch users, maxAdmins, telegram_subscribers, and whatsapp_subscribers
  useEffect(() => {
    console.log('Fetching data with department path:', departmentPath);

    // Only fetch department-specific data if we have a department path
    if (!departmentPath) {
      console.log('Waiting for department path to be set before fetching data');
      return () => {}; // Return empty cleanup function
    }

    const usersRef = ref(database, 'users');
    const unsubscribeUsers = onValue(
      usersRef,
      (snapshot) => {
        const data = snapshot.val();
        const userList = data
          ? Object.entries(data)
              .map(([uid, user]) => ({
                uid,
                email: user.email || 'Unknown',
                role: user.role || 'user',
              }))
              .filter((user) => user.uid && user.email !== 'Unknown')
          : [];
        if (data && userList.length !== Object.keys(data).length) {
          console.warn('Filtered out invalid user entries:', {
            totalEntries: Object.keys(data).length,
            validUsers: userList.length,
            invalidEntries: Object.entries(data).filter(
              ([_, user]) => !user.uid || !user.email
            ),
          });
        }
        console.log('Processed users:', userList);
        setUsers(userList);
      },
      (error) => {
        console.error('Error fetching users:', error);
        toast.error('Failed to load users.');
      }
    );

    const maxAdminsRef = ref(database, `${departmentPath}config/maxAdmins`);
    get(maxAdminsRef)
      .then((snapshot) => {
        if (snapshot.exists()) {
          console.log('Found maxAdmins in department config:', snapshot.val());
          setMaxAdmins(snapshot.val());
        } else {
          console.warn(`maxAdmins not set in department ${userDepartment}. Using default: 2`);
          toast.warn('Admin limit not configured. Contact support.');
        }
      })
      .catch((error) => {
        console.error('Error fetching maxAdmins:', error);
        toast.error('Failed to load admin limit.');
      });

    const telegramSubscribersRef = ref(database, `${departmentPath}telegram_subscribers`);
    const unsubscribeTelegram = onValue(
      telegramSubscribersRef,
      (snapshot) => {
        const data = snapshot.val();
        console.log('Telegram subscribers data:', data);
        const subscriberList = data
          ? Object.entries(data).map(([id, details]) => ({
              id,
              status: details.status || 'unknown',
              subscribed: details.subscribed || false,
            }))
          : [];
        setTelegramSubscribers(subscriberList);
        // Detect duplicate Telegram IDs
        const idCounts = {};
        subscriberList.forEach(sub => {
          idCounts[sub.id] = (idCounts[sub.id] || 0) + 1;
        });
        const duplicateIds = Object.entries(idCounts)
          .filter(([_, count]) => count > 1)
          .map(([id]) => id);
        setDuplicateTelegramIds(duplicateIds);
      },
      (error) => {
        console.error('Error fetching Telegram subscribers:', error);
        toast.error('Failed to load Telegram subscribers.');
      }
    );

    const whatsappSubscribersRef = ref(database, `${departmentPath}whatsapp_subscribers`);
    const unsubscribeWhatsapp = onValue(
      whatsappSubscribersRef,
      (snapshot) => {
        const data = snapshot.val();
        console.log('WhatsApp subscribers data:', data);
        const subscriberList = data
          ? Object.entries(data).map(([number, details]) => ({
              number,
              status: details.status || 'unknown',
              subscribed: details.subscribed || false,
            }))
          : [];
        setWhatsappSubscribers(subscriberList);
        // Detect duplicate WhatsApp numbers
        const numberCounts = {};
        subscriberList.forEach(sub => {
          numberCounts[sub.number] = (numberCounts[sub.number] || 0) + 1;
        });
        const duplicateNumbers = Object.entries(numberCounts)
          .filter(([_, count]) => count > 1)
          .map(([number]) => number);
        setDuplicateWhatsappNumbers(duplicateNumbers);
      },
      (error) => {
        console.error('Error fetching WhatsApp subscribers:', error);
        toast.error('Failed to load WhatsApp subscribers.');
      }
    );

    return () => {
      unsubscribeUsers();
      unsubscribeTelegram();
      unsubscribeWhatsapp();
    };
  }, [departmentPath]);

  // Map employees to select options
  const employeeOptions = useMemo(() => {
    console.log('Recalculating employeeOptions:', localEmployees.map(emp => ({ employeeId: emp.employeeId, name: emp.name, email: emp.email })));
    return localEmployees
      .filter((emp) => emp && emp.email && emp.email !== '')
      .map((emp) => ({
        employeeId: emp.employeeId,
        name: emp.name,
        email: emp.email,
      }))
      .sort((a, b) => a.name.localeCompare(b.name));
  }, [localEmployees]);

  const selectedEmail = employeeOptions.find((opt) => opt.name === selectedEmployee)?.email || '';

  const handleAddUser = async () => {
    if (isSubmitting || isInitializing || isLoading || !selectedEmail) {
      if (!selectedEmail) toast.error('Please select an employee with a valid email.');
      return;
    }

    setIsLoading(true);
    try {
      const token = await auth.currentUser.getIdToken();
      const addUserUrl = process.env.ADD_USER_FUNCTION_URL;

      const response = await fetch(addUserUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: selectedEmail,
          displayName: selectedEmployee,
        }),
      });

      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.error || 'Failed to add user');
      }

      // Set the user's department to the current department being managed
      const currentDept = isRegionManager || isSuperAdmin() || isSuperUser || isRegionUser ? selectedDepartment : userDepartment;
      if (currentDept && result.uid) {
        await set(ref(database, `users/${result.uid}/department`), currentDept);
      }

      await sendPasswordResetEmail(auth, selectedEmail);
      toast.success(`User created for ${selectedEmployee}. A password reset email has been sent.`);
      setSelectedEmployee('');
    } catch (error) {
      if (error.message.includes('email-already-in-use')) {
        toast.error('This email is already registered. Please delete the existing account or contact support.');
      } else if (error.message.includes('too-many-requests')) {
        toast.error('Too many attempts. Please try again later.');
      } else {
        toast.error(`Failed to create user: ${error.message}`);
      }
      console.error('Error creating user:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRoleChange = useCallback(
    async (uid, currentRole) => {
      if (isSubmitting || isInitializing || actionLoading[uid]) {
        return;
      }

      // Cycle through roles: user → admin → region-manager → super-user → region-user → user
      let newRole;
      switch (currentRole) {
        case 'user':
          newRole = 'admin';
          break;
        case 'admin':
          newRole = 'region-manager';
          break;
        case 'region-manager':
          newRole = 'super-user';
          break;
        case 'super-user':
          newRole = 'region-user';
          break;
        case 'region-user':
          newRole = 'user';
          break;
        default:
          newRole = 'user';
      }

      // Check admin limit only when promoting to admin
      if (newRole === 'admin') {
        const currentAdmins = users.filter((u) => u.role === 'admin').length;
        if (currentAdmins >= maxAdmins) {
          toast.error(`Cannot assign more admins. Maximum allowed: ${maxAdmins}.`);
          return;
        }
      }

      if (!window.confirm(`Change role for ${users.find((u) => u.uid === uid)?.email || 'Unknown'} to ${newRole}?`)) return;

      setActionLoading((prev) => ({ ...prev, [uid]: true }));
      try {
        await set(ref(database, `users/${uid}/role`), newRole);

        // If changing to region-manager or region-user, we might need to set managedDepartments
        if (newRole === 'region-manager' || newRole === 'region-user') {
          // For now, set empty array - this should be configured separately
          await set(ref(database, `users/${uid}/managedDepartments`), []);
          toast.success(`Role changed to ${newRole}. Please configure managed departments separately.`);
        } else {
          // Remove managedDepartments if not region-manager or region-user
          if (currentRole === 'region-manager' || currentRole === 'region-user') {
            await set(ref(database, `users/${uid}/managedDepartments`), null);
          }
          toast.success(`Role changed to ${newRole}.`);
        }
      } catch (error) {
        toast.error('Failed to change role.');
        console.error('Error changing role:', error);
      } finally {
        setActionLoading((prev) => ({ ...prev, [uid]: false }));
      }
    },
    [users, maxAdmins, isSubmitting, isInitializing, actionLoading]
  );

  const handleResetPassword = useCallback(
    async (email, uid) => {
      if (isSubmitting || isInitializing || actionLoading[uid]) {
        return;
      }

      if (!window.confirm(`Send password reset email to ${email}?`)) return;

      setActionLoading((prev) => ({ ...prev, [uid]: true }));
      try {
        await sendPasswordResetEmail(auth, email);
        toast.success(`Password reset email sent to ${email}.`);
      } catch (error) {
        if (error.code === 'auth/too-many-requests') {
          toast.error('Too many attempts. Please try again later.');
        } else {
          toast.error('Failed to send password reset email.');
        }
        console.error('Error sending password reset:', error);
      } finally {
        setActionLoading((prev) => ({ ...prev, [uid]: false }));
      }
    },
    [isSubmitting, isInitializing, actionLoading]
  );

  const handleDeleteUser = useCallback(
    async (uid, email) => {
      if (isSubmitting || isInitializing || actionLoading[uid]) {
        return;
      }

      if (!uid) {
        toast.error('Cannot delete user: Invalid user ID.');
        console.error('Invalid UID in handleDeleteUser:', { uid, email });
        return;
      }

      const deleteUrl = `${process.env.DELETE_USER_FUNCTION_URL}?uid=${uid}`;

      // Sending DELETE request for user
      if (!window.confirm(`Delete user ${email}? This cannot be undone.`)) return;

      setActionLoading((prev) => ({ ...prev, [uid]: true }));
      try {
        const token = await auth.currentUser.getIdToken();
        const response = await fetch(deleteUrl, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        const result = await response.json();
        if (!response.ok) {
          throw new Error(result.error || 'Failed to delete user');
        }

        console.log('Delete user response:', result);
        toast.success(`User ${email} deleted.`);
      } catch (error) {
        if (error.message.includes('Failed to fetch')) {
          toast.error('Failed to delete user: Network or CORS error. Check server configuration.');
        } else {
          toast.error(`Failed to delete user: ${error.message}`);
        }
        console.error('Error deleting user:', error, { uid, email, deleteUrl });
      } finally {
        setActionLoading((prev) => ({ ...prev, [uid]: false }));
      }
    },
    [isSubmitting, isInitializing, actionLoading]
  );

  const handleToggleSubscription = useCallback(
    async (type, id, currentSubscribed) => {
      if (isSubmitting || isInitializing || actionLoading[id]) {
        return;
      }

      const newSubscribed = !currentSubscribed;
      const path = type === 'telegram' ? `${departmentPath}telegram_subscribers/${id}` : `${departmentPath}whatsapp_subscribers/${id}`;
      const displayId = type === 'telegram' ? id : id.replace(/"/g, '');

      if (!window.confirm(`${newSubscribed ? 'Resubscribe' : 'Unsubscribe'} ${displayId} from ${type} notifications?`)) return;

      setActionLoading((prev) => ({ ...prev, [id]: true }));
      try {
        await set(ref(database, path), {
          status: 'active',
          subscribed: newSubscribed,
        });
        toast.success(`${displayId} ${newSubscribed ? 'resubscribed to' : 'unsubscribed from'} ${type} notifications.`);
      } catch (error) {
        toast.error(`Failed to update ${type} subscription for ${displayId}.`);
        console.error(`Error updating ${type} subscription:`, error);
      } finally {
        setActionLoading((prev) => ({ ...prev, [id]: false }));
      }
    },
    [isSubmitting, isInitializing, actionLoading, departmentPath]
  );

  if (!isAuthReady) {
    return (
      <div className="mb-8">
        <h2 className="text-xl font-bold mb-4">Admin User Manager</h2>
        <p className="text-gray-500">Initializing authentication...</p>
      </div>
    );
  }

  return (
    <AdminUserManagerErrorBoundary>
      <div className="mb-8">
        <h2 className="text-xl font-bold mb-4">Admin User Manager</h2>
        <div className="mb-4">
          <p className="text-sm text-gray-700">
            Maximum Admins Allowed: {maxAdmins} (Current Admins: {users.filter((u) => u.role === 'admin').length})
          </p>
          {duplicateEmails.length > 0 && (
            <p className="text-sm text-red-600 mt-2">
              Warning: Duplicate emails detected: {duplicateEmails.join(', ')}. Please ensure unique emails for each employee.
            </p>
          )}
          {duplicateTelegramIds.length > 0 && (
            <p className="text-sm text-red-600 mt-2">
              Warning: Duplicate Telegram IDs detected: {duplicateTelegramIds.join(', ')}. Please ensure unique IDs.
            </p>
          )}
          {duplicateWhatsappNumbers.length > 0 && (
            <p className="text-sm text-red-600 mt-2">
              Warning: Duplicate WhatsApp numbers detected: {duplicateWhatsappNumbers.map(num => num.replace(/"/g, '')).join(', ')}. Please ensure unique numbers.
            </p>
          )}
        </div>
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <select
            value={selectedEmployee}
            onChange={(e) => setSelectedEmployee(e.target.value)}
            className="p-2 border rounded-md w-64"
            disabled={isSubmitting || isInitializing || isLoading}
            aria-label="Select employee to add as user"
          >
            <option value="">Select Employee</option>
            {employeeOptions.map((emp) => (
              <option key={emp.employeeId} value={emp.name}>
                {emp.name} ({emp.email})
              </option>
            ))}
          </select>
          <button
            onClick={handleAddUser}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400"
            disabled={isSubmitting || isInitializing || isLoading || !selectedEmail}
            aria-label="Add selected employee as user"
          >
            {isLoading ? 'Adding...' : 'Add User'}
          </button>
        </div>
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-2">Users</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border border-gray-300 p-2 text-left">Email</th>
                  <th className="border border-gray-300 p-2 text-left">Role</th>
                  <th className="border border-gray-300 p-2 text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {users.map((user) => (
                  <tr key={user.uid} className="hover:bg-gray-50">
                    <td className="border border-gray-300 p-2">{user.email}</td>
                    <td className="border border-gray-300 p-2">{user.role}</td>
                    <td className="border border-gray-300 p-2 flex gap-2">
                      <button
                        onClick={() => handleRoleChange(user.uid, user.role)}
                        className="text-blue-600 hover:underline disabled:text-gray-400"
                        disabled={isSubmitting || isInitializing || actionLoading[user.uid]}
                        aria-label={`Change role for ${user.email}`}
                      >
                        {actionLoading[user.uid] ? 'Updating...' : `→ ${
                          user.role === 'user' ? 'Admin' :
                          user.role === 'admin' ? 'R-Mgr' :
                          user.role === 'region-manager' ? 'S-User' :
                          user.role === 'super-user' ? 'R-User' :
                          'User'
                        }`}
                      </button>
                      <button
                        onClick={() => handleResetPassword(user.email, user.uid)}
                        className="text-green-600 hover:underline disabled:text-gray-400"
                        disabled={isSubmitting || isInitializing || actionLoading[user.uid]}
                        aria-label={`Send password reset email to ${user.email}`}
                      >
                        {actionLoading[user.uid] ? 'Sending...' : 'Reset Password'}
                      </button>
                      <button
                        onClick={() => handleDeleteUser(user.uid, user.email)}
                        className="text-red-600 hover:underline disabled:text-gray-400"
                        disabled={isSubmitting || isInitializing || actionLoading[user.uid]}
                        aria-label={`Delete user ${user.email}`}
                      >
                        {actionLoading[user.uid] ? 'Deleting...' : 'Delete'}
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            {users.length === 0 && <p className="text-gray-500 mt-2">No users found.</p>}
          </div>
        </div>
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-2">Telegram Subscribers</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border border-gray-300 p-2 text-left">Telegram ID</th>
                  <th className="border border-gray-300 p-2 text-left">Status</th>
                  <th className="border border-gray-300 p-2 text-left">Subscribed</th>
                  <th className="border border-gray-300 p-2 text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {telegramSubscribers.map((sub) => (
                  <tr key={sub.id} className="hover:bg-gray-50">
                    <td className="border border-gray-300 p-2">{sub.id}</td>
                    <td className="border border-gray-300 p-2">{sub.status}</td>
                    <td className="border border-gray-300 p-2">{sub.subscribed ? 'Yes' : 'No'}</td>
                    <td className="border border-gray-300 p-2">
                      <button
                        onClick={() => handleToggleSubscription('telegram', sub.id, sub.subscribed)}
                        className="text-blue-600 hover:underline disabled:text-gray-400"
                        disabled={isSubmitting || isInitializing || actionLoading[sub.id]}
                        aria-label={`${sub.subscribed ? 'Unsubscribe' : 'Resubscribe'} Telegram ID ${sub.id}`}
                      >
                        {actionLoading[sub.id] ? 'Updating...' : sub.subscribed ? 'Unsubscribe' : 'Resubscribe'}
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            {telegramSubscribers.length === 0 && <p className="text-gray-500 mt-2">No Telegram subscribers found.</p>}
          </div>
        </div>
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-2">WhatsApp Subscribe</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border border-gray-300 p-2 text-left">Phone Number</th>
                  <th className="border border-gray-300 p-2 text-left">Status</th>
                  <th className="border border-gray-300 p-2 text-left">Subscribed</th>
                  <th className="border border-gray-300 p-2 text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {whatsappSubscribers.map((sub) => (
                  <tr key={sub.number} className="hover:bg-gray-50">
                    <td className="border border-gray-300 p-2">{sub.number.replace(/"/g, '')}</td>
                    <td className="border border-gray-300 p-2">{sub.status}</td>
                    <td className="border border-gray-300 p-2">{sub.subscribed ? 'Yes' : 'No'}</td>
                    <td className="border border-gray-300 p-2">
                      <button
                        onClick={() => handleToggleSubscription('whatsapp', sub.number, sub.subscribed)}
                        className="text-blue-600 hover:underline disabled:text-gray-400"
                        disabled={isSubmitting || isInitializing || actionLoading[sub.number]}
                        aria-label={`${sub.subscribed ? 'Unsubscribe' : 'Resubscribe'} WhatsApp number ${sub.number.replace(/"/g, '')}`}
                      >
                        {actionLoading[sub.number] ? 'Updating...' : sub.subscribed ? 'Unsubscribe' : 'Resubscribe'}
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            {whatsappSubscribers.length === 0 && <p className="text-gray-500 mt-2">No WhatsApp subscribers found.</p>}
          </div>
        </div>
      </div>
    </AdminUserManagerErrorBoundary>
  );
};

AdminUserManager.propTypes = {
  employees: PropTypes.arrayOf(
    PropTypes.shape({
      employeeId: PropTypes.string.isRequired,
      rowIndex: PropTypes.number,
      name: PropTypes.string,
      srcNumber: PropTypes.string,
      position: PropTypes.string,
      phone: PropTypes.string,
      email: PropTypes.string,
      createdAt: PropTypes.string,
      updatedAt: PropTypes.string,
    })
  ),
  isSubmitting: PropTypes.bool.isRequired,
  isInitializing: PropTypes.bool.isRequired,
};

export default React.memo(AdminUserManager);