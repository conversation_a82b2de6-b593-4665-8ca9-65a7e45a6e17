// src\contexts\actions\shiftActions.js
import { getDatabase, ref, set, get,remove} from 'firebase/database';
import { getAuth } from 'firebase/auth';
import {
  FETCH_SHIFTS_START,
  FETCH_SHIFTS_SUCCESS,
  FETCH_SHIFTS_ERROR,
  ADD_SHIFT,
  UPDATE_SHIFT,
  DELETE_SHIFT
} from '../DataContext';
import getDepartmentPath from '../../utils/getDepartmentPath';
import dataCache from '../../utils/cacheUtils';

// ===== SHIFT ACTIONS =====


// Fetch shifts data with caching

export const fetchShiftsData = async (dispatch, forceRefresh = false, selectedDepartment = null) => {
  dispatch({ type: FETCH_SHIFTS_START });

  const cacheKey = `shifts_data_${selectedDepartment || 'default'}`;

  // Check cache first if not forcing a refresh
  if (!forceRefresh) {
    const cachedData = dataCache.get(cacheKey);
    if (cachedData) {
      console.log('Using cached shifts data');
      dispatch({ type: FETCH_SHIFTS_SUCCESS, payload: cachedData });
      return;
    }
  }

  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);

    const db = getDatabase();
    const shiftsRef = ref(db, `${departmentPath}shifts`);

    get(shiftsRef)
      .then((snapshot) => {
        if (snapshot.exists()) {
          const data = snapshot.val();

          // Extract shift types, hours, colors, etc.
          const types = Object.keys(data);
          const hours = {};
          const colors = {};
          const startTimes = {};
          const endTimes = {};

          Object.entries(data).forEach(([key, value]) => {
            hours[key] = value.hours || 0;
            colors[key] = value.color || '#D3D3D3'; // Default gray if color missing
            startTimes[key] = value.startTime || '';
            endTimes[key] = value.endTime || '';
          });

          const payload = { data, types, hours, colors, startTimes, endTimes };

          // Cache the data for future use (1 hour TTL)
          dataCache.set(cacheKey, payload, 60 * 60 * 1000);

          dispatch({ type: FETCH_SHIFTS_SUCCESS, payload });
        } else {
          const emptyPayload = { data: {}, types: [], hours: {}, colors: {}, startTimes: {}, endTimes: {} };
          dispatch({ type: FETCH_SHIFTS_SUCCESS, payload: emptyPayload });
        }
      })
      .catch((err) => {
        dispatch({
          type: FETCH_SHIFTS_ERROR,
          payload: new Error('Failed to fetch shifts data: ' + err.message)
        });
      });
  } catch (err) {
    console.error('Error getting department path:', err);
    dispatch({
      type: FETCH_SHIFTS_ERROR,
      payload: new Error('Failed to get department path: ' + err.message)
    });
  }
};

// Add or update shift with optimistic update
export const updateShift = async (dispatch, shiftName, shiftData, selectedDepartment = null) => {
  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);
    const cacheKey = `shifts_data_${selectedDepartment || 'default'}`;

    // Optimistically update the UI immediately
    dispatch({ type: UPDATE_SHIFT, payload: { name: shiftName, data: shiftData } });

    // Invalidate the cache
    dataCache.delete(cacheKey);

    // Perform the actual database update
    const db = getDatabase();
    await set(ref(db, `${departmentPath}shifts/${shiftName}`), shiftData);

    return true;
  } catch (err) {
    console.error('Error updating shift:', err);

    // Fetch fresh data to revert the optimistic update
    fetchShiftsData(dispatch, true, selectedDepartment);

    return false;
  }
};

// Delete shift with optimistic update
export const deleteShift = async (dispatch, shiftName, selectedDepartment = null) => {
  try {
    const departmentPath = await getDepartmentPath(true, selectedDepartment);
    const cacheKey = `shifts_data_${selectedDepartment || 'default'}`;

    // Optimistically update the UI immediately
    dispatch({ type: DELETE_SHIFT, payload: shiftName });

    // Invalidate the cache
    dataCache.delete(cacheKey);

    // Perform the actual database update
    const db = getDatabase();
    await remove(ref(db, `${departmentPath}shifts/${shiftName}`));

    return true;
  } catch (err) {
    console.error('Error deleting shift:', err);

    // Fetch fresh data to revert the optimistic update
    fetchShiftsData(dispatch, true, selectedDepartment);

    return false;
  }
};