import React, { useState, useEffect, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { AuthContext } from './AuthProvider';
import { auth } from '../firebaseConfig';
import { signInWithEmailAndPassword, sendPasswordResetEmail } from 'firebase/auth';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { toast } from 'react-toastify';import './LoginPage.css';

// Define allowed email domains
const ALLOWED_DOMAINS = ['gmail.com', 'example.com']; // Replace with your domains

// Validation schema
const loginSchema = yup.object().shape({
  email: yup
    .string()
    .email('Invalid email format')
    .required('Email is required')
    .test(
      'is-allowed-domain',
      `Email domain must be one of: ${ALLOWED_DOMAINS.join(', ')}`,
      (value) => {
        if (!value) return true;
        const domain = value.split('@')[1];
        return domain && ALLOWED_DOMAINS.includes(domain);
      }
    ),
  password: yup
    .string()
    .required('Password is required')
    .min(6, 'Password must be at least 6 characters'),
});

const LoginPage = () => {
  const { currentUser, userRole } = useContext(AuthContext);
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(loginSchema),
  });
  const [firebaseError, setFirebaseError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  // Redirect after login
  useEffect(() => {
    if (currentUser) {
      console.log('LoginPage: Redirecting user', { uid: currentUser.uid, userRole });
      const redirectPath = userRole === 'admin' ? '/employees' : '/';
      navigate(redirectPath, { replace: true });
    }
  }, [currentUser, userRole, navigate]);

  const onSubmit = async (data) => {
    setFirebaseError('');
    setIsLoading(true);
    try {
      await signInWithEmailAndPassword(auth, data.email, data.password);
      toast.success('Logged in successfully!');
    } catch (error) {
      console.error('Login error:', error.code, error.message);
      let errorMessage = 'Invalid email or password.';
      if (error.code === 'auth/too-many-requests') {
        errorMessage = 'Too many attempts. Please try again later.';
      } else if (error.code === 'auth/user-not-found' || error.code === 'auth/wrong-password') {
        errorMessage = 'Invalid email or password.';
      }
      setFirebaseError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePasswordReset = async () => {
    const email = prompt('Enter your email address:');
    if (!email) return;
    if (!ALLOWED_DOMAINS.some((domain) => email.endsWith(`@${domain}`))) {
      toast.error(`Email must be from: ${ALLOWED_DOMAINS.join(', ')}`);
      return;
    }
    setIsLoading(true);
    try {
      await sendPasswordResetEmail(auth, email);
      toast.success('Password reset email sent!');
    } catch (error) {
      console.error('Password reset error:', error.code, error.message);
      toast.error('Failed to send password reset email.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen w-screen flex items-center justify-center bg-gradient-to-br from-gray-200 to-gray-300 login-bg">
      <div className="max-w-md w-full mx-auto p-8 bg-gray-50 rounded-lg shadow-xl animate-fade-in -mt-8">
        <h1 className="text-3xl font-bold text-gray-800 text-center mb-6 font-poppins">
          Human Resource Management
        </h1>
        {firebaseError && <p className="text-red-500 mb-4 text-center">{firebaseError}</p>}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700">
              Email
            </label>
            <input
              type="email"
              id="email"
              className="mt-1 p-3 w-full border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200"
              {...register('email')}
              disabled={isLoading}
            />
            {errors.email && <p className="text-orange-500 text-sm mt-1">{errors.email.message}</p>}
          </div>
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700">
              Password
            </label>
            <input
              type="password"
              id="password"
              className="mt-1 p-3 w-full border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200"
              {...register('password')}
              disabled={isLoading}
            />
            {errors.password && (
              <p className="text-orange-500 text-sm mt-1">{errors.password.message}</p>
            )}
          </div>
          <button
            type="submit"
            className="w-full py-3 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 transition duration-200"
            disabled={isLoading}
          >
            {isLoading ? 'Logging in...' : 'Log In'}
          </button>
        </form>
        <div className="mt-4 text-center">
          <button
            onClick={handlePasswordReset}
            className="text-blue-600 hover:underline disabled:text-gray-400 text-sm"
            disabled={isLoading}
          >
            Forgot Password?
          </button>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;