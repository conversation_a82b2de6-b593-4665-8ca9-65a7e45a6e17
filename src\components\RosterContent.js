// src/components/RosterContent.js
import React from 'react';
import RosterTable from '../RosterTable';
import ShiftSummaryTable from './ShiftSummaryTable';
import ShiftLegend from './ShiftLegend';
import MarkerLegend from './MarkerLegend';
import RosterAdditionalInfo from './RosterAdditionalInfo';

/**
 * RosterContent component - Main roster display area
 *
 * @param {Object} props - Component props
 * @param {Array} props.localRosterData - Current roster data
 * @param {Function} props.setLocalRosterData - Function to update roster data
 * @param {Object} props.shiftTypes - Shift types configuration
 * @param {Object} props.shiftHours - Shift hours configuration
 * @param {Object} props.shiftColors - Shift colors configuration
 * @param {Array} props.daysOfWeek - Days of the week for the current month
 * @param {Function} props.formatHoursMinutes - Function to format hours and minutes
 * @param {number} props.year - Current year
 * @param {number} props.month - Current month
 * @param {Object} props.weekdayMarkers - Weekday markers configuration
 * @param {Object} props.holidayMarkers - Holiday markers configuration
 * @param {Object} props.specialDayMarkers - Special day markers configuration
 * @param {Object} props.shiftSummary - Shift summary data
 * @param {Object} props.shiftTotals - Shift totals data
 * @param {number} props.daysInMonth - Number of days in the current month
 * @param {Object} props.shiftStartTimes - Shift start times configuration
 * @param {Object} props.shiftEndTimes - Shift end times configuration
 * @param {Array} props.orderedContactInfo - Ordered contact information
 * @param {boolean} props.isRosterDraft - Whether the current roster is a draft
 * @param {Function} props.changeYear - Function to change year
 * @param {Function} props.changeMonth - Function to change month
 * @returns {JSX.Element} RosterContent component
 */
const RosterContent = ({
  localRosterData,
  setLocalRosterData,
  shiftTypes,
  shiftHours,
  shiftColors,
  daysOfWeek,
  formatHoursMinutes,
  year,
  month,
  weekdayMarkers,
  holidayMarkers,
  specialDayMarkers,
  shiftSummary,
  shiftTotals,
  daysInMonth,
  shiftStartTimes,
  shiftEndTimes,
  orderedContactInfo,
  isRosterDraft,
  changeYear,
  changeMonth,
}) => {
  return (
    <>
      <RosterTable
        rosterData={localRosterData}
        setRosterData={isRosterDraft ? setLocalRosterData : () => {}}
        shiftTypes={shiftTypes}
        shiftHours={shiftHours}
        shiftColors={shiftColors}
        daysOfWeek={daysOfWeek}
        formatHoursMinutes={formatHoursMinutes}
        year={year}
        month={month}
        weekdayMarkers={weekdayMarkers}
        holidayMarkers={holidayMarkers}
        specialDayMarkers={specialDayMarkers}
        isRosterDraft={isRosterDraft}
        changeYear={changeYear}
        changeMonth={changeMonth}
      />
      <ShiftSummaryTable
        shiftSummary={shiftSummary}
        shiftTotals={shiftTotals}
        daysInMonth={daysInMonth}
      />
      <div
        style={{
          display: 'flex',
          flexWrap: 'wrap',
          gap: '1rem',
          maxWidth: '1500px',
          marginBottom: '2rem',
        }}
      >
        <div style={{ flex: '1 1 300px' }}>
          <ShiftLegend shiftHours={shiftHours} shiftColors={shiftColors} />
        </div>
        <div style={{ flex: '1 1 300px' }}>
          <MarkerLegend
            weekdayMarkers={weekdayMarkers}
            holidayMarkers={holidayMarkers}
            specialDayMarkers={specialDayMarkers}
            year={year}
            month={month}
          />
        </div>
      </div>
      <RosterAdditionalInfo
        shiftHours={shiftHours}
        shiftStartTimes={shiftStartTimes}
        shiftEndTimes={shiftEndTimes}
        orderedContactInfo={orderedContactInfo}
      />
    </>
  );
};

export default RosterContent;
